import { sensors } from '@app/catalogs/sensors';
import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { getClass } from '@app/lib/get-class';
import { getEl, hideEl, showEl } from '@app/lib/get-el';
import { CameraType } from '@app/singletons/camera';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { PersistenceManager, StorageKey } from '@app/singletons/persistence-manager';
import sensorPng from '@public/img/icons/sensor.png';
import { BaseObject, DetailedSatellite, DetailedSensor, ZoomValue } from 'ootk';
import { SensorGroup, sensorGroups } from '../../catalogs/sensor-groups';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';
import { DateTimeManager } from '../date-time-manager/date-time-manager';
import { SatInfoBox } from '../select-sat-manager/sat-info-box';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';
import { SoundNames } from '../sounds/SoundNames';
import { InputEventType, keepTrackApi } from './../../keepTrackApi';
import './sensor-list.css';

// TODO: Add a search bar and filter for sensors

export class SensorListPlugin extends KeepTrackPlugin {
  readonly id = 'SensorListPlugin';
  dependencies_: string[] = [DateTimeManager.name];
  private readonly sensorGroups_: SensorGroup[] = sensorGroups;

  bottomIconCallback: () => void = () => {
    if (this.isMenuButtonActive) {
      // Force update custom sensors list when menu is opened
      setTimeout(() => {
        SensorListPlugin.updateCustomSensorsList();
      }, 100);

      if (keepTrackApi.getPluginByName('Planetarium')?.isMenuButtonActive) {
        getClass('sensor-top-link').forEach((el) => {
          el.style.display = 'none';
        });
      } else {
        getClass('sensor-top-link').forEach((el) => {
          el.style.gridTemplateColumns = 'repeat(2,1fr)';
          el.style.display = '';
        });
      }
    }
  };

  dragOptions: ClickDragOptions = {
    isDraggable: true,
    minWidth: 550,
    maxWidth: 800,
  };

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = sensorPng;

  sideMenuElementName: string = 'sensor-list-menu';
  sideMenuElementHtml: string =
    keepTrackApi.html`
    <div id="sensor-list-menu" class="side-menu-parent start-hidden text-select">
        <div id="sensor-list-content" class="side-menu">
        <div class="row">
          <ul id="reset-sensor-text" class="sensor-reset-menu">
            <button id="reset-sensor-button" class="center-align btn btn-ui waves-effect waves-light menu-selectable" type="button" disabled>重置传感器 &#9658;</button>
          </ul>
          <ul id="list-of-sensors">
            <div id="custom-sensors-group" class="start-hidden">
              ${SensorListPlugin.genH5Title_('自定义传感器')}
              <div id="custom-sensors-list"></div>
            </div>` +
    this.sensorGroups_.map((sensorGroup) => this.genericSensors_(sensorGroup.name)).join('') +
    keepTrackApi.html`
          </ul>
        </div>
      </div>
    </div>`;

  isSensorLinksAdded = false;

  addHtml(): void {
    super.addHtml();

    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerInit,
      () => {
        // 延迟执行以确保DateTimeManager已经创建了nav-mobile
        setTimeout(() => {
          const navMobile = getEl('nav-mobile');
          if (navMobile) {
            navMobile.insertAdjacentHTML(
              'beforeend',
              keepTrackApi.html`
              <div id="sensor-selected-container" class="start-hidden">
                <div id="sensor-selected" class="waves-effect waves-light">

                </div>
              </div>
              `,
            );
          }
        }, 100); // 比DateTimeManager的50ms延迟更长
      },
    );
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl('sensor-selected-container')?.addEventListener('click', () => {
          keepTrackApi.emit(KeepTrackApiEvents.bottomMenuClick, this.bottomIconElementName);
          keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
        });

        getEl('sensor-list-content')?.addEventListener('click', (e: Event) => {
          let realTarget = e.target as HTMLElement | null | undefined;

          if (!realTarget?.classList.contains('menu-selectable')) {
            realTarget = realTarget?.closest('.menu-selectable');
            if (!realTarget?.classList.contains('menu-selectable')) {
              return;
            }
          }

          if (realTarget.id === 'reset-sensor-button') {
            keepTrackApi.getSensorManager().resetSensorSelected();
            keepTrackApi.getSoundManager().play(SoundNames.MENU_BUTTON);

            return;
          }

          keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
          const sensorClick = realTarget.dataset.sensor;

          this.sensorListContentClick(sensorClick ?? '');
        });
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.selectSatData,
      (obj: BaseObject) => {
        // Skip this if there is no satellite object because the menu isn't open
        if (!obj?.isSatellite()) {
          hideEl('sensors-in-fov-link');

          return;
        }

        showEl('sensors-in-fov-link');

        if (keepTrackApi.getPlugin(SatInfoBox) !== null && !this.isSensorLinksAdded) {
          getEl('actions-section')?.insertAdjacentHTML(
            'beforeend',
            keepTrackApi.html`
                  <div id="sensors-in-fov-link" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
                        data-tooltip="可视化传感器覆盖范围">可视化传感器覆盖范围</div>
                `,
          );
          getEl('sensors-in-fov-link')?.addEventListener('click', () => {
            keepTrackApi.getSoundManager().play(SoundNames.CLICK);

            const selectSatManagerInstance = keepTrackApi.getPlugin(SelectSatManager);

            if (!selectSatManagerInstance) {
              return;
            }

            const sat = selectSatManagerInstance.getSelectedSat();

            if (!sat.isSatellite()) {
              return;
            }

            keepTrackApi.getLineManager().createSensorsToSatFovOnly(sat as DetailedSatellite);
          });
          this.isSensorLinksAdded = true;
        }
      },
    );
  }

  addJs(): void {
    super.addJs();

    // 初始化时更新自定义传感器列表
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => {
      // 延迟一点时间确保所有插件都已加载
      setTimeout(() => {
        SensorListPlugin.updateCustomSensorsList();
      }, 100);
    });

    // 监听传感器变化事件，但不频繁更新列表（只在传感器数量变化时更新）
    let lastCustomSensorCount = 0;

    const updateIfNeeded = () => {
      const currentCount = keepTrackApi.getSensorManager().getAllActiveSensors().filter(sensor =>
        sensor.objName?.startsWith('Custom Sensor')
      ).length;

      if (currentCount !== lastCustomSensorCount) {
        lastCustomSensorCount = currentCount;
        SensorListPlugin.updateCustomSensorsList();
      }
    };

    keepTrackApi.on(KeepTrackApiEvents.setSensor, () => {
      // 延迟更新，确保传感器状态已经完全更新
      setTimeout(updateIfNeeded, 100);
    });

    // 监听重置传感器事件，更新自定义传感器列表显示
    keepTrackApi.on(KeepTrackApiEvents.resetSensor, () => {
      // 延迟更新，确保传感器状态已经完全更新
      setTimeout(updateIfNeeded, 100);
    });

    keepTrackApi.on(
      KeepTrackApiEvents.sensorDotSelected,
      (obj: BaseObject) => {
        if (settingsManager.isMobileModeEnabled) {
          return;
        }
        if (!obj.isSensor()) {
          return;
        }
        const sensor = obj as DetailedSensor;

        const sensorManagerInstance = keepTrackApi.getSensorManager();
        // No sensor manager on mobile

        sensorManagerInstance.setSensor(null, sensor.sensorId);

        if (sensorManagerInstance.currentSensors.length === 0) {
          throw new Error('No sensors found');
        }
        const timeManagerInstance = keepTrackApi.getTimeManager();

        keepTrackApi
          .getMainCamera()
          .lookAtLatLon(
            sensorManagerInstance.currentSensors[0].lat,
            sensorManagerInstance.currentSensors[0].lon,
            sensorManagerInstance.currentSensors[0].zoom ?? ZoomValue.GEO,
            timeManagerInstance.selectedDate,
          );
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.onCruncherReady,
      () => {
        if (!settingsManager.disableUI && settingsManager.isLoadLastSensor) {
          SensorListPlugin.reloadLastSensor_();
        }
        // 延迟更新自定义传感器列表，确保自定义传感器已经加载完成
        setTimeout(() => {
          SensorListPlugin.updateCustomSensorsList();
        }, 500);
      },
    );

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === 'Home' && !isRepeat) {
        // If a sensor is selected rotate the camera to it
        if ((keepTrackApi.getSensorManager().currentSensors.length > 0) &&
          (keepTrackApi.getMainCamera().cameraType === CameraType.DEFAULT)) {
          const sensor = keepTrackApi.getSensorManager().currentSensors[0];

          keepTrackApi.getMainCamera().lookAtLatLon(sensor.lat, sensor.lon, sensor.zoom ?? ZoomValue.GEO, keepTrackApi.getTimeManager().selectedDate);
          keepTrackApi.getSoundManager().play(SoundNames.WHOOSH);
        }
      }
    });
  }

  sensorListContentClick(sensorClick: string) {
    if (!this.isMenuButtonActive) {
      return;
    }

    const sensorManagerInstance = keepTrackApi.getSensorManager();

    if (sensorClick === '') {
      errorManagerInstance.debug('The menu item was clicked but the menu was not defined.');

      return;
    }

    // if sensorClick is a name in sensorGroup then load all sensors in that group
    if (this.sensorGroups_.some((sensorGroup) => sensorGroup.name === sensorClick)) {
      // Remove any secondary sensors for sensor groups
      sensorManagerInstance.clearSecondarySensors();
      sensorManagerInstance.setSensor(sensorClick);
    } else if (sensorClick.startsWith('Custom Sensor')) {
      // 处理自定义传感器 - 从注册表查找
      const foundSensor = (window as any).CustomSensorPlugin?.getCustomSensor?.(sensorClick);

      if (foundSensor) {
        sensorManagerInstance.setSensor(foundSensor);
      } else {
        errorManagerInstance.warn(`自定义传感器未找到: ${sensorClick}`);
        return;
      }
    } else {
      // Remove any secondary sensors for regular sensors
      sensorManagerInstance.clearSecondarySensors();
      sensorManagerInstance.setSensor(sensors[`${sensorClick}`]);
    }

    // Deselect any satellites
    if ((keepTrackApi.getPlugin(SelectSatManager)?.selectedSat ?? -1) <= -1) {
      try {
        keepTrackApi
          .getMainCamera()
          .lookAtLatLon(
            sensorManagerInstance.currentSensors[0].lat,
            sensorManagerInstance.currentSensors[0].lon,
            sensorManagerInstance.currentSensors[0].zoom ?? ZoomValue.GEO,
            keepTrackApi.getTimeManager().selectedDate,
          );
      } catch (e) {
        // TODO: More intentional conditional statement
        errorManagerInstance.warn(`Error in sensorListContentClick: ${e}`);
        // Multi-sensors break this
      }
    }
  }

  private static createLiForSensor_(sensor: DetailedSensor) {
    return keepTrackApi.html`
      <li class="menu-selectable" data-sensor="${sensor.objName ?? 'Missing Data'}">
        <span>${sensor.uiName ?? 'Missing Data'}</span>
        <span>${sensor.system ?? 'Missing Data'}</span>
        <span class="badge dark-blue-badge" data-badge-caption="${sensor.operator ?? 'Missing Data'}"></span>
      </li>
    `;
  }

  private genericSensors_(name: string): string {
    try {
      if (!name) {
        throw new Error('Name parameter is required');
      }

      const sensorGroup = this.sensorGroups_.find((sensorGroup: SensorGroup) => sensorGroup.name === name);

      if (!sensorGroup) {
        throw new Error(`No sensor group found with name: ${name}`);
      }

      if (!sensorGroup.header || !sensorGroup.list || !sensorGroup.topLink) {
        throw new Error(`Sensor group ${name} is missing required properties`);
      }

      const params = {
        name,
        header: sensorGroup.header,
        sensors: sensorGroup.list.map((sensorName) => {
          const sensor = sensors[sensorName];

          if (!sensor) {
            errorManagerInstance.warn(`Sensor ${sensorName} listed in sensorGroups was not found in sensors catalog!`);

            return null;
          }

          return sensor;
        }).filter((sensor) => sensor !== null),
        topLinks: [
          {
            name: sensorGroup.topLink.name,
            badge: sensorGroup.topLink.badge,
          },
        ],
      };

      if (params.sensors.length === 0) {
        throw new Error(`No sensors found for group: ${name}`);
      }

      const renderedTopLink = params.topLinks
        .map(
          (link) => keepTrackApi.html`<li class="menu-selectable sensor-top-link" data-sensor="${params.name}">
              <span>${link.name}</span>
              <span class="badge dark-blue-badge" data-badge-caption="${link.badge}"></span>
            </li>`,
        )
        .join('');

      return keepTrackApi.html`
        ${SensorListPlugin.genH5Title_(params.header)}
        ${renderedTopLink}
        ${params.sensors.map((sensor) => SensorListPlugin.createLiForSensor_(sensor)).join('')}
      `;
    } catch (error) {
      errorManagerInstance.warn('Error generating HTML:', error);

      return '';
    }
  }

  private static reloadLastSensor_() {
    const json = PersistenceManager.getInstance().getItem(StorageKey.CURRENT_SENSOR);

    if (!json) {
      return;
    }
    const currentSensor = JSON.parse(json);
    // istanbul ignore next

    if (currentSensor !== null) {
      try {
        const sensorManagerInstance = keepTrackApi.getSensorManager();

        // If there is a sensorId set use that
        if (typeof currentSensor[0] === 'undefined' || currentSensor[0] === null) {
          sensorManagerInstance.setSensor(null, currentSensor[1]);
          // If the sensor is a string, load that collection of sensors
        } else if (typeof currentSensor[0].objName === 'undefined') {
          sensorManagerInstance.setSensor(currentSensor[0], currentSensor[1]);
        } else {
          // Seems to be a single sensor without a sensorId, load that
          sensorManagerInstance.setSensor(sensors[currentSensor[0].objName], currentSensor[1]);
        }
      } catch {
        PersistenceManager.getInstance().removeItem(StorageKey.CURRENT_SENSOR);
      }
    }
  }

  /**
   * 更新自定义传感器列表显示
   */
  static updateCustomSensorsList(): void {
    const customSensorsGroup = getEl('custom-sensors-group');
    const customSensorsList = getEl('custom-sensors-list');

    if (!customSensorsGroup || !customSensorsList) {
      return;
    }

    // 从注册表获取自定义传感器
    const customSensors = (window as any).CustomSensorPlugin?.getAllCustomSensors?.() || [];

    // 按objName排序以确保稳定的显示顺序
    customSensors.sort((a, b) => {
      const aName = a.objName || '';
      const bName = b.objName || '';
      return aName.localeCompare(bName);
    });



    if (customSensors.length === 0) {
      customSensorsGroup.classList.add('start-hidden');
      return;
    }

    customSensorsGroup.classList.remove('start-hidden');

    const htmlContent = customSensors.map(sensor => {
      return keepTrackApi.html`
        <li class="menu-selectable" data-sensor="${sensor.objName ?? 'Missing Data'}">
          <span>${sensor.uiName ?? 'Missing Data'}</span>
          <span>${sensor.system ?? 'Missing Data'}</span>
          <span class="badge dark-blue-badge" data-badge-caption="${sensor.operator ?? 'Missing Data'}"></span>
        </li>
      `;
    }).join('');

    customSensorsList.innerHTML = htmlContent;

    // 自定义传感器现在使用统一的点击处理机制，不需要单独的事件监听器
  }
}
