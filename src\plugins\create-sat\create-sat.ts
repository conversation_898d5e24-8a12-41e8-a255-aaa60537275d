import addSatellitePnng from '@public/img/icons/add-satellite.png';
import {
  DetailedSatellite,
  DetailedSatelliteParams,
  EciVec3,
  FormatTle,
  KilometersPerSecond,
  SatelliteRecord,
  Sgp4,
} from 'ootk';
import { keepTrackApi } from '../../keepTrackApi';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

import { countryCodeList, countryNameList } from '@app/catalogs/countries';
import { GetSatType, KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { getEl } from '@app/lib/get-el';
import { t7e } from '@app/locales/keys';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { SatMath } from '@app/static/sat-math';
import { CruncerMessageTypes } from '@app/webworker/positionCruncher';
import { saveAs } from 'file-saver';

/**
 * Interface for TLE input parameters
 */
interface TleInputParams {
  scc: string;
  type: string;
  country: string;
  inc: string;
  meanmo: string;
  rasc: string;
  ecen: string;
  argPe: string;
  meana: string;
  epochyr: string;
  epochday: string;
  source: string;
  name: string;
}

/**
 * CreateSat plugin for creating and editing satellites
 */
export class CreateSat extends KeepTrackPlugin {
  readonly id = 'CreateSat';
  dependencies_ = [SelectSatManager.name];

  menuMode: MenuMode[] = [MenuMode.BASIC, MenuMode.ADVANCED, MenuMode.ALL];

  isRequireSatelliteSelected = false;
  isIconDisabledOnLoad = false;
  isIconDisabled = false;

  static readonly elementPrefix = 'createSat';
  bottomIconImg = addSatellitePnng;
  sideMenuElementName = 'createSat-menu';

  constructor() {
    super();
  }

  /**
   * HTML template for the side menu
   */
  sideMenuElementHtml = keepTrackApi.html`
    <div id="createSat-menu" class="side-menu-parent start-hidden text-select">
      <div id="createSat-content" class="side-menu">
        <div class="row">
          <h5 class="center-align">新建卫星</h5>
          <form id="createSat">
            <div class="input-field col s12">
              <input value="90000" id="${CreateSat.elementPrefix}-scc" type="text" maxlength="5" />
              <label for="${CreateSat.elementPrefix}-scc" class="active">卫星SCC#</label>
            </div>
            <div class="input-field col s12">
              <select value=1 id="${CreateSat.elementPrefix}-type" type="text">
                <option value=1>载荷</option>
                <option value=2>火箭体</option>
                <option value=3>碎片</option>
                <option value=4>特殊的</option>
              </select>
              <label for="${CreateSat.elementPrefix}-type">目标类型</label>
            </div>
            <div class="input-field col s12">
              <select value="TBD" id="${CreateSat.elementPrefix}-country" type="text">
                <option value="TBD">未知</option>
              </select>
              <label for="${CreateSat.elementPrefix}-country">国家</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AA" id="${CreateSat.elementPrefix}-year" type="text" maxlength="2" />
              <label for="${CreateSat.elementPrefix}-year" class="active">视锥</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AAA.AAAAAAAA" id="${CreateSat.elementPrefix}-day" type="text" maxlength="12" />
              <label for="${CreateSat.elementPrefix}-day" class="active">Epoch天</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AAA.AAAA" id="${CreateSat.elementPrefix}-inc" type="text" maxlength="8" />
              <label for="${CreateSat.elementPrefix}-inc" class="active">倾角</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AAA.AAAA" id="${CreateSat.elementPrefix}-rasc" type="text" maxlength="8" />
              <label for="${CreateSat.elementPrefix}-rasc" class="active">升交点赤经</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AA.AAAAAAAA" id="${CreateSat.elementPrefix}-ecen" type="text" maxlength="7" />
              <label for="${CreateSat.elementPrefix}-ecen" class="active">偏心率</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AA.AAAAAAAA" id="${CreateSat.elementPrefix}-argPe" type="text" maxlength="8" />
              <label for="${CreateSat.elementPrefix}-argPe" class="active">近地点幅角</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AAA.AAAA" id="${CreateSat.elementPrefix}-meana" type="text" maxlength="8" />
              <label for="${CreateSat.elementPrefix}-meana" class="active">平均异常</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AAA.AAAA" id="${CreateSat.elementPrefix}-meanmo" type="text" maxlength="11" />
              <label for="${CreateSat.elementPrefix}-meanmo" class="active">平均运动</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="AA.AAAA" id="${CreateSat.elementPrefix}-per" type="text" maxlength="11" />
              <label for="${CreateSat.elementPrefix}-per" class="active">轨道周期</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="" id="${CreateSat.elementPrefix}-src" type="text" maxlength="24" />
              <label for="${CreateSat.elementPrefix}-src" class="active">数据来源</label>
            </div>
            <div class="input-field col s12">
              <input placeholder="" id="${CreateSat.elementPrefix}-name" type="text" maxlength="24" />
              <label for="${CreateSat.elementPrefix}-name" class="active">卫星名字</label>
            </div>
            <div class="center-align row">
              <button id="createSat-submit" class="btn btn-ui waves-effect waves-light" type="button" name="action">新建卫星 &#9658;</button>
            </div>
            <div class="center-align row">
              <button id="createSat-save" class="btn btn-ui waves-effect waves-light" type="button" name="action">保存TLE &#9658;</button>
            </div>
          </form>
        </div>
        <div id="${CreateSat.elementPrefix}-error" class="center-align menu-selectable start-hidden">
          <h6 class="center-align">Error</h6>
        </div>
      </div>
    </div>
  `;

  /**
   * Add HTML and register events
   */
  addHtml(): void {
    super.addHtml();
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, this.uiManagerFinal_.bind(this));
  }

  /**
   * Initialize all event listeners for the UI
   */
  private uiManagerFinal_(): void {
    // Period and mean motion converter
    this.setupPeriodMeanMotionConverters_();

    // Submit and save buttons
    getEl('createSat-submit')!.addEventListener('click', CreateSat.createSatSubmit_);
    getEl('createSat-save')!.addEventListener('click', CreateSat.exportTLE_);

    countryNameList.forEach((countryName: string) => {
      let countryCode = countryCodeList[countryName];

      if (typeof countryCode === 'string' && countryCode.includes('|')) {
        countryCode = countryCode.split('|')[0];
      }

      getEl(`${CreateSat.elementPrefix}-country`)!.insertAdjacentHTML('beforeend', `<option value="${countryCode}">${countryName}</option>`);
    });

    // Populate default values
    this.populateSideMenu_();
  }

  /**
   * Setup period and mean motion converter event listeners
   */
  private setupPeriodMeanMotionConverters_(): void {
    // Period to Mean Motion conversion
    getEl(`${CreateSat.elementPrefix}-per`)!.addEventListener('change', () => {
      const perInput = getEl(`${CreateSat.elementPrefix}-per`) as HTMLInputElement;
      const per = perInput.value;

      if (per === '') {
        return;
      }

      try {
        const meanmo = 1440 / parseFloat(per);

        (getEl(`${CreateSat.elementPrefix}-meanmo`) as HTMLInputElement).value = meanmo.toFixed(4);
      } catch (error) {
        errorManagerInstance.error(error as Error, 'create-sat.ts', '将周期转换为平均运动时出现错误');
      }
    });

    // Mean Motion to Period conversion
    getEl(`${CreateSat.elementPrefix}-meanmo`)!.addEventListener('change', () => {
      const meanmoInput = getEl(`${CreateSat.elementPrefix}-meanmo`) as HTMLInputElement;
      const meanmo = meanmoInput.value;

      if (meanmo === '') {
        return;
      }

      try {
        const per = (1440 / parseFloat(meanmo)).toFixed(4);

        (getEl(`${CreateSat.elementPrefix}-per`) as HTMLInputElement).value = per;
      } catch (error) {
        errorManagerInstance.error(error as Error, 'create-sat.ts', '将平均运动转换为周期时出错');
      }
    });
  }

  /**
   * Get all TLE input values from form
   */
  private static getTleInputs_(): TleInputParams {
    return {
      scc: (getEl(`${CreateSat.elementPrefix}-scc`) as HTMLInputElement).value,
      type: (getEl(`${CreateSat.elementPrefix}-type`) as HTMLInputElement).value,
      country: (getEl(`${CreateSat.elementPrefix}-country`) as HTMLInputElement).value,
      inc: (getEl(`${CreateSat.elementPrefix}-inc`) as HTMLInputElement).value,
      meanmo: (getEl(`${CreateSat.elementPrefix}-meanmo`) as HTMLInputElement).value,
      rasc: (getEl(`${CreateSat.elementPrefix}-rasc`) as HTMLInputElement).value,
      ecen: (getEl(`${CreateSat.elementPrefix}-ecen`) as HTMLInputElement).value,
      argPe: (getEl(`${CreateSat.elementPrefix}-argPe`) as HTMLInputElement).value,
      meana: (getEl(`${CreateSat.elementPrefix}-meana`) as HTMLInputElement).value,
      epochyr: (getEl(`${CreateSat.elementPrefix}-year`) as HTMLInputElement).value,
      epochday: (getEl(`${CreateSat.elementPrefix}-day`) as HTMLInputElement).value,
      source: (getEl(`${CreateSat.elementPrefix}-src`) as HTMLInputElement).value,
      name: (getEl(`${CreateSat.elementPrefix}-name`) as HTMLInputElement).value,
    };
  }

  /**
   * Populate the form with default values
   */
  private populateSideMenu_(): void {
    // Set default inclination
    const defaultInc = 0;
    const inc = defaultInc.toFixed(4).padStart(8, '0');

    (getEl(`${CreateSat.elementPrefix}-inc`) as HTMLInputElement).value = inc;

    // Set date-related values
    const date = new Date(keepTrackApi.getTimeManager().simulationTimeObj);
    const year = date.getFullYear().toString().slice(2, 4);
    const currentJday = keepTrackApi.getTimeManager().getUTCDayOfYear(date);
    const currentTime = (date.getUTCHours() * 3600 + date.getUTCMinutes() * 60 + date.getUTCSeconds()) / 86400;
    const day = (currentJday + currentTime).toFixed(8).padStart(12, '0');


    (getEl(`${CreateSat.elementPrefix}-year`) as HTMLInputElement).value = year;
    (getEl(`${CreateSat.elementPrefix}-day`) as HTMLInputElement).value = day;

    // Set orbital parameters with reasonable defaults
    (getEl(`${CreateSat.elementPrefix}-rasc`) as HTMLInputElement).value = '000.0000';
    (getEl(`${CreateSat.elementPrefix}-ecen`) as HTMLInputElement).value = '0000000';
    (getEl(`${CreateSat.elementPrefix}-meana`) as HTMLInputElement).value = '000.0000';
    (getEl(`${CreateSat.elementPrefix}-argPe`) as HTMLInputElement).value = '000.0000';
    (getEl(`${CreateSat.elementPrefix}-meanmo`) as HTMLInputElement).value = '16.00000';
    (getEl(`${CreateSat.elementPrefix}-per`) as HTMLInputElement).value = '90.00000';

    // Set metadata
    (getEl(`${CreateSat.elementPrefix}-src`) as HTMLInputElement).value = '用户创建';
    (getEl(`${CreateSat.elementPrefix}-name`) as HTMLInputElement).value = '新卫星';
  }

  /**
   * Create and submit a new satellite
   */
  private static createSatSubmit_(): void {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const orbitManagerInstance = keepTrackApi.getOrbitManager();
    const errorElement = getEl(`${CreateSat.elementPrefix}-error`)!;

    // Hide any previous error
    errorElement.style.display = 'none';

    // Get all input values
    const inputParams = CreateSat.getTleInputs_();

    try {
      // Convert SCC to internal ID
      const satId = catalogManagerInstance.sccNum2Id(parseInt(inputParams.scc)) ?? -1;
      const obj = catalogManagerInstance.getObject(satId, GetSatType.EXTRA_ONLY);

      if (!obj?.isSatellite()) {
        keepTrackApi.getUiManager().toast(
          'Invalid satellite object',
          ToastMsgType.error,
          true,
        );

        return;
      }

      const sat = obj as DetailedSatellite;
      const country = inputParams.country;
      const type = parseInt(inputParams.type);
      const intl = `${inputParams.epochyr}69B`; // International designator

      // Create TLE from parameters
      const { tle1, tle2 } = FormatTle.createTle({
        sat,
        inc: inputParams.inc,
        meanmo: inputParams.meanmo,
        rasc: inputParams.rasc,
        argPe: inputParams.argPe,
        meana: inputParams.meana,
        ecen: inputParams.ecen,
        epochyr: inputParams.epochyr,
        epochday: inputParams.epochday,
        intl,
        scc: inputParams.scc,
      });

      // Check if TLE generation failed
      if (tle1 === 'Error') {
        errorManagerInstance.error(
          new Error(tle2),
          'create-sat.ts',
          t7e('errorMsgs.CreateSat.errorCreatingSat'),
        );

        return;
      }

      // Create satellite record from TLE
      let satrec: SatelliteRecord;

      try {
        satrec = Sgp4.createSatrec(tle1, tle2);
      } catch (e) {
        errorManagerInstance.error(e as Error, 'create-sat.ts', '创建卫星记录时出错！');

        return;
      }

      // Validate altitude is reasonable
      if (SatMath.altitudeCheck(satrec, keepTrackApi.getTimeManager().simulationTimeObj) <= 1) {
        keepTrackApi.getUiManager().toast(
          '传播卫星失败。请尝试不同的参数，如果参数正确，请报告此问题。',
          ToastMsgType.caution,
          true,
        );

        return;
      }

      // Propagate satellite to get position and velocity
      const spg4vec = Sgp4.propagate(satrec, 0);
      const pos = spg4vec.position as EciVec3;
      const vel = spg4vec.velocity as EciVec3<KilometersPerSecond>;

      // Create new satellite object
      const info: DetailedSatelliteParams = {
        id: satId,
        type,
        country,
        tle1,
        tle2,
        name: inputParams.name,
      };

      const newSat = new DetailedSatellite({
        ...info,
        ...{
          position: pos,
          velocity: vel,
          source: inputParams.source,
        },
      });

      // Add to catalog
      catalogManagerInstance.objectCache[satId] = newSat;

      // Update satellite cruncher
      try {
        catalogManagerInstance.satCruncher.postMessage({
          typ: CruncerMessageTypes.SAT_EDIT,
          active: true,
          id: satId,
          tle1,
          tle2,
        });
      } catch (e) {
        errorManagerInstance.error(e as Error, 'create-sat.ts', 'Sat Cruncher message failed');
      }

      // Update orbit buffer
      try {
        orbitManagerInstance.changeOrbitBufferData(satId, tle1, tle2);
      } catch (e) {
        errorManagerInstance.error(e as Error, 'create-sat.ts', '更改轨道缓冲区数据失败');
      }

      // Search for the new satellite
      keepTrackApi.getUiManager().doSearch(inputParams.scc);

      // Show success message
      keepTrackApi.getUiManager().toast(
        `卫星 ${inputParams.name} (${inputParams.scc}) 创建成功`,
        ToastMsgType.standby,
        true,
      );

    } catch (error) {
      errorManagerInstance.error(error as Error, 'create-sat.ts', 'Failed to create satellite');
      keepTrackApi.getUiManager().toast('创建卫星失败', ToastMsgType.error, true);
    }
  }

  /**
   * Export TLE to a file
   */
  private static exportTLE_(e: Event): void {
    e.preventDefault();

    const catalogManagerInstance = keepTrackApi.getCatalogManager();

    try {
      const scc = (getEl(`${CreateSat.elementPrefix}-scc`) as HTMLInputElement).value;
      const satId = catalogManagerInstance.sccNum2Id(parseInt(scc));
      const sat = catalogManagerInstance.getObject(satId, GetSatType.EXTRA_ONLY) as DetailedSatellite;

      if (!sat || !sat.tle1 || !sat.tle2) {
        keepTrackApi.getUiManager().toast('没有TLE数据可导出', ToastMsgType.error, true);

        return;
      }

      // Format TLE data
      const tleText = `${sat.tle1}\n${sat.tle2}`;
      const blob = new Blob([tleText], {
        type: 'text/plain;charset=utf-8',
      });

      saveAs(blob, `${scc}.tle`);
      keepTrackApi.getUiManager().toast('TLE导出成功', ToastMsgType.standby, true);
    } catch (error) {
      errorManagerInstance.error(error as Error, 'create-sat.ts', 'Failed to export TLE');
      keepTrackApi.getUiManager().toast('TLE导出失败', ToastMsgType.error, true);
    }
  }
}
