import { KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl, hideEl } from '@app/lib/get-el';
import { adviceManagerInstance } from '@app/singletons/adviceManager';
import fullscreenPng from '@public/img/icons/fullscreen.png';
import helpPng from '@public/img/icons/help.png';
import layersPng from '@public/img/icons/layers.png';
import soundOffPng from '@public/img/icons/sound-off.png';
import soundOnPng from '@public/img/icons/sound-on.png';
import { errorManagerInstance } from '../../singletons/errorManager';
import { KeepTrackPlugin } from '../KeepTrackPlugin';

export class TopMenu extends KeepTrackPlugin {
  readonly id = 'TopMenu';
  dependencies_ = [];
  static readonly SEARCH_RESULT_ID = 'search-results';

  addHtml() {
    super.addHtml();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerInit,
      () => {
        getEl('keeptrack-header')?.insertAdjacentHTML(
          'beforeend',
          keepTrackApi.html`
            <nav>
              <div id="nav-wrapper" class="nav-wrapper">
                <ul id="nav-mobile2" class="right">
                  <li>
                    <div id="search-holder" class="menu-item">
                      <input id="search" type="search" name="search" placeholder="搜索...  多目标用逗号分割" required />
                      <span id="clear-search" class="search-clear" style="display:none">X</span>
                    </div>
                  </li>
                  <li>
                    <a id="sound-btn" class="top-menu-icons">
                      <div class="top-menu-icons bmenu-item-selected">
                        <img id="sound-icon"
                        src="" delayedsrc="${soundOnPng}" alt="" />
                      </div>
                    </a>
                  </li>
                  <li>
                    <a id="legend-menu" class="top-menu-icons">
                      <div id="legend-icon" class="top-menu-icons">
                        <img src=${layersPng} alt="" />
                      </div>
                    </a>
                  </li>
                  <li>
                    <a id="tutorial-btn" class="top-menu-icons">
                      <div id="tutorial-icon" class="top-menu-icons">
                        <img src=${helpPng} alt="" />
                      </div>
                    </a>
                  </li>
                  <li>
                    <a id="fullscreen-icon" class="top-menu-icons"><img src=${fullscreenPng} alt="" /></a>
                  </li>
                </ul>
              </div>
            </nav>
          `,
        );

        // Advice only applies to things in the bottom menu
        if (settingsManager.isDisableBottomMenu) {
          keepTrackApi.on(
            KeepTrackApiEvents.uiManagerFinal,
            () => {
              hideEl('tutorial-btn');
            },
          );

          return;
        }

        keepTrackApi.containerRoot?.insertAdjacentHTML(
          'beforeend',
          keepTrackApi.html`
            <div id="help-outer-container" class="valign">
              <div id="help-screen" class="valign-wrapper">
                <div id="help-inner-container" class="valign">
                  <p>
                    <span id="help-header" class="logo-font">TITLE</span>
                  </p>
                  <span id="help-text">ADVICE</span>
                </div>
              </div>
            </div>
          `,
        );

        adviceManagerInstance.init();
      },
    );
  }

  addJs() {
    super.addJs();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl('sound-btn')!.onclick = () => {
          const soundIcon = <HTMLImageElement>getEl('sound-icon');
          const soundManager = keepTrackApi.getSoundManager();

          if (!soundManager) {
            errorManagerInstance.warn('声音管理器未启用，请检查您的设置！');

            return;
          }

          if (!soundManager.isMute) {
            soundManager.isMute = true;
            soundIcon.src = soundOffPng;
            soundIcon.parentElement!.classList.remove('bmenu-item-selected');
            soundIcon.parentElement!.classList.add('bmenu-item-error');
          } else {
            soundManager.isMute = false;
            soundIcon.src = soundOnPng;
            soundIcon.parentElement!.classList.add('bmenu-item-selected');
            soundIcon.parentElement!.classList.remove('bmenu-item-error');
          }
        };

        getEl('tutorial-icon')!.onclick = () => {
          const helpScreen = getEl('help-screen');
          if (helpScreen) {
            helpScreen.style.display = helpScreen.style.display === 'none' ? 'flex' : 'none';
          }
        };

        // 完全移除搜索图标点击事件
        const searchIcon = getEl('search-icon');
        if (searchIcon) {
          searchIcon.onclick = null;
          searchIcon.style.pointerEvents = 'none';
        }

        // 设置清除按钮功能
        const searchInput = <HTMLInputElement>getEl('search');
        const clearSearch = getEl('clear-search');

        // 清除按钮始终显示
        clearSearch.style.display = 'block';

        searchInput.addEventListener('input', () => {
          if (searchInput.value.length > 0) {
            clearSearch.style.display = 'block';
          } else {
            clearSearch.style.display = 'none';
          }
        });

        clearSearch.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();

          searchInput.value = '';
          searchInput.focus();
          clearSearch.style.display = 'none';

          // 清除搜索结果
          const searchResults = getEl(TopMenu.SEARCH_RESULT_ID) || getEl('search-results');
          if (searchResults) {
            searchResults.innerHTML = '';
            searchResults.style.display = 'none';
          }

          // 清除主界面搜索结果和轨道
          try {
            keepTrackApi.getUiManager()?.searchManager?.hideResults();
            keepTrackApi.getUiManager()?.searchManager?.doSearch('');
          } catch (error) {
            console.warn('搜索清除时出错:', error);
          }

          // 触发搜索清空事件
          keepTrackApi.emit(KeepTrackApiEvents.searchUpdated, '');
        };

        // 🔧 添加额外的事件监听器确保清除功能正常
        clearSearch.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();

          searchInput.value = '';
          searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        });

        clearSearch.addEventListener('mousedown', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
      },
    );

    keepTrackApi.on(KeepTrackApiEvents.setSensor, this.updateSensorName.bind(this));
  }

  updateSensorName() {
    const sensorSelectedDom = getEl('sensor-selected', true);

    if (sensorSelectedDom) {
      const sensorTitle = keepTrackApi.getSensorManager()?.sensorTitle;

      // If this.sensorTitle is empty hide the div
      if (!sensorTitle || sensorTitle === '') {
        sensorSelectedDom.parentElement!.style.display = 'none';
      } else {
        sensorSelectedDom.innerText = sensorTitle;
        sensorSelectedDom.parentElement!.style.display = 'block';
      }
    }
  }
}
