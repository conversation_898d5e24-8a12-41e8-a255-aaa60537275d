const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "z<PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function testESConnection() {
  try {
    console.log('正在连接 Elasticsearch...');
    console.log('ES 地址:', esConfig.url);
    console.log('用户名:', esConfig.username);
    console.log('索引:', esConfig.index);
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 10000,
    });

    // 测试连接
    console.log('\n1. 测试 ES 连接...');
    const info = await client.info();
    console.log('ES 信息:', info);

    // 测试索引是否存在
    console.log('\n2. 测试索引是否存在...');
    const indexExists = await client.indices.exists({
      index: esConfig.index
    });
    console.log('索引存在:', indexExists);

    if (indexExists) {
      // 测试简单查询
      console.log('\n3. 测试简单查询...');
      const searchResult = await client.search({
        index: esConfig.index,
        size: 1,
        query: {
          match_all: {}
        }
      });
      console.log('查询结果数量:', searchResult.hits?.total?.value || 0);
    }

  } catch (error) {
    console.error('ES 连接失败:', error.message);
    console.error('错误详情:', error);
  }
}

testESConnection(); 