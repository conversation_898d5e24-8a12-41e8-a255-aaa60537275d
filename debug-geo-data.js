const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function debugGeoData() {
  try {
    console.log('调试GEO数据结构...\n');
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 30000,
    });

    // 1. 查找包含orbital_elements.object_type字段的记录
    console.log('1. 查找包含orbital_elements.object_type字段的记录...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          exists: { field: "orbital_elements.object_type" }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条包含 orbital_elements.object_type 的记录`);
      
      if (result.hits?.hits?.length > 0) {
        console.log('\n前5条记录的object_type值:');
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name}`);
          console.log(`   object_type: "${record.orbital_elements?.object_type}"`);
          console.log(`   subsat_long: ${record.orbital_elements?.subsat_long}`);
          console.log('');
        });
        
        // 统计不同的object_type值
        console.log('统计不同的object_type值...');
        const typeAgg = await client.search({
          index: esConfig.index,
          size: 0,
          aggs: {
            object_types: {
              terms: {
                field: "orbital_elements.object_type.keyword",
                size: 20
              }
            }
          }
        });
        
        if (typeAgg.aggregations?.object_types?.buckets) {
          console.log('object_type值统计:');
          typeAgg.aggregations.object_types.buckets.forEach(bucket => {
            console.log(`  - "${bucket.key}": ${bucket.doc_count} 条记录`);
          });
        }
      }
    } catch (error) {
      console.log('查询失败:', error.message);
    }

    // 2. 查找包含subsat_long字段的记录
    console.log('\n2. 查找包含orbital_elements.subsat_long字段的记录...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          exists: { field: "orbital_elements.subsat_long" }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条包含 orbital_elements.subsat_long 的记录`);
      
      if (result.hits?.hits?.length > 0) {
        console.log('\n前5条记录的经度值:');
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name}`);
          console.log(`   subsat_long: ${record.orbital_elements?.subsat_long}`);
          console.log(`   object_type: "${record.orbital_elements?.object_type}"`);
          console.log('');
        });
      }
    } catch (error) {
      console.log('查询失败:', error.message);
    }

    // 3. 查找最近的记录，看看数据结构
    console.log('\n3. 查找最近的记录，检查数据结构...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 3,
        sort: [{ time: { order: 'desc' } }],
        query: { match_all: {} }
      });
      
      if (result.hits?.hits?.length > 0) {
        console.log('最近的3条记录:');
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`\n记录 ${index + 1}:`);
          console.log(`  NORAD: ${record.norad_id}`);
          console.log(`  名称: ${record.satellite_name}`);
          console.log(`  时间: ${record.time}`);
          console.log(`  orbital_elements存在: ${!!record.orbital_elements}`);
          
          if (record.orbital_elements) {
            console.log(`  orbital_elements.object_type: "${record.orbital_elements.object_type}"`);
            console.log(`  orbital_elements.subsat_long: ${record.orbital_elements.subsat_long}`);
            console.log(`  orbital_elements的所有字段: ${Object.keys(record.orbital_elements).join(', ')}`);
          }
        });
      }
    } catch (error) {
      console.log('查询失败:', error.message);
    }

    // 4. 尝试查找特定的NORAD ID（从您提供的示例）
    console.log('\n4. 查找特定的NORAD ID (39476 - INMARSAT 5-F1)...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 1,
        query: {
          term: { norad_id: 39476 }
        }
      });
      
      if (result.hits?.hits?.length > 0) {
        const record = result.hits.hits[0]._source;
        console.log('找到INMARSAT 5-F1记录:');
        console.log(`  NORAD: ${record.norad_id}`);
        console.log(`  名称: ${record.satellite_name}`);
        console.log(`  时间: ${record.time}`);
        console.log(`  object_type: "${record.orbital_elements?.object_type}"`);
        console.log(`  subsat_long: ${record.orbital_elements?.subsat_long}`);
        
        if (record.orbital_elements?.object_type === 'GEO') {
          console.log('✓ 这是一个GEO卫星！');
        }
      } else {
        console.log('未找到NORAD ID 39476的记录');
      }
    } catch (error) {
      console.log('查询失败:', error.message);
    }

  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

debugGeoData();
