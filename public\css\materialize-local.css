.page-footer {
    background-color: var(--color-dark-background) !important;
}

.spinner-layer {
    border-color: #ffffff !important;
}

.switch label input[type='checkbox']:checked+.lever:after {
    background-color: rgb(255, 255, 255) !important;
}

/* Text inputs */
input:not([type]),
input[type='text']:not(.browser-default),
input[type='password']:not(.browser-default),
input[type='email']:not(.browser-default),
input[type='url']:not(.browser-default),
input[type='time']:not(.browser-default),
input[type='date']:not(.browser-default),
input[type='datetime']:not(.browser-default),
input[type='datetime-local']:not(.browser-default),
input[type='tel']:not(.browser-default),
input[type='number']:not(.browser-default),
input[type='search']:not(.browser-default),
textarea.materialize-textarea {
    border-bottom: 1px solid #ffffff !important;
    color: #ffffff;
    -webkit-appearance: none;
}

.btn,
.btn-large,
.btn-small {
    background-color: var(--color-primary);
}

.btn:hover,
.btn-large:hover,
.btn-small:hover {
    background-color: var(--color-primary-light);
}

.btn:focus,
.btn-large:focus,
.btn-small:focus,
.btn-floating:focus {
    background-color: var(--color-primary-dark);
}

.switch label input[type='checkbox']:checked+.lever {
    background-color: var(--color-primary) !important;
}

.dropdown-content li>a,
.dropdown-content li>span {
    color: var(--color-primary) !important;
    font-size: 16px !important;  /* 改为16px，与其他设置一致 */
}

.toast {
    border-radius: 0px !important;
    min-height: 4.25rem !important;
}

.select-wrapper input.select-dropdown {
    height: 35px !important;  /* 与输入框高度一致 */
    line-height: 1.4 !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.row {
    margin-bottom: 1rem;
}

.input-field {
    margin-top: 1rem 0 0 0;
    padding-left: 1rem;
    padding-right: 1rem;
}

.input-field.col label {
    padding-top: 0px;
}

input:not([type]):focus:not([readonly])+label,
input[type='text']:not(.browser-default):focus:not([readonly])+label,
input[type='password']:not(.browser-default):focus:not([readonly])+label,
input[type='email']:not(.browser-default):focus:not([readonly])+label,
input[type='url']:not(.browser-default):focus:not([readonly])+label,
input[type='time']:not(.browser-default):focus:not([readonly])+label,
input[type='date']:not(.browser-default):focus:not([readonly])+label,
input[type='datetime']:not(.browser-default):focus:not([readonly])+label,
input[type='datetime-local']:not(.browser-default):focus:not([readonly])+label,
input[type='tel']:not(.browser-default):focus:not([readonly])+label,
input[type='number']:not(.browser-default):focus:not([readonly])+label,
input[type='search']:not(.browser-default):focus:not([readonly])+label,
textarea.materialize-textarea:focus:not([readonly])+label {
    color: var(--color-dark-text-accent);
}

input:not([type]):disabled+label,
input:not([type])[readonly='readonly']+label,
input[type='text']:not(.browser-default):disabled+label,
input[type='text']:not(.browser-default)[readonly='readonly']+label,
input[type='password']:not(.browser-default):disabled+label,
input[type='password']:not(.browser-default)[readonly='readonly']+label,
input[type='email']:not(.browser-default):disabled+label,
input[type='email']:not(.browser-default)[readonly='readonly']+label,
input[type='url']:not(.browser-default):disabled+label,
input[type='url']:not(.browser-default)[readonly='readonly']+label,
input[type='time']:not(.browser-default):disabled+label,
input[type='time']:not(.browser-default)[readonly='readonly']+label,
input[type='date']:not(.browser-default):disabled+label,
input[type='date']:not(.browser-default)[readonly='readonly']+label,
input[type='datetime']:not(.browser-default):disabled+label,
input[type='datetime']:not(.browser-default)[readonly='readonly']+label,
input[type='datetime-local']:not(.browser-default):disabled+label,
input[type='datetime-local']:not(.browser-default)[readonly='readonly']+label,
input[type='tel']:not(.browser-default):disabled+label,
input[type='tel']:not(.browser-default)[readonly='readonly']+label,
input[type='number']:not(.browser-default):disabled+label,
input[type='number']:not(.browser-default)[readonly='readonly']+label,
input[type='search']:not(.browser-default):disabled+label,
input[type='search']:not(.browser-default)[readonly='readonly']+label,
textarea.materialize-textarea:disabled+label,
textarea.materialize-textarea[readonly='readonly']+label {
    color: var(--color-dark-text-muted)
}

input:not([type]):disabled,
input:not([type])[readonly='readonly'],
input[type='text']:not(.browser-default):disabled,
input[type='text']:not(.browser-default)[readonly='readonly'],
input[type='password']:not(.browser-default):disabled,
input[type='password']:not(.browser-default)[readonly='readonly'],
input[type='email']:not(.browser-default):disabled,
input[type='email']:not(.browser-default)[readonly='readonly'],
input[type='url']:not(.browser-default):disabled,
input[type='url']:not(.browser-default)[readonly='readonly'],
input[type='time']:not(.browser-default):disabled,
input[type='time']:not(.browser-default)[readonly='readonly'],
input[type='date']:not(.browser-default):disabled,
input[type='date']:not(.browser-default)[readonly='readonly'],
input[type='datetime']:not(.browser-default):disabled,
input[type='datetime']:not(.browser-default)[readonly='readonly'],
input[type='datetime-local']:not(.browser-default):disabled,
input[type='datetime-local']:not(.browser-default)[readonly='readonly'],
input[type='tel']:not(.browser-default):disabled,
input[type='tel']:not(.browser-default)[readonly='readonly'],
input[type='number']:not(.browser-default):disabled,
input[type='number']:not(.browser-default)[readonly='readonly'],
input[type='search']:not(.browser-default):disabled,
input[type='search']:not(.browser-default)[readonly='readonly'],
textarea.materialize-textarea:disabled,
textarea.materialize-textarea[readonly='readonly'] {
    color: var(--color-dark-text-muted)
}

.input-field>label {
    color: var(--color-dark-text-muted);
}

@media only screen and (min-width: 601px) {

    nav,
    nav .nav-wrapper i,
    nav a.sidenav-trigger,
    nav a.sidenav-trigger i {
        height: 64px;
        line-height: 64px;
    }
}