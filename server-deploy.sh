#!/bin/bash


# 太空物体模拟平台 - 服务器部署脚本
# 使用方法: ./server-deploy.sh

set -e

# 项目路径
PROJECT_PATH="/home/<USER>/keeptrack.space"

echo "=== 太空物体模拟平台 - 服务器部署 ==="
echo "时间: $(date)"
echo "项目路径: $PROJECT_PATH"
echo ""

# 检查项目路径是否存在
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 错误: 项目路径不存在: $PROJECT_PATH"
    echo "请确保项目已正确部署到指定路径"
    exit 1
fi

# 切换到项目目录
echo "切换到项目目录..."
cd "$PROJECT_PATH"
echo "当前目录: $(pwd)"
echo ""

# 检查Node.js版本
echo "检查Node.js版本..."
node_version=$(node --version)
echo "Node.js版本: $node_version"

# 检查npm版本
echo "检查npm版本..."
npm_version=$(npm --version)
echo "npm版本: $npm_version"

# 检查并安装依赖
echo ""
echo "检查项目依赖..."
if [ ! -d "node_modules" ]; then
    echo "node_modules 不存在，安装项目依赖..."
    npm install
else
    echo "✅ node_modules 已存在，跳过依赖安装"
fi

# 检查ES配置文件
echo ""
echo "检查ES配置文件..."
if [ ! -f "es-config.enc" ]; then
    echo "❌ 错误: es-config.enc 文件不存在"
    echo "请先运行: node create-secure-config.js"
    exit 1
fi
echo "✅ ES配置文件存在"

# 设置环境变量（可选）
if [ -z "$ES_ENCRYPTION_KEY" ]; then
    echo "⚠️  提示: 建议设置环境变量 ES_ENCRYPTION_KEY 来增强安全性"
    echo "    export ES_ENCRYPTION_KEY='your-secret-key-32-chars-long!!'"
fi

# 启动后端API服务
echo ""
echo "启动后端API服务..."
echo "API服务地址: http://localhost:3001"
echo "健康检查: http://localhost:3001/api/health"
echo "ES历史数据: http://localhost:3001/api/es-history"
echo ""

# 使用PM2启动（如果可用）
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动后端服务..."
    pm2 start "npm run start:api" --name "keeptrack-api" --time
    echo "✅ 后端服务已启动 (PM2)"
    echo "查看日志: pm2 logs keeptrack-api"
    echo "停止服务: pm2 stop keeptrack-api"
    echo "重启服务: pm2 restart keeptrack-api"
else
    echo "使用npm直接启动后端服务..."
    echo "按 Ctrl+C 停止服务"
    npm run start:api
fi

echo ""
echo "=== 部署完成 ===" 