<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="canonical" href="http//www.SpaceDefense.cn" />
  <meta name="description" content="" />

  <meta property="og:title" content="太空物体模拟平台" />
  <meta property="og:site_name" content="太空物体模拟平台" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="http//www.SpaceDefense.cn" />
  <meta property="og:image" content="" />
  <meta property="og:image:type" content="image/jpg" />
  <meta property="og:image:width" content="1920" />
  <meta property="og:image:height" content="1080" />
  <meta property="og:description" content="" />

  <link rel="manifest" href="./manifest.webmanifest" />
  <meta name="theme-color" content="var(--color-dark-background)" />
  <link rel="shortcut icon" type="image/x-icon" href="./img/favicons/favicon.ico?v=10.0.0" />

  <link rel="apple-touch-icon" href="./img/favicons/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="太空物体模拟平台" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,
        maximum-scale=1.0, user-scalable=no" />
  <meta http-equiv="CACHE-CONTROL" content="NO-CACHE" />
  <title>太空物体模拟平台</title>

  <!-- 🔒 优先加载认证检查脚本，确保在页面内容加载前进行认证验证 -->
  <script src="js/auth-check.js"></script>

  <!-- 🔧 性能监控工具 -->
  <script src="js/performance-monitor.js"></script>

  <!-- 🚫 强制认证检查：立即执行，阻止未认证访问 -->
  <script>
    (function() {
      // 立即检查认证状态，不等待任何其他脚本
      const token = localStorage.getItem('authToken');
      const currentPath = window.location.pathname;
      const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

      console.log('🔒 主页强制认证检查...', '路径:', currentPath, '是主页:', isMainPage, '有token:', !!token);

      // 如果是主页且没有token，立即跳转
      if (isMainPage && !token) {
        console.log('❌ 主页访问被拒绝：未找到认证令牌');
        // 立即跳转，不显示任何内容
        window.location.replace('/login.html');
        // 阻止页面继续加载
        document.write('<div style="display:none;">Redirecting to login...</div>');
        throw new Error('Authentication required');
      }

      // 如果有token，允许访问（简化验证）
      if (isMainPage && token) {
        console.log('✅ 找到认证令牌，允许访问主页');

        // 🔧 简化验证：只检查token存在即可，不进行复杂的格式验证
        // 这样可以兼容不同类型的token（JWT、简单token等）

        // 🔒 异步验证已暂时禁用，避免与其他认证检查冲突
        // 主要的认证逻辑由 auth-check.js 和 keeptrack.ts 处理
        console.log('✅ Token存在，允许页面加载，详细验证由其他模块处理');

        // 检查是否是刚登录（通过URL参数判断）
        const urlParams = new URLSearchParams(window.location.search);
        const justLoggedIn = urlParams.get('login') === 'success';

        if (justLoggedIn) {
          console.log('🎉 检测到登录成功，清除URL参数');
          // 清除URL中的登录成功标记
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }

      }
    })();
  </script>

  <script type="application/ld+json">
      {
        "@context": "www.KeepTrack.cn",
        "@type": "website",
        "name": "太空物体模拟平台",
        "author": {
          "@type": "Organization",
          "url": "www.KeepTrack.cn",
          "name": "北京星地探索科技有限公司"
        },
        "description": "",
        "image": "www.KeepTrack.cn",
        "url": "www.KeepTrack.cn",

      }
    </script>
  <!-- 认证检查脚本已在 head 部分优先加载 -->

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // DOM loaded
      console.log('✅ 主页DOM加载完成');
    });

    // 移除过于严格的页面可见性和焦点检查
    // 这些检查会在用户切换桌面或应用时误判，导致不必要的登录跳转
    console.log('✅ 认证检查已简化，避免切换桌面时的误判');
  </script>
  <!-- 终极菜单修复CSS通过webpack动态加载，无需在此引用 -->

  <!-- 🔥🔥🔥 终极字体修复 - 超高优先级 🔥🔥🔥 -->
  <style id="ultimate-font-fix">
    /* 🎯 超级暴力字体修复 - 覆盖所有可能的干扰 */
    html body * #custom-sensor-menu .input-field label,
    html body * [id$="-menu"] .input-field label,
    html body * .side-menu-parent .input-field label,
    html body * .input-field label {
        font-size: 16px !important;  /* 改为16px */
        font-size: 1rem !important;  /* 改为1rem */
        color: #b3e5fc !important;
        font-weight: 400 !important;
        top: -2px !important;  /* 向上移动2px，增加与输入框的距离 */
        position: absolute !important;
        left: 0 !important;
        pointer-events: none !important;
        z-index: 1 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        cursor: default !important;
        opacity: 1 !important;
        transition: all 0.2s ease-out !important;
        transform-origin: 0 0 !important;
    }

    /* 针对激活状态的标签 */
    html body * .input-field label.active {
        font-size: 16px !important;
        font-size: 1rem !important;
        color: #b3e5fc !important;
        transform: translateY(-0.8rem) scale(0.85) !important;
        top: 0px !important;
    }

    /* 覆盖Materialize的所有可能规则 */
    label,
    .input-field label,
    .input-field > label,
    .input-field.col > label {
        font-size: 16px !important;
        color: #b3e5fc !important;
    }

    /* 🎯 专门针对选择框标签的强制规则 */
    .input-field .select-wrapper + label,
    .input-field .select-wrapper ~ label,
    .input-field:has(.select-wrapper) > label,
    [id$="-menu"] .input-field .select-wrapper + label,
    [id$="-menu"] .input-field .select-wrapper ~ label,
    [id$="-menu"] .input-field:has(.select-wrapper) > label {
        font-size: 16px !important;  /* 改为16px */
        font-size: 1rem !important;  /* 改为1rem */
        color: #b3e5fc !important;
        font-weight: 400 !important;
        top: -2px !important;  /* 向上移动2px，增加与输入框的距离 */
        position: absolute !important;
        left: 0 !important;
        pointer-events: none !important;
        z-index: 1 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        cursor: default !important;
        opacity: 1 !important;
        transition: all 0.2s ease-out !important;
        transform-origin: 0 0 !important;
    }

    /* ✅ 完美35px输入框高度 */
    .input-field input[type="text"],
    .input-field input[type="number"],
    .input-field input[type="email"],
    .input-field input[type="password"] {
        height: 35px !important;
        margin-top: 3px !important;
        padding-top: 4px !important;
        padding-bottom: 4px !important;
        font-size: 16px !important;
        line-height: 1.4 !important;
    }

    /* ✅ 选择框与输入框一致的样式 */
    .input-field .select-dropdown,
    .select-wrapper input.select-dropdown {
        height: 35px !important;
        min-height: 35px !important;
        margin-top: 3px !important;
        padding-top: 4px !important;
        padding-bottom: 4px !important;
        font-size: 16px !important;  /* 选择框内容16px，与标签一致 */
        line-height: 1.4 !important;
        box-sizing: border-box !important;
    }

    /* ✅ 完美容器间距 */
    .input-field {
        padding-top: 6px !important;
        margin-bottom: 5px !important;  /* 缩短参数间距为一半 */
    }

    /* 🎯 选择框下拉窗口优化 */
    .dropdown-content {
        max-height: 300px !important;
        min-width: 150px !important;
        width: auto !important;
        background: var(--color-dark-background) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
        overflow-y: auto !important;
    }

    .dropdown-content li {
        min-height: 40px !important;  /* 设置为40px */
        line-height: 1.5 !important;  /* 设置行高 */
        padding: 8px 16px !important;  /* 设置内边距 */
        color: white !important;
        background: transparent !important;
        display: flex !important;
        align-items: center !important;  /* 垂直居中对齐 */
        font-size: 16px !important;  /* 设置字体大小 */
    }

    .dropdown-content li:hover,
    .dropdown-content li.active {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .dropdown-content li > a,
    .dropdown-content li > span {
        font-size: 16px !important;
        color: white !important;
        line-height: 1.6 !important;  /* 增加行高 */
        padding: 8px 0 !important;  /* 增加内边距 */
        display: block !important;
        width: 100% !important;
    }

    /* 🚫 sat-info-box 例外规则 - 标签保持14px */
    #sat-infobox .sat-info-key,
    html body #sat-infobox .sat-info-key,
    html body div#sat-infobox .sat-info-key {
        font-size: 14px !important;
        color: white !important;
        font-weight: 400 !important;
    }

    /* 🎯 设置菜单完整修复方案 - 基于根因分析 */

    /* 🔥 关键修复1：修复异常高度的开关元素（ECF空白的真正原因） */
    #settings-menu .switch.row {
        height: 25px !important;        /* 强制限制开关高度为25px */
        max-height: 25px !important;    /* 防止异常增高 */
        min-height: 25px !important;    /* 确保最小高度 */
        margin-bottom: 8px !important;  /* 合理间距 */
        margin-top: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 0px !important;
        overflow: hidden !important;    /* 防止内容溢出 */
        line-height: 25px !important;   /* 文字垂直居中 */
    }

    /* 🔥 关键修复2：ECF选择框容器优化 */
    #settings-menu .input-field.col.s12 {
        margin-bottom: 2px !important;  /* 减少底部间距 */
        margin-top: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 2px !important;    /* 减少顶部内边距 */
        height: auto !important;
        min-height: 40px !important;    /* 确保最小高度 */
    }

    /* 🔥 关键修复3：选择框包装器 */
    #settings-menu .select-wrapper {
        margin-bottom: 0px !important;
        margin-top: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 0px !important;
        height: 35px !important;
        min-height: 35px !important;
        max-height: 35px !important;    /* 防止异常增高 */
    }

    /* 移除错误的强制显示下拉框CSS */

    /* 🔥 关键修复5：下拉框选项样式 */
    #settings-menu .dropdown-content li {
        height: 35px !important;        /* 每个选项35px高 */
        line-height: 35px !important;   /* 文字垂直居中 */
        padding: 0 15px !important;     /* 左右内边距 */
        font-size: 14px !important;     /* 字体大小 */
    }

    /* 🔥 关键修复6：原生select样式 */
    #settings-menu select {
        height: 35px !important;
        line-height: 35px !important;
        font-size: 14px !important;
        padding: 0 8px !important;
    }

    /* 修复普通行间距 */
    #settings-menu .row {
        margin-bottom: 3px !important;
        margin-top: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 0px !important;
    }

    /* 移除了调试样式 */
  </style>



  <script src="settings/settingsOverride.js"></script>

  <!-- 移除了不合理的定时器和所有调试代码，改用纯CSS解决方案 -->




</head>

<body>
  <!-- 🔒 页面内容加载前的最后一道认证检查 -->
  <script>
    (function() {
      const token = localStorage.getItem('authToken');
      const currentPath = window.location.pathname;
      const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

      // 对主页进行严格检查
      if (isMainPage) {
        if (!token) {
          console.log('❌ 页面内容加载被阻止：缺少认证令牌');
          document.body.innerHTML = `
            <div style="
              position: fixed; top: 0; left: 0; width: 100%; height: 100%;
              background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
              color: white; display: flex; align-items: center; justify-content: center;
              font-family: 'Microsoft YaHei', Arial, sans-serif; z-index: 10000;
            ">
              <div style="text-align: center;">
                <h2>🔒 访问受限</h2>
                <p>需要登录才能访问此页面</p>
                <p>正在跳转到登录页面...</p>
              </div>
            </div>
          `;
          setTimeout(() => window.location.replace('/login.html'), 500);
          return;
        }

        // 简单检查token是否存在即可
        console.log('✅ 认证令牌存在，允许加载页面内容');
      }
    })();
  </script>

  <div id="keeptrack-root" class="no-text-select"
    style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; margin: 0;"></div>

  <!-- 🔍 调试：脚本加载状态检查 -->
  <script>
    console.log('🔍 DEBUG: 开始加载外部脚本...');
    window.debugInfo = {
      scriptsLoaded: [],
      errors: [],
      startTime: Date.now()
    };

    // 全局错误捕获
    window.addEventListener('error', function(e) {
      console.error('❌ DEBUG: 全局JavaScript错误:', e.error);
      console.error('❌ DEBUG: 错误文件:', e.filename);
      console.error('❌ DEBUG: 错误行号:', e.lineno);
      console.error('❌ DEBUG: 错误列号:', e.colno);
      window.debugInfo.errors.push({
        type: 'javascript',
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno,
        error: e.error
      });
    });

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(e) {
      console.error('❌ DEBUG: 未处理的Promise拒绝:', e.reason);
      window.debugInfo.errors.push({
        type: 'promise',
        reason: e.reason
      });
    });
  </script>

  <!-- 统一配置加载器 - 暂时禁用以避免404错误 -->
  <!-- <script src="js/config-loader.js" onload="console.log('✅ config-loader.js 加载成功'); window.debugInfo.scriptsLoaded.push('config-loader')" onerror="console.error('❌ config-loader.js 加载失败'); window.debugInfo.errors.push('config-loader')"></script> -->

  <!-- 强制透明菜单脚本 - 暂时禁用以避免404错误 -->
  <!-- <script src="js/force-transparent.js" onload="console.log('✅ force-transparent.js 加载成功'); window.debugInfo.scriptsLoaded.push('force-transparent')" onerror="console.error('❌ force-transparent.js 加载失败'); window.debugInfo.errors.push('force-transparent')"></script> -->

  <!-- 🔍 调试：主应用初始化检查 -->
  <script>
    console.log('🔍 DEBUG: 外部脚本加载完成，准备初始化主应用...');
    console.log('🔍 DEBUG: 已加载脚本:', window.debugInfo.scriptsLoaded);
    console.log('🔍 DEBUG: 脚本错误:', window.debugInfo.errors);

    // 检查关键对象是否存在
    setTimeout(() => {
      console.log('🔍 DEBUG: 检查关键对象...');
      console.log('🔍 DEBUG: window.configLoader 存在:', !!window.configLoader);
      console.log('🔍 DEBUG: 主应用脚本加载耗时:', Date.now() - window.debugInfo.startTime, 'ms');

      // 检查主应用是否开始加载
      setTimeout(() => {
        console.log('🔍 DEBUG: 5秒后检查主应用状态...');
        console.log('🔍 DEBUG: 是否有JavaScript错误:', window.debugInfo.errors.length > 0);
        console.log('🔍 DEBUG: 错误详情:', window.debugInfo.errors);

        // 检查页面是否有内容加载
        const bodyContent = document.body.innerHTML.length;
        console.log('🔍 DEBUG: 页面内容长度:', bodyContent);

        // 检查是否有canvas元素（主应用的标志）
        const canvas = document.querySelector('canvas');
        console.log('🔍 DEBUG: Canvas元素存在:', !!canvas);
      }, 5000);
    }, 100);
  </script>

</body>

</html>