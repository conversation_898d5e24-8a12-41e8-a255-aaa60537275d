#!/usr/bin/env node

/**
 * 验证强制认证配置
 * 检查主页是否正确配置了强制认证检查
 */

const fs = require('fs');

console.log('🔍 验证强制认证配置...\n');

// 检查主页配置
console.log('📄 检查主页强制认证配置...');
try {
    const indexContent = fs.readFileSync('public/index.html', 'utf8');
    
    const checks = [
        {
            name: '优先加载认证脚本',
            pattern: 'auth-check.js',
            required: true
        },
        {
            name: 'head 中的强制认证检查',
            pattern: '主页强制认证检查',
            required: true
        },
        {
            name: '立即跳转逻辑',
            pattern: 'window.location.replace',
            required: true
        },
        {
            name: 'body 中的二次检查',
            pattern: '页面内容加载前的最后一道认证检查',
            required: true
        },
        {
            name: '主页路径检查',
            pattern: 'isMainPage',
            required: true
        }
    ];
    
    let allPassed = true;
    
    checks.forEach(check => {
        if (indexContent.includes(check.pattern)) {
            console.log(`✅ ${check.name}`);
        } else {
            console.log(`❌ ${check.name}`);
            if (check.required) allPassed = false;
        }
    });
    
    // 检查认证检查的位置
    const lines = indexContent.split('\n');
    let authCheckLine = -1;
    let bodyLine = -1;
    
    lines.forEach((line, index) => {
        if (line.includes('auth-check.js')) {
            authCheckLine = index + 1;
        }
        if (line.includes('<body>')) {
            bodyLine = index + 1;
        }
    });
    
    if (authCheckLine > 0 && authCheckLine < 50) {
        console.log(`✅ 认证脚本在 head 部分优先加载 (第${authCheckLine}行)`);
    } else {
        console.log(`❌ 认证脚本位置不正确 (第${authCheckLine}行)`);
        allPassed = false;
    }
    
    if (allPassed) {
        console.log('\n🎉 主页强制认证配置正确！');
    } else {
        console.log('\n⚠️ 主页强制认证配置有问题！');
    }
    
} catch (error) {
    console.log('❌ 无法读取主页文件');
}

// 检查认证脚本配置
console.log('\n🔐 检查认证脚本强化配置...');
try {
    const authContent = fs.readFileSync('public/js/auth-check.js', 'utf8');
    
    const scriptChecks = [
        {
            name: '立即执行认证检查',
            pattern: 'immediateAuthCheck',
            required: true
        },
        {
            name: '主页路径强制检查',
            pattern: 'isMainPage',
            required: true
        },
        {
            name: '强制执行认证检查日志',
            pattern: '强制执行认证检查',
            required: true
        },
        {
            name: 'DOM加载后认证检查',
            pattern: 'DOM加载后认证检查',
            required: true
        }
    ];
    
    let scriptPassed = true;
    
    scriptChecks.forEach(check => {
        if (authContent.includes(check.pattern)) {
            console.log(`✅ ${check.name}`);
        } else {
            console.log(`❌ ${check.name}`);
            if (check.required) scriptPassed = false;
        }
    });
    
    if (scriptPassed) {
        console.log('\n🎉 认证脚本强化配置正确！');
    } else {
        console.log('\n⚠️ 认证脚本强化配置有问题！');
    }
    
} catch (error) {
    console.log('❌ 无法读取认证脚本');
}

// 生成测试指南
console.log('\n🧪 测试强制认证的步骤:');
console.log('1. 清除浏览器所有缓存和本地存储');
console.log('2. 使用无痕模式打开浏览器');
console.log('3. 访问 http://localhost:8080/test-force-auth.html');
console.log('4. 点击"清除认证令牌"按钮');
console.log('5. 点击"测试主页访问"按钮');
console.log('6. 应该立即跳转到登录页面，不显示主页内容');

console.log('\n📋 预期行为:');
console.log('✅ 访问主页时立即检查认证令牌');
console.log('✅ 没有令牌时立即跳转到登录页面');
console.log('✅ 不显示主页的任何内容');
console.log('✅ 浏览器控制台显示认证检查日志');

console.log('\n⚠️ 如果仍然可以直接访问主页:');
console.log('1. 检查浏览器是否禁用了 JavaScript');
console.log('2. 检查是否有浏览器扩展干扰');
console.log('3. 尝试不同的浏览器');
console.log('4. 确保清除了所有缓存');
console.log('5. 检查控制台是否有错误信息');

console.log('\n🔧 故障排除命令:');
console.log('node verify-force-auth.js  # 验证配置');
console.log('node debug-auth.js         # 调试认证系统');
console.log('npm run start:api          # 启动认证服务器');
console.log('npm start                  # 启动前端服务器');
