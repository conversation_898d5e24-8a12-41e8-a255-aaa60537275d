#sat-info-title {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  color: #4A90E2 !important;
  min-height: calc(45px / var(--system-scale-factor));
  height: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: center;
  padding-bottom: calc(5px / var(--system-scale-factor));
}

#sat-add-watchlist,
#sat-remove-watchlist {
  width: calc(25px / var(--system-scale-factor));
  height: calc(25px / var(--system-scale-factor));
  cursor: pointer;
}

#sat-add-watchlist {
  filter: hue-rotate(275deg) brightness(1.2);
}

#sat-remove-watchlist {
  filter: invert(52%) sepia(130%) saturate(7323%) hue-rotate(353deg) brightness(100%) contrast(86%);
}

/* 卫星名字样式 - 加粗加大，红色字体 */
#sat-info-title-name {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  font-weight: bold !important;
  color: #219ff3 !important;
  text-align: center;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: calc((100% - 80px) / var(--system-scale-factor)); /* 为左右按钮留出空间 */
  white-space: normal; /* 允许换行 */
  padding: calc(2px / var(--system-scale-factor)) 0; /* 增加上下内边距 */
}

/* 国旗图标样式 */
#sat-infobox-fi {
  width: calc(24px / var(--system-scale-factor)) !important;  /* 缩小国旗宽度 */
  height: calc(18px / var(--system-scale-factor)) !important; /* 缩小国旗高度 */
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}

.sat-info-section-header {
  height: calc(25px / var(--system-scale-factor));
  font-size: calc(18px / var(--system-scale-factor));
  text-align: center;
  background: var(--color-dark-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--colorWhite);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: calc(5px / var(--system-scale-factor));
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sat-info-section-header:hover {
  background: #0d47a1;
}

/* 🔥 整个sat-infobox应该可以拖动 */
#sat-infobox {
  cursor: move !important; /* 改为move光标，表示可拖动 */
  user-select: none !important; /* 防止文本选择干扰拖动 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
  /* 🔥 确保拖动时不受CSS约束 */
  transition: none !important;
}

/* 🔥🔥🔥 确保Draggabilly可以正常工作 - 超高优先级 */
html body #sat-infobox.is-dragging,
#sat-infobox.is-dragging {
  position: absolute !important;
  z-index: 9999 !important;
  /* 🔥 移除位置强制设置，让Draggabilly控制位置 */
  transition: none !important;
}

/* 顶部卫星名字标题应该可以拖动 */
#sat-info-title {
  cursor: move !important; /* 改为move光标，表示可拖动 */
  user-select: none !important; /* 防止文本选择干扰拖动 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

#sat-info-title:hover {
  background: rgba(0, 26, 51, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

#sat-infobox a {
  color: white;
}

.sat-info-row {
  margin-bottom: 3px;
  padding: 1px 10px;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  align-items: baseline !important; /* 改为baseline对齐 */
  min-height: 24px !important; /* 确保有足够的高度 */
  line-height: 1.4 !important; /* 统一行高 */
  box-sizing: border-box !important;
}

.sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important; /* 🔥 蓝色高亮背景 */
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
}

/* 🔥 为可点击的元素添加更明显的悬停效果 */
.sat-info-row[onclick]:hover,
.sat-info-row[data-tooltip]:hover {
  background: rgba(33, 150, 243, 0.5) !important; /* 更明显的蓝色高亮 */
  cursor: pointer !important;
}

/* 🔥 为按钮和链接添加悬停效果 */
#sat-infobox button:hover,
#sat-infobox .clickable:hover {
  background: rgba(33, 150, 243, 0.4) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: all 0.2s ease !important;
}

#sat-site {
  max-height: 40px;
}

#sat-site-row {
  max-height: 48px !important; /* 允许两行内容 */
  min-height: 24px !important;
}

.sat-info-key,
.sat-info-value {
  display: block; /* 改为block，配合flex布局 */
  float: none; /* 移除float，使用flex布局 */
}

.sat-info-key {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;  /* 🔧 使用缩放变量 */
  color: white;
  flex: 0 0 auto; /* 不伸缩，自动宽度 */
  margin-right: calc(10px / var(--system-scale-factor, 1)); /* 与值之间的间距 */
  max-width: 50%; /* 最大宽度限制 */
  word-wrap: break-word; /* 长文本换行 */
  line-height: calc(20px / var(--system-scale-factor, 1)) !important; /* 🔧 与值部分保持一致的行高 */
  vertical-align: baseline !important; /* 统一垂直对齐 */
}

.sat-infobox-links {
  font-size: small;
  padding: 2px 10px;
  color: var(--color-dark-text-accent);
}

.section-collapse {
  cursor: pointer;
  color: var(--color-dark-text-accent);
  width: 25px;
  height: 25px;
}

#sat-info-close-btn {
  transition: color 0.2s ease, background-color 0.2s ease;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#sat-info-close-btn:hover {
  color: #ff4444 !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.section-collapse:hover {
  background-color: var(--color-dark-text-accent);
  color: white;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.section-collapse:active {
  background-color: var(--color-primary-dark);
  color: white;
  scale: 0.9;
}

#search-links {
  margin-top: 5px;
}

#actions-section,
#launch-section,
#sat-identifier-data,
#orbital-section,
#secondary-sat-info,
#sensor-sat-info,
#sat-mission-data {
  transition: max-height 0.5s ease-in-out;
  max-height: 1000px;
  /* Set this to a value larger than the content's height */
  overflow: hidden;
  margin: 0px; /* 🔥 移除margin，避免缝隙 */
  padding: 0px; /* 🔥 移除padding，避免缝隙 */
}

#actions-section.collapsed,
#launch-section.collapsed,
#sat-identifier-data.collapsed,
#orbital-section.collapsed,
#secondary-sat-info.collapsed,
#sensor-sat-info.collapsed,
#sat-mission-data.collapsed {
  max-height: 25px;
  transition: max-height 0.25s ease-in-out;
  margin: 0px; /* 🔥 收纳时也移除margin，避免缝隙 */
  padding: 0px; /* 🔥 收纳时也移除padding，避免缝隙 */
}

/* 🔥 修复菜单标题部分的间距，避免缝隙 */
.sat-info-section-header {
  margin: 0px !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  background: rgba(13, 71, 161, 0.8) !important;
  color: white !important;
  font-weight: bold !important;
  min-height: calc(25px / var(--system-scale-factor, 1)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important; /* 🔥 文字居中 */
  text-align: center !important; /* 🔥 确保文字居中 */
}

/* 🔥 修复折叠按钮位置 */
.sat-info-section-header .section-collapse {
  position: absolute !important;
  right: calc(10px / var(--system-scale-factor, 1)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* 🔧 超超超高优先级透明背景修复 - 无色透明模糊效果 */
html[lang="zh-CN"] body div#sat-infobox,
html[lang] body div#sat-infobox,
html body div#sat-infobox,
body div#sat-infobox,
div#sat-infobox,
#sat-infobox {
  display: none;
  position: absolute !important; /* 使用absolute，允许拖动 */
  user-select: none !important; /* 防止文本选择干扰拖动 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  /* cursor: default !important; 移除光标控制，让Draggabilly处理 */
  /* 🔥 移除强制位置设置，让Draggabilly控制位置 */
  height: fit-content;
  max-height: 80%;
  width: 90%;
  max-width: 500px;
  border: none !important;
  border-width: 0 !important;
  box-shadow: none !important;
  overflow: auto; /* 恢复为auto，保持滚动功能 */
  z-index: 1001 !important; /* 提高z-index确保可拖动 */
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

/* 🔧 sat-info-title保持深蓝色不透明 */
html[lang] body #sat-info-title,
html body #sat-info-title,
body #sat-info-title,
#sat-info-title {
  background: rgba(0, 26, 51, 0.8) !important;
  background-color: rgba(0, 26, 51, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* 🔧 确保所有sat-info-box内部元素也使用透明背景 */
html[lang] body #sat-infobox .sat-info-section-header,
html body #sat-infobox .sat-info-section-header,
body #sat-infobox .sat-info-section-header,
#sat-infobox .sat-info-section-header {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* 🔧 只针对主容器背景透明，不影响其他元素 */
html[lang="zh-CN"] body div#sat-infobox:not(#sat-info-title),
html[lang] body div#sat-infobox:not(#sat-info-title),
html body div#sat-infobox:not(#sat-info-title),
body div#sat-infobox:not(#sat-info-title),
div#sat-infobox:not(#sat-info-title),
#sat-infobox:not(#sat-info-title) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

.sat-info-value {
  flex: 1 1 auto; /* 占据剩余空间 */
  text-align: right; /* 右对齐 */
  min-height: calc(20px / var(--system-scale-factor, 1));
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap; /* 防止换行导致重叠 */
  font-size: calc(16px / var(--system-scale-factor, 1)) !important; /* 🔧 与标签字体一致，使用缩放变量 */
  color: white; /* 确保颜色一致 */
  line-height: calc(20px / var(--system-scale-factor, 1)) !important; /* 🔧 统一行高，使用缩放变量 */
  vertical-align: baseline !important; /* 统一垂直对齐 */
  padding: 0 calc(10px / var(--system-scale-factor, 1)) 0 calc(10px / var(--system-scale-factor, 1)) !important; /* 🔧 使用缩放变量 */
}

/* 确保上标和下标不影响行高 */
.sat-info-value sup,
.sat-info-value sub {
  font-size: 10px !important;
  line-height: 0 !important;
  vertical-align: baseline !important;
  position: relative;
}

.sat-info-value sup {
  top: -0.3em;
}

.sat-info-value sub {
  bottom: -0.2em;
}

/* 特殊处理发射场行的嵌套div结构 */
#sat-site-row .sat-info-value {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  justify-content: center !important;
}

#sat-site-row .sat-info-value div {
  font-size: 14px !important;  /* 改为14px，与标签字体一致 */
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 固定头部样式 */
#sat-info-header {
  position: sticky;
  top: 0;
  z-index: 10;
  /* 移除强制透明，让CSS控制背景 */
}

/* 可滚动内容区域 - 让父容器处理滚动 */

/* 强制覆盖响应式CSS中的冲突样式 */
@media (min-width: 640px) {
  .sat-info-value {
    float: none !important;
    width: auto !important;
    padding: 0 10px 0 10px !important;
    text-align: right !important;
    flex: 1 1 auto !important;
  }
}

@media (min-width: 1024px) {
  .sat-info-value {
    float: none !important;
    width: auto !important;
    padding: 0 10px 0 10px !important;
    text-align: right !important;
    flex: 1 1 auto !important;
  }
}

/* ========================================
 * sat-info-box 标签字体强制设置
 * ======================================== */

/* 🔧 强制设置sat-info-box中所有标签使用缩放变量 */
#sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-key,
html body div#sat-infobox .sat-info-key {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  color: white !important;
  font-weight: 400 !important;
}

/* NORAD编号值显示为蓝色 */
#sat-objnum,
html body #sat-objnum,
html body div#sat-objnum {
  color: #2196F3 !important;
  font-weight: bold !important;
}