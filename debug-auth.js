#!/usr/bin/env node

/**
 * 认证系统调试脚本
 * 帮助诊断为什么主页可以不登录就访问
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 认证系统调试分析...\n');

// 检查主页配置
console.log('📄 检查主页配置...');
try {
    const indexContent = fs.readFileSync('public/index.html', 'utf8');
    
    if (indexContent.includes('auth-check.js')) {
        console.log('✅ 主页已引入认证检查脚本');
    } else {
        console.log('❌ 主页未引入认证检查脚本');
    }
    
    // 检查脚本引入位置
    const lines = indexContent.split('\n');
    lines.forEach((line, index) => {
        if (line.includes('auth-check.js')) {
            console.log(`   脚本位置: 第${index + 1}行`);
            console.log(`   内容: ${line.trim()}`);
        }
    });
    
} catch (error) {
    console.log('❌ 无法读取主页文件');
}

// 检查认证脚本
console.log('\n🔐 检查认证脚本...');
try {
    const authContent = fs.readFileSync('public/js/auth-check.js', 'utf8');
    
    // 检查关键函数
    const checks = [
        { name: 'DOMContentLoaded 事件监听', pattern: 'DOMContentLoaded' },
        { name: '路径检查逻辑', pattern: 'currentPath' },
        { name: 'token 检查', pattern: 'authToken' },
        { name: '跳转逻辑', pattern: 'window.location.href' },
        { name: '认证验证', pattern: 'checkAuth' }
    ];
    
    checks.forEach(check => {
        if (authContent.includes(check.pattern)) {
            console.log(`✅ ${check.name}`);
        } else {
            console.log(`❌ ${check.name}`);
        }
    });
    
    // 检查是否有立即跳转逻辑
    if (authContent.includes('立即跳转到登录页面')) {
        console.log('✅ 包含立即跳转逻辑');
    } else {
        console.log('⚠️  缺少立即跳转逻辑');
    }
    
} catch (error) {
    console.log('❌ 无法读取认证脚本');
}

// 检查可能的问题
console.log('\n🔍 可能的问题分析...');

const possibleIssues = [
    {
        issue: '认证服务器未运行',
        description: '如果认证服务器没有启动，认证检查会失败',
        solution: '运行: npm run start:api'
    },
    {
        issue: '浏览器缓存问题',
        description: '旧版本的脚本可能被缓存',
        solution: '清除浏览器缓存或强制刷新 (Ctrl+F5)'
    },
    {
        issue: 'JavaScript 错误',
        description: '脚本执行过程中可能有错误',
        solution: '检查浏览器控制台的错误信息'
    },
    {
        issue: '脚本加载顺序',
        description: '认证脚本可能在其他脚本之后加载',
        solution: '将认证脚本移到 <head> 部分的最前面'
    },
    {
        issue: 'localStorage 中有无效 token',
        description: '可能存在无效的认证令牌',
        solution: '清除 localStorage 或使用无痕模式测试'
    }
];

possibleIssues.forEach((item, index) => {
    console.log(`${index + 1}. ${item.issue}`);
    console.log(`   问题: ${item.description}`);
    console.log(`   解决: ${item.solution}\n`);
});

// 生成测试步骤
console.log('🧪 建议的测试步骤:');
console.log('1. 打开浏览器无痕模式');
console.log('2. 访问 http://localhost:8080/test-redirect.html');
console.log('3. 检查是否立即跳转到登录页面');
console.log('4. 如果没有跳转，按 F12 查看控制台错误');
console.log('5. 检查网络请求是否成功');

console.log('\n📋 快速修复建议:');
console.log('1. 确保认证服务器正在运行 (端口 3001)');
console.log('2. 清除浏览器缓存和 localStorage');
console.log('3. 使用无痕模式测试');
console.log('4. 检查浏览器控制台错误信息');

// 检查端口占用
console.log('\n🌐 检查服务器状态...');
console.log('请手动检查以下端口是否有服务运行:');
console.log('- 端口 3001: 认证 API 服务器');
console.log('- 端口 8080: 前端 Web 服务器');
console.log('\n可以使用以下命令检查:');
console.log('Windows: netstat -an | findstr :3001');
console.log('Linux/Mac: lsof -i :3001');
