/**
 * 测试更新后的GEO卫星经度历史API
 */

const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function testUpdatedGeoAPI() {
  console.log('开始测试更新后的GEO卫星经度历史API...\n');
  
  try {
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 30000,
    });

    // 1. 直接测试ES查询（模拟API查询）
    console.log('1. 直接测试ES查询（模拟API查询）');
    
    const today = new Date();
    const threeMonthsAgo = new Date(today);
    threeMonthsAgo.setMonth(today.getMonth() - 3);

    const startDatetime = threeMonthsAgo.toISOString().split('T')[0] + 'T00:00:00Z';
    const endDatetime = today.toISOString().split('T')[0] + 'T23:59:59Z';
    
    console.log(`查询时间范围: ${startDatetime} 到 ${endDatetime}`);
    
    // 构建查询条件（使用正确的字段路径）
    const query = {
      bool: {
        must: [
          { range: { time: { gte: startDatetime, lte: endDatetime } } },
          { term: { "orbital_elements.object_type": "GEO" } },
          { exists: { field: "orbital_elements.subsat_long" } }
        ]
      }
    };
    
    console.log('查询条件:', JSON.stringify(query, null, 2));
    
    const searchResult = await client.search({
      index: esConfig.index,
      size: 10,
      query: query,
      sort: [
        { time: 'asc' },
        { norad_id: 'asc' }
      ],
      _source: [
        'norad_id',
        'time',
        'satellite_name',
        'orbital_elements.object_type',
        'orbital_elements.subsat_long'
      ]
    });
    
    console.log(`\n找到 ${searchResult.hits.total.value} 条GEO卫星记录`);
    
    if (searchResult.hits?.hits?.length > 0) {
      console.log('\n前10条记录:');
      searchResult.hits.hits.forEach((hit, index) => {
        const record = hit._source;
        console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name}`);
        console.log(`   时间: ${record.time}`);
        console.log(`   类型: ${record.orbital_elements?.object_type}`);
        console.log(`   经度: ${record.orbital_elements?.subsat_long}°`);
        console.log('');
      });
      
      // 2. 测试API接口
      console.log('2. 测试API接口');
      
      const baseUrl = 'http://localhost:3001';
      const startDate = threeMonthsAgo.toISOString().split('T')[0];
      const endDate = today.toISOString().split('T')[0];
      
      console.log(`API查询: ${baseUrl}/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
      
      try {
        const fetch = require('node-fetch');
        const response = await fetch(`${baseUrl}/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`✓ API请求成功，返回 ${data.data?.length || 0} 条记录`);
          
          if (data.data && data.data.length > 0) {
            console.log('\nAPI返回的前3条记录:');
            data.data.slice(0, 3).forEach((record, index) => {
              console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name || record.object_name}`);
              console.log(`   时间: ${record.time}`);
              console.log(`   类型: ${record.object_type}`);
              console.log(`   经度: ${record.subsat_long}°`);
              console.log('');
            });
          }
        } else {
          console.log(`✗ API请求失败: ${response.status} ${response.statusText}`);
          const errorText = await response.text();
          console.log('错误信息:', errorText);
        }
      } catch (apiError) {
        console.log('✗ API请求异常:', apiError.message);
        console.log('请确保API服务器正在运行 (npm run start:api)');
      }
      
    } else {
      console.log('未找到符合条件的GEO卫星数据');
      
      // 检查是否有任何GEO类型的记录
      console.log('\n检查是否有任何GEO类型的记录...');
      const geoCheck = await client.search({
        index: esConfig.index,
        size: 1,
        query: {
          term: { "orbital_elements.object_type": "GEO" }
        }
      });
      
      console.log(`数据库中共有 ${geoCheck.hits.total.value} 条GEO卫星记录`);
      
      if (geoCheck.hits?.hits?.length > 0) {
        const sample = geoCheck.hits.hits[0]._source;
        console.log('示例GEO记录:');
        console.log(`  NORAD: ${sample.norad_id}`);
        console.log(`  名称: ${sample.satellite_name}`);
        console.log(`  时间: ${sample.time}`);
        console.log(`  经度: ${sample.orbital_elements?.subsat_long}`);
      }
    }

  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
testUpdatedGeoAPI().catch(console.error);
