/**
 * 测试字段映射配置和GEO卫星查询
 */

const fs = require('fs');
const http = require('http');

async function testFieldMapping() {
  console.log('开始测试字段映射配置和GEO卫星查询...\n');
  
  // 测试1: 检查字段映射配置文件
  console.log('测试1: 检查字段映射配置文件');
  const configFile = 'es-field-mapping.json';
  if (fs.existsSync(configFile)) {
    try {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      console.log('✓ 字段映射配置文件存在且格式正确');
      console.log('✓ 配置的索引名:', config.elasticsearch.index);
      console.log('✓ GEO卫星类型值:', config.elasticsearch.geo_satellite.object_type_value);
      console.log('✓ 经度字段名:', config.elasticsearch.geo_satellite.longitude_field);
      console.log('✓ 最大记录数限制:', config.elasticsearch.query_limits.max_records);
    } catch (error) {
      console.log('✗ 字段映射配置文件格式错误:', error.message);
      return false;
    }
  } else {
    console.log('✗ 字段映射配置文件不存在');
    return false;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试2: 测试API是否使用了字段映射
  console.log('测试2: 测试GEO卫星查询API');
  const baseUrl = 'http://localhost:3001';
  
  try {
    // 测试查询所有GEO卫星（最近1天的数据）
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    const startDate = yesterday.toISOString().split('T')[0];
    const endDate = today.toISOString().split('T')[0];
    
    console.log(`查询时间范围: ${startDate} 到 ${endDate}`);
    
    const response = await fetch(`${baseUrl}/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
    
    if (!response.ok) {
      console.log(`✗ API请求失败: ${response.status} ${response.statusText}`);
      return false;
    }
    
    const data = await response.json();
    
    if (data.data && data.data.length > 0) {
      console.log(`✓ 成功获取 ${data.data.length} 条记录`);
      
      // 检查返回的数据结构
      const firstRecord = data.data[0];
      console.log('✓ 数据字段检查:');
      console.log(`  - norad_id: ${firstRecord.norad_id ? '✓' : '✗'}`);
      console.log(`  - time: ${firstRecord.time ? '✓' : '✗'}`);
      console.log(`  - object_type: ${firstRecord.object_type ? '✓' : '✗'}`);
      console.log(`  - object_name: ${firstRecord.object_name ? '✓' : '✗'}`);
      console.log(`  - subsat_long: ${firstRecord.subsat_long !== undefined ? '✓' : '✗'}`);
      
      // 检查是否都是GEO卫星
      const geoRecords = data.data.filter(record => record.object_type === 'GEO');
      console.log(`✓ GEO卫星记录: ${geoRecords.length}/${data.data.length}`);
      
      // 检查经度数据
      const withLongitude = data.data.filter(record => 
        record.subsat_long !== null && 
        record.subsat_long !== undefined && 
        record.subsat_long !== '' &&
        !isNaN(parseFloat(record.subsat_long))
      );
      console.log(`✓ 有效经度数据: ${withLongitude.length}/${data.data.length}`);
      
      if (withLongitude.length > 0) {
        const sampleLongitude = parseFloat(withLongitude[0].subsat_long);
        console.log(`✓ 示例经度值: ${sampleLongitude}° (${sampleLongitude > 0 ? '东经' : '西经'})`);
      }
      
    } else {
      console.log('! 未找到GEO卫星数据，可能原因:');
      console.log('  - 数据库中没有GEO卫星数据');
      console.log('  - 字段名映射不正确');
      console.log('  - 查询时间范围内无数据');
      console.log('  - object_type字段值不是"GEO"');
      
      // 尝试查询任意数据来检查字段
      console.log('\n尝试查询任意数据来检查字段...');
      const anyDataResponse = await fetch(`${baseUrl}/api/es-history?norad_id=25544&start=${startDate}&end=${endDate}`);
      if (anyDataResponse.ok) {
        const anyData = await anyDataResponse.json();
        if (anyData.data && anyData.data.length > 0) {
          console.log('✓ 数据库连接正常，找到数据');
          console.log('✓ 示例记录字段:', Object.keys(anyData.data[0]));
          
          const sampleRecord = anyData.data[0];
          if (sampleRecord.object_type) {
            console.log(`✓ object_type值: "${sampleRecord.object_type}"`);
          }
          if (sampleRecord.subsat_long !== undefined) {
            console.log(`✓ subsat_long值: "${sampleRecord.subsat_long}"`);
          }
        } else {
          console.log('✗ 数据库中无任何数据');
        }
      }
    }
    
  } catch (error) {
    console.log('✗ API测试失败:', error.message);
    return false;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试3: 建议的字段映射检查
  console.log('测试3: 字段映射建议');
  console.log('如果上述测试失败，请检查以下配置:');
  console.log('1. 确认ES数据库中的实际字段名');
  console.log('2. 更新 es-field-mapping.json 中的字段映射');
  console.log('3. 确认GEO卫星的object_type值');
  console.log('4. 确认经度字段名和数据格式');
  
  console.log('\n建议的调试步骤:');
  console.log('1. 直接查询ES数据库获取一条记录');
  console.log('2. 检查返回的字段名和值');
  console.log('3. 相应更新配置文件');
  
  return true;
}

// 运行测试
if (require.main === module) {
  testFieldMapping().catch(console.error);
}

module.exports = { testFieldMapping };
