/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * history-track.ts is a plugin for viewing historical satellite track data
 * and analyzing orbital parameters over time.
 *
 * http://www.spacesecure.cn
 *
 * @Copyright 北京星地探索科技有限公司
 *
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { MenuMode, ToastMsgType, KeepTrackApiEvents } from '@app/interfaces';
import { BaseObject, DetailedSatellite } from 'ootk';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import historyPng from '@public/img/icons/history.png';
import { KeepTrackPlugin } from '../KeepTrackPlugin';


export class HistoryTrackPlugin extends KeepTrackPlugin {
  constructor() {
    super();
  }

  init(): void {
    super.init();
  }

  // 当点击底部图标时的回调
  bottomIconCallback = (): void => {
    this.updateNoradFromSelectedSat();

    // 修复日期选择器
    setTimeout(() => {
      this.fixDatePickers();
    }, 100);
  };

  private fixDatePickers(): void {
    const startDateInput = document.getElementById('history-start-date') as HTMLInputElement;
    const endDateInput = document.getElementById('history-end-date') as HTMLInputElement;

    if (startDateInput && !startDateInput.dataset.pickerFixed) {
      // 标记已处理，避免重复添加事件监听器
      startDateInput.dataset.pickerFixed = 'true';

      // 确保日期输入框可以正常点击
      startDateInput.style.pointerEvents = 'auto';
      startDateInput.style.cursor = 'pointer';
      startDateInput.style.position = 'relative';
      startDateInput.style.zIndex = '1000';

      // 添加点击事件确保日历能弹出
      startDateInput.addEventListener('click', function(e) {
        e.stopPropagation();
        this.focus();
        // 使用 try-catch 包装 showPicker 调用，避免错误
        try {
          if (this.showPicker && typeof this.showPicker === 'function') {
            this.showPicker();
          }
        } catch (error) {
          // 如果 showPicker 失败，忽略错误，让浏览器使用默认行为
          console.debug('showPicker failed, using default behavior:', error);
        }
      });
    }

    if (endDateInput && !endDateInput.dataset.pickerFixed) {
      // 标记已处理，避免重复添加事件监听器
      endDateInput.dataset.pickerFixed = 'true';

      // 确保日期输入框可以正常点击
      endDateInput.style.pointerEvents = 'auto';
      endDateInput.style.cursor = 'pointer';
      endDateInput.style.position = 'relative';
      endDateInput.style.zIndex = '1000';

      // 添加点击事件确保日历能弹出
      endDateInput.addEventListener('click', function(e) {
        e.stopPropagation();
        this.focus();
        // 使用 try-catch 包装 showPicker 调用，避免错误
        try {
          if (this.showPicker && typeof this.showPicker === 'function') {
            this.showPicker();
          }
        } catch (error) {
          // 如果 showPicker 失败，忽略错误，让浏览器使用默认行为
          console.debug('showPicker failed, using default behavior:', error);
        }
      });
    }
  }



  readonly id = 'HistoryTrackPlugin';
  protected dependencies_: string[] = [];
  menuMode: MenuMode[] = [MenuMode.ANALYSIS, MenuMode.ALL];
  bottomIconImg = historyPng;
  bottomIconLabel = '历史轨道';
  bottomIconElementName = 'menu-history-track';
  sideMenuElementName = 'history-track-menu';
  sideMenuElementHtml = keepTrackApi.html`
  <style>
  #history-parameter {
    width: calc(260px / var(--system-scale-factor, 1));
    height: calc(320px / var(--system-scale-factor, 1));
    font-size: calc(12px / var(--system-scale-factor, 1));
    white-space: nowrap;
    overflow-x: auto;
  }
  #history-parameter option {
    font-size: calc(12px / var(--system-scale-factor, 1));
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  #history-parameter::-webkit-scrollbar {
    width: calc(8px / var(--system-scale-factor, 1));
  }
  #history-parameter::-webkit-scrollbar-thumb {
    background: #2196f3;
    border-radius: calc(4px / var(--system-scale-factor, 1));
  }
  #history-parameter::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  </style>
  <div id="history-track-menu" class="side-menu-parent start-hidden text-select" style="width: 100vw; min-width: 100vw; max-width: 100vw;">
    <!-- 右上角关闭按钮 -->
    <div id="history-track-close-btn" style="position: fixed; top: 20px; right: 20px; z-index: 99999; width: 40px; height: 40px; background: transparent; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 28px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="history-track-inner-menu" class="side-menu" style="width: 100vw;">
      <h5 class="center-align">历史轨道数据</h5>
      <div class="divider"></div>
      <div class="row"></div>
      <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 10px; width: 100vw;">
        <form id="history-track-form" style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100vw; gap: 0;">
          <div style="display: flex; align-items: center; margin-right: 24px;">
            <label for="history-norad-id" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">NORAD编号</label>
            <input value="25544" id="history-norad-id" type="text" placeholder="多目标用,或者空格分割" style="width: 220px; height: 32px; margin-right: 16px; font-size: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box;" />
          </div>
          <div style="display: flex; align-items: center; margin-right: 24px;">
            <label for="history-start-date" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">开始日期</label>
            <input id="history-start-date" type="date" value="2025-01-12" style="width: 160px; height: 32px; margin-right: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box;" />
          </div>
          <div style="display: flex; align-items: center; margin-right: 24px;">
            <label for="history-end-date" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">结束日期</label>
            <input id="history-end-date" type="date" value="2025-07-12" style="width: 160px; height: 32px; margin-right: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box;" />
          </div>

          <button id="history-get-data" class="btn btn-ui waves-effect waves-light" type="submit" name="action" style="height: 32px; line-height: 32px; margin-left: 8px; padding: 0 16px; box-sizing: border-box;">获取数据 &#9658;</button>
        </form>

        </div>
      <!-- 参数按钮容器移到外面，始终可见 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin: 5px 60px 5px 60px;">
        <!-- 卫星选择按钮容器，左侧 -->
        <div id="satellite-buttons-container" style="display: none; justify-content: flex-start; align-items: center; gap: 6px; flex-wrap: nowrap;">
          <div id="satellite-buttons" style="display: flex; gap: 6px; justify-content: flex-start; align-items: center; flex-wrap: nowrap;">
            <!-- 卫星按钮将在这里动态生成 -->
          </div>
        </div>
        <!-- 参数选择按钮容器，右侧 -->
        <div id="param-buttons-container" style="display: none; justify-content: flex-end; align-items: center; gap: 6px; flex-wrap: wrap;">
        <!-- 硬编码按钮，优化尺寸和响应性，修正参数键匹配 -->
        <button type="button" class="param-btn" value="arg_alt_km" data-selected="true" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #d500f9; background: #d500f9; color: #fff; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">轨道高度</button>
        <button type="button" class="param-btn" value="inc_deg" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #ff5252; background: transparent; color: #ff5252; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">倾角</button>
        <button type="button" class="param-btn" value="ecc" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #ffd740; background: transparent; color: #ffd740; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">偏心率</button>
        <button type="button" class="param-btn" value="raan_deg" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #40c4ff; background: transparent; color: #40c4ff; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">升交点赤经</button>
        <button type="button" class="param-btn" value="arg_peri_deg" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #69f0ae; background: transparent; color: #69f0ae; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">近地点幅角</button>
        <button type="button" class="param-btn" value="mean_anom_deg" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #b388ff; background: transparent; color: #b388ff; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">平近点角</button>
        <button type="button" class="param-btn" value="orbital_period_min" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #ffb300; background: transparent; color: #ffb300; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">轨道周期</button>
        <button type="button" class="param-btn" value="sema_km" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #00bfae; background: transparent; color: #00bfae; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">半长轴</button>
        <!-- 暂时屏蔽星下点经度按钮 -->
        <!-- <button type="button" class="param-btn" value="sub_lon_deg" data-selected="false" onclick="window.historyTrackButtonClick(this)" style="border: 1px solid #ff6b35; background: transparent; color: #ff6b35; border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none;">星下点经度</button> -->
        </div>
      </div>
      <div id="history-results" class="row" style="display: none;">
        <div class="col s12">
          <div id="history-stats-title" class="center-align" style="font-size: 17px; font-weight: bold; margin: 12px 0 8px 0;"></div>
          <div id="history-chart-container" style="height: 1080px; width: 100%; margin: 10px 0; position: relative;">
            <div id="history-chart-stats" style="color:#fff;font-size:15px;padding:0 10px 6px 10px;display:flex;justify-content:space-between;"></div>
            <canvas id="history-chart" width="1200" height="800" style="width: 100%; height: calc(100% - 20px);"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
  `;

  addHtml(): void {
    super.addHtml();

    // 确保菜单和canvas父容器可见，移除start-hidden
    const menu = document.getElementById('history-track-menu');
    if (menu) menu.classList.remove('start-hidden');
    
    // 强制显示结果容器
    const results = document.getElementById('history-results');
    if (results) {
      results.style.display = 'block';
      results.style.visibility = 'visible';
    }
    
    // 强制显示图表容器
    const chartContainer = document.getElementById('history-chart-container');
    if (chartContainer) {
      chartContainer.style.display = 'block';
      chartContainer.style.visibility = 'visible';
      chartContainer.style.width = '100%';
      chartContainer.style.height = '800px';
    }
    
    // 强制显示canvas
    const canvas = document.getElementById('history-chart');
    if (canvas) {
      canvas.style.display = 'block';
      canvas.style.visibility = 'visible';
      canvas.style.width = '100%';
      canvas.style.height = '100%';
    }

    // 强制显示按钮容器
    const btnContainer = document.getElementById('param-buttons-container');
    if (btnContainer) {
      btnContainer.style.display = 'flex';
      btnContainer.style.visibility = 'visible';
    }



  }

  // 数据视图状态管理
  private dataView = {
    yMin: 0,
    yMax: 1000,
    yRange: 1000,
    xMin: 0,
    xMax: 1,
    xRange: 1,
  };
  // 全局缩放比例
  private scale = 1;
  // 全局范围
  private globalXMin = 0;
  private globalXMax = 1;
  // 参数列表
  private paramList = [
    { key: 'inc_deg', color: '#ff5252', label: '倾角(度)' },
    { key: 'raan_deg', color: '#40c4ff', label: '升交点赤经(度)' },
    { key: 'ecc', color: '#ffd740', label: '偏心率' },
    { key: 'arg_peri_deg', color: '#69f0ae', label: '近地点幅角(度)' },
    { key: 'mean_anom_deg', color: '#b388ff', label: '平近点角(度)' },
    { key: 'orbital_period_min', color: '#ffb300', label: '轨道周期(分钟)' },
    { key: 'sema_km', color: '#00bfae', label: '半长轴(公里)' },
    { key: 'arg_alt_km', color: '#d500f9', label: '轨道高度(公里)' }
    // 暂时屏蔽星下点经度
    // { key: 'sub_lon_deg', color: '#ff6b35', label: '星下点经度(度)' }
  ];
  // 鼠标悬停点位后显示对应数值和属性
  private lastTooltip: {x: number, y: number, text: string}[] | {x: number, y: number, text: string} | null = null;

  private _lastTooltipText: string | null = null;
  private _chartYParams: { globalMin: number, globalMax: number, valueRange: number, yZoom: number, height: number, padding: number } | null = null;
  // 新增：多参数缓存
  private lastChartData: { timestamps: string[], values: (number | null)[] } | null = null;
  private lastChartParam: string | null = null;
  private lastAllData: any[] | null = null;
  // 新增：多参数缓存
  private lastChartDataArr: { timestamps: string[], values: (number | null)[], satelliteId?: string, color?: string, paramKey?: string, satelliteName?: string }[] | null = null;
  private lastChartParams: string[] | null = null;
  // 1. 在类中添加属性
  private selectedCurveIndex: number | null = null;

  private _onWheelBound: ((e: WheelEvent) => void) | null = null;

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器 - 使用事件委托确保能工作
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'history-track-close-btn') {
        e.preventDefault();
        e.stopPropagation();

        // 隐藏历史轨道菜单
        const menu = document.getElementById('history-track-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById('menu-history-track');
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
      }
    });

    document.addEventListener('submit', (e) => {
      const form = e.target as HTMLFormElement;
      if (form && form.id === 'history-track-form') {
        e.preventDefault();
        this.handleGetData_();
      }
    }, true);

    // 延迟创建全局函数处理按钮点击，确保插件完全初始化
    setTimeout(() => {
      (window as any).historyTrackButtonClick = (button: HTMLButtonElement) => {
        this.handleButtonClick(button);
      };
    }, 50);

    // 监听卫星选择事件，自动填充NORAD编号
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, (obj: BaseObject) => {
      if (this.isMenuButtonActive && obj?.isSatellite()) {
        const sat = obj as DetailedSatellite;
        this.updateNoradInput(sat.sccNum);
      }
    });

    // 初始化时从当前选中的卫星更新NORAD编号（延迟执行，确保DOM已准备好）
    setTimeout(() => {
      this.updateNoradFromSelectedSat();
    }, 100);





    // Canvas事件将在图表显示后绑定

    // 新增：监听窗口resize，自动重绘
    window.addEventListener('resize', () => {
      if (this.lastChartDataArr && this.lastChartParams && this.lastAllData) {
        this.displayChartMulti_(this.lastChartDataArr, this.lastChartParams, this.lastAllData, false);
      } else if (this.lastChartData && this.lastChartParam && this.lastAllData) {
        this.displayChart_(this.lastChartData, this.lastChartParam, this.lastAllData);
      }
    });
    // 自定义多选下拉菜单逻辑
    setTimeout(() => {
      const multiselect = document.getElementById('history-parameter');
      if (multiselect) {
        const selectedDiv = multiselect.querySelector('.custom-multiselect-selected') as HTMLDivElement;
        const dropdown = multiselect.querySelector('.custom-multiselect-dropdown') as HTMLDivElement;
        if (dropdown) {
          const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]') as NodeListOf<HTMLInputElement>;
          // 展开/收起
          selectedDiv.onclick = () => {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
          };
          // 点击外部收起
          document.addEventListener('click', (e) => {
            if (!multiselect.contains(e.target as Node)) {
              dropdown.style.display = 'none';
            }
          });
          // 选中项显示
          function updateSelectedText() {
            const selected = Array.from(checkboxes).filter(cb => cb.checked).map(cb => cb.parentElement?.textContent?.trim() || '').filter(Boolean);
            selectedDiv.textContent = selected.length ? selected.join('，') : '请选择参数';
          }
          checkboxes.forEach(cb => {
            cb.addEventListener('change', () => {
              updateSelectedText();
              // 参数变更时立即更新图表
              (window as any).historyTrackPluginInstance?.handleGetData_();
            });
          });
          updateSelectedText();
        }
      }
    }, 1000);
    // 参数按钮组点击事件，切换曲线显示/隐藏
    setTimeout(() => {
      const btnGroup = document.getElementById('param-btn-group');
      if (btnGroup) {
        const btns = btnGroup.querySelectorAll('button.param-btn');
        btns.forEach((b) => {
          const btn = b as HTMLButtonElement;
          const paramKey = btn.value;
          const param = this.paramList.find(p => p.key === paramKey);
          if (!param) return;

          btn.addEventListener('click', () => {
            // 切换选中状态
            if (btn.dataset.selected === 'true') {
              // 取消选中：变为透明背景，保持边框颜色
              btn.style.background = 'transparent';
              btn.style.color = param.color;
              btn.style.opacity = '0.5';
              btn.dataset.selected = 'false';
            } else {
              // 选中：填充背景色
              btn.style.background = param.color;
              btn.style.color = '#fff';
              btn.style.opacity = '1';
              btn.dataset.selected = 'true';
            }

            // 获取所有选中的参数key
            const selectedParams = Array.from(btnGroup.querySelectorAll('button.param-btn[data-selected="true"]')).map(b => (b as HTMLButtonElement).value);

            // 如果有数据，重新渲染图表
            if (this.lastAllData && this.lastAllData.length > 0) {
              if (selectedParams.length === 0) {
                // 如果没有选中任何参数，清空图表
                const canvas = document.getElementById('history-chart') as HTMLCanvasElement;
                if (canvas) {
                  const ctx = canvas.getContext('2d');
                  if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                  }
                }
                return;
              }

              // 重新生成选中参数的图表数据
              const chartDataArr = selectedParams.map(param => this.extractChartData_(this.lastAllData!, param));
              // 参数切换时强制更新Y轴范围到合适范围
              this.displayChartMulti_(chartDataArr, selectedParams, this.lastAllData!, false, true);
            }
          });
        });
      }
    }, 300); // 减少延迟
  }

  private bindCanvasEvents_(canvas: HTMLCanvasElement) {
    const self = this;

    // 移除之前的事件监听器
    if (this._onWheelBound) {
      canvas.removeEventListener('wheel', this._onWheelBound, true);
    }

    // 缩放
    this._onWheelBound = (e: WheelEvent) => this._onWheel(e, canvas);
    canvas.addEventListener('wheel', this._onWheelBound, true);

    // 拖拽平移数据区间
    let isDragging = false;
    let lastMouseX = 0;
    let lastMouseY = 0;
    let redrawTimeout: ReturnType<typeof setTimeout> | null = null;
    
    // 防抖重绘函数 - 减少延迟
    const debouncedRedraw = () => {
      if (redrawTimeout) {
        clearTimeout(redrawTimeout);
      }
      redrawTimeout = setTimeout(() => {
        if (self.lastChartDataArr && self.lastChartParams && self.lastAllData) {
          self.displayChartMulti_(self.lastChartDataArr as any, self.lastChartParams, self.lastAllData, false);
        } else if (self.lastChartData && self.lastChartParam && self.lastAllData) {
          self.displayChart_(self.lastChartData, self.lastChartParam, self.lastAllData);
        }
        redrawTimeout = null;
      }, 8); // 减少到8ms，约120fps，提高响应性
    };
    
    canvas.addEventListener('mousedown', function (e: MouseEvent) {
      if (e.button === 0) { // 左键
        isDragging = true;
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;
        canvas.style.cursor = 'grabbing';
      }
    });
    
    canvas.addEventListener('mousemove', function (e: MouseEvent) {
      if (isDragging) {
        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;
        
        // 计算像素到数据区间的转换比例
        const padding = 60;
        const bottomPadding = 50;
        const rightPadding = 30;
        const dpr = window.devicePixelRatio || 1;
        const chartWidth = (canvas.width / dpr) - padding - rightPadding;
        const chartHeight = (canvas.height / dpr) - padding - bottomPadding;
        const xDataPerPixel = self.dataView.xRange / chartWidth * 0.7;
        const yDataPerPixel = self.dataView.yRange / chartHeight * 0.7;
        const xDataStep = deltaX * xDataPerPixel;
        const yDataStep = deltaY * yDataPerPixel;
        
        self.dataView.xMin -= xDataStep;
        self.dataView.xMax -= xDataStep;
        self.dataView.xRange = self.dataView.xMax - self.dataView.xMin;
        self.dataView.yMin += yDataStep;
        self.dataView.yMax += yDataStep;
        self.dataView.yRange = self.dataView.yMax - self.dataView.yMin;
        
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;
        
        // 直接重绘，确保实时响应
        if (self.lastChartDataArr && self.lastChartParams && self.lastAllData) {
          self.displayChartMulti_(self.lastChartDataArr, self.lastChartParams, self.lastAllData, false);
        } else if (self.lastChartData && self.lastChartParam && self.lastAllData) {
          self.displayChart_(self.lastChartData, self.lastChartParam, self.lastAllData);
        }
      } else {
        // 简化的tooltip逻辑 - 只在鼠标移动时计算，不触发重绘
        if (self.lastChartDataArr && self.lastChartParams && self.lastAllData) {
          const rect = canvas.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          const { height, padding } = self._chartYParams || { height: 800, padding: 60 };
          const dpr = window.devicePixelRatio || 1;
          const canvasWidth = canvas.width / dpr;
          
          // 简化的tooltip计算 - 支持多卫星
          const tooltips: any[] = [];
          self.lastChartDataArr.forEach((data, idx) => {
            const paramKey = (data as any).paramKey || self.lastChartParams![idx];
            const param = self.paramList.find(p => p.key === paramKey) || self.paramList[idx] || self.paramList[0];
            const satelliteId = (data as any).satelliteId || 'unknown';
            const values = data.values as number[];
            
            // 根据图表缩放程度调整采样密度
            const sampleStep = self.dataView.xRange < 0.1 ? 1 : self.dataView.xRange < 0.5 ? 5 : 10;
            let minDist = 99999;
            let found: any = null;
            for (let i = 0; i < values.length; i += sampleStep) {
              const value = values[i];
              if (typeof value !== 'number' || isNaN(value)) continue;
              
              const dataIndex = i / (values.length - 1);
              const px = padding + (canvasWidth - 2 * padding) * (dataIndex - self.dataView.xMin) / self.dataView.xRange;
              const py = height - padding - (height - 2 * padding) * (value - self.dataView.yMin) / self.dataView.yRange;
              const dist = Math.sqrt((px - x) ** 2 + (py - y) ** 2);
              
              // 根据缩放程度动态调整检测距离，更精准
              const detectionRadius = self.dataView.xRange < 0.1 ? 25 : self.dataView.xRange < 0.5 ? 20 : 15;
              if (dist < minDist && dist < detectionRadius) {
                minDist = dist;
                let timeStr = data.timestamps[i];
                if (timeStr && timeStr.length >= 16) {
                  timeStr = timeStr.slice(0, 16);
                  timeStr = timeStr.replace('T', ' ');
                }
                found = { x: px, y: py, text: `${(data as any).satelliteName || `卫星${satelliteId}`}(${satelliteId}) ${param.label}: ${value.toFixed(4)}\n时间: ${timeStr}` };
              }
            }
            if (found) tooltips.push(found);
          });
          
          const tooltipText = tooltips.map(t => t.text).join(' | ');
          if (tooltipText !== self._lastTooltipText) {
            self.lastTooltip = tooltips.length > 0 ? tooltips : null;
            self._lastTooltipText = tooltipText;
            // 直接更新tooltip，确保实时响应
            if (self.lastChartDataArr && self.lastChartParams && self.lastAllData) {
              self.displayChartMulti_(self.lastChartDataArr, self.lastChartParams, self.lastAllData, false);
            } else if (self.lastChartData && self.lastChartParam && self.lastAllData) {
              self.displayChart_(self.lastChartData, self.lastChartParam, self.lastAllData);
            }
          }
        }
      }
    });
    
    canvas.addEventListener('mouseup', function (e: MouseEvent) {
      if (e.button === 0) {
        isDragging = false;
        canvas.style.cursor = 'default';
      }
    });
    
    canvas.addEventListener('mouseleave', function () {
      isDragging = false;
      canvas.style.cursor = 'default';
      self.lastTooltip = null;
      self._lastTooltipText = null;
      debouncedRedraw();
    });

    // 双击重置数据视图
    canvas.addEventListener('dblclick', function () {
      // 重置为数据全范围
      if (self.lastChartData && self.lastChartData.values.length > 0) {
        const validValues = self.lastChartData.values.filter((v): v is number => typeof v === 'number' && !isNaN(v));
        const globalMin = Math.min(...validValues);
        const globalMax = Math.max(...validValues);
        const range = globalMax - globalMin;
        const minPixelMargin = 2;
        const yPixelPerValue = (canvas.height / (window.devicePixelRatio || 1) - 2 * 60) / Math.max(1, range);
        const minMarginByPixel = minPixelMargin / Math.max(1, yPixelPerValue);
        const yMargin = Math.max(range * 0.05, minMarginByPixel);
        self.dataView = {
          yMin: globalMin - yMargin,
          yMax: globalMax + yMargin,
          yRange: globalMax - globalMin + 2 * yMargin,
          xMin: 0,
          xMax: 1,
          xRange: 1,
        };
      }
      
      // 直接重绘缩放，确保实时响应
      if (self.lastChartDataArr && self.lastChartParams && self.lastAllData) {
        self.displayChartMulti_(self.lastChartDataArr, self.lastChartParams, self.lastAllData, true);
      } else if (self.lastChartData && self.lastChartParam && self.lastAllData) {
        self.displayChart_(self.lastChartData, self.lastChartParam, self.lastAllData);
      }
    });

    // 2. 在bindCanvasEvents_最后添加canvas点击事件
    canvas.addEventListener('click', (e: MouseEvent) => {
      if (!self.lastChartDataArr) return;
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const { height, padding } = self._chartYParams || { height: 800, padding: 60 };
      const dpr = window.devicePixelRatio || 1;
      const canvasWidth = canvas.width / dpr;
      let minDist = 99999;
      let foundIdx: number | null = null;
      self.lastChartDataArr.forEach((data, idx) => {
        const values = data.values as number[];
        for (let i = 0; i < values.length; i += 2) { // 采样加速
          const value = values[i];
          if (typeof value !== 'number' || isNaN(value)) continue;
          const px = padding + (canvasWidth - padding - 30) * (i / (values.length - 1) - self.dataView.xMin) / self.dataView.xRange;
          const py = height - padding - (height - 2 * padding) * (value - self.dataView.yMin) / self.dataView.yRange;
          const dist = Math.sqrt((px - x) ** 2 + (py - y) ** 2);
          // 根据缩放程度动态调整选中阈值
          const selectionRadius = self.dataView.xRange < 0.1 ? 30 : self.dataView.xRange < 0.5 ? 25 : 20;
          if (dist < minDist && dist < selectionRadius) {
            minDist = dist;
            foundIdx = idx;
          }
        }
      });
      self.selectedCurveIndex = foundIdx;
      self.displayChartMulti_(self.lastChartDataArr as any, self.lastChartParams!, self.lastAllData!, false);
    });
  }

  private _onWheel(e: WheelEvent, canvas: HTMLCanvasElement) {
    e.preventDefault();
    const minScale = 0.01, maxScale = 1.0;
    const isZoomIn = e.deltaY < 0;
    if (isZoomIn) this.scale = Math.max(minScale, this.scale * 0.95);
    else this.scale = Math.min(maxScale, this.scale / 0.95);
    if (this.scale > 0.99) {
      this.scale = 1;
      if (this.lastChartDataArr && this.lastChartParams && this.lastAllData) {
        this.displayChartMulti_(this.lastChartDataArr, this.lastChartParams, this.lastAllData, true);
        return;
      } else if (this.lastChartData && this.lastChartParam && this.lastAllData) {
        this.displayChart_(this.lastChartData, this.lastChartParam, this.lastAllData);
        return;
      }
    }
    const rect = canvas.getBoundingClientRect();
    const mouseX = (e.clientX - rect.left) / (rect.width) * (canvas.width / (window.devicePixelRatio || 1));
    const mouseY = (e.clientY - rect.top) / (rect.height) * (canvas.height / (window.devicePixelRatio || 1));
    const padding = 60;
    const bottomPadding = 50;
    const rightPadding = 30;
    const chartWidth = (canvas.width / (window.devicePixelRatio || 1)) - padding - rightPadding;
    const chartHeight = (canvas.height / (window.devicePixelRatio || 1)) - padding - bottomPadding;
    const xRatio = (mouseX - padding) / chartWidth;
    const yRatio = 1 - (mouseY - padding) / chartHeight;
    const xData = this.dataView.xMin + xRatio * this.dataView.xRange;
    const yData = this.dataView.yMin + yRatio * this.dataView.yRange;
    // === 主图内容绘制都在clip内 ===
    // 缩放灵敏度调高
    const scaleStep = e.deltaY < 0 ? 0.7 : 1.3;
    let newYRange = this.dataView.yRange * scaleStep;
    let newYMin = yData - (yRatio) * newYRange;
    let newYMax = yData + (1 - yRatio) * newYRange;
    if (newYRange < 1e-6) newYRange = 1e-6;
    let newXRange = this.dataView.xRange * scaleStep;
    let newXMin = xData - (xRatio) * newXRange;
    let newXMax = xData + (1 - xRatio) * newXRange;
    if (newXRange < 1e-6) newXRange = 1e-6;
    this.dataView.yMin = newYMin;
    this.dataView.yMax = newYMax;
    this.dataView.yRange = newYRange;
    this.dataView.xMin = newXMin;
    this.dataView.xMax = newXMax;
    this.dataView.xRange = newXRange;
    console.log('scale:', this.scale, 'xRange:', this.dataView.xRange, 'yRange:', this.dataView.yRange);
    if (this.lastChartDataArr && this.lastChartParams) {
      this.displayChartMulti_(this.lastChartDataArr, this.lastChartParams, this.lastAllData || undefined);
    } else if (this.lastChartData) {
      this.displayChart_(this.lastChartData, this.lastChartParam!, this.lastAllData || undefined);
    }
  }

  // 缓存上次绘图数据以便缩放重绘
  private async handleGetData_(): Promise<void> {
    const rawInput = (getEl('history-norad-id') as HTMLInputElement)?.value || '';
    const noradIds = rawInput
      .split(/[\s,]+/)
      .map(id => id.trim())
      .filter(id => id.length > 0);
    const startDate = (getEl('history-start-date') as HTMLInputElement)?.value;
    const endDate = (getEl('history-end-date') as HTMLInputElement)?.value;
    if (!noradIds.length || !startDate || !endDate) {
      keepTrackApi.getUiManager()?.toast('请填写所有字段', ToastMsgType.error);
      return;
    }

    // 开始获取数据时立即显示结果容器和空图表
    const resultsContainer = document.getElementById('history-results');
    if (resultsContainer) {
      resultsContainer.style.display = 'block';
    }

    // 显示空图表，确保界面布局正确
    this.clearChart_();

    try {
      // 使用配置加载器获取API地址
      let apiBaseUrl: string;
      if ((window as any).configLoader) {
        try {
          apiBaseUrl = await (window as any).configLoader.getApiBaseUrl();
        } catch (error) {
          console.warn('无法从配置加载器获取API地址，使用默认地址:', error);
          const currentHost = window.location.hostname;
          apiBaseUrl = `http://${currentHost}:5001`;
        }
      } else {
        console.warn('配置加载器不可用，使用默认地址');
        const currentHost = window.location.hostname;
        apiBaseUrl = `http://${currentHost}:5001`;
      }

      // 组装API参数
      const params = new URLSearchParams();
      params.append('norad_id', noradIds.join(','));
      params.append('start', startDate!);
      params.append('end', endDate!);

      const apiUrl = `${apiBaseUrl}/api/satellite-history?${params.toString()}`;
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        // API请求失败时重置UI状态
        this.hideParameterButtons_();
        this.hideSatelliteButtons_();
        this.clearChart_('API请求失败，请检查网络连接');
        keepTrackApi.getUiManager()?.toast('API请求失败', ToastMsgType.error);
        return;
      }
      
      const data = await response.json();

      // 添加调试信息
      console.log('历史轨道数据API响应:', data);
      console.log('data.data存在:', !!data.data);
      console.log('data.data长度:', data.data ? data.data.length : 'N/A');
      console.log('完整响应结构:', Object.keys(data));

      if (data.data) {
        this.lastAllData = data.data;
        // 清空所有缓存和选中状态，确保强制刷新
        this.selectedCurveIndex = null;
        this.lastTooltip = null;
        this._lastTooltipText = null;
        this.lastChartDataArr = null;
        this.lastChartParams = null;
        // 确保结果容器可见
        const resultsContainer = document.getElementById('history-results');
        if (resultsContainer) {
          resultsContainer.style.display = 'block';
        }
        
        // 获取所有参数的数据，让按钮控制显示
        const activeParams = this.paramList.map(p => p.key);
        console.log('获取所有参数的数据:', activeParams);
        
        // 按卫星分组数据，并保存satellite_name字段
        const satelliteGroups = new Map<string, any[]>();
        const satelliteNames = new Map<string, string>();
        this.lastAllData!.forEach(record => {
          const noradId = record.norad_id || 'unknown';
          if (!satelliteGroups.has(noradId)) {
            satelliteGroups.set(noradId, []);
          }
          satelliteGroups.get(noradId)!.push(record);
          // 优先取satellite_name字段
          satelliteNames.set(noradId, record.satellite_name || record.name || `卫星${noradId}`);
        });

        // 为每个卫星和参数组合创建图表数据
        const chartDataArr: any[] = [];
        const satelliteIds = Array.from(satelliteGroups.keys());

        // 生成卫星选择按钮（仅在多卫星时显示）
        this.generateSatelliteButtons_(satelliteIds, satelliteNames);

        satelliteIds.forEach((satelliteId) => {
          const satelliteData = satelliteGroups.get(satelliteId)!;
          const satelliteName = satelliteNames.get(satelliteId) || `卫星${satelliteId}`;
          activeParams.forEach((param) => {
            const chartData = this.extractChartData_(satelliteData, param, satelliteId, satelliteName);
            // 使用参数对应的颜色
            const paramConfig = this.paramList.find(p => p.key === param);
            chartData.color = paramConfig ? paramConfig.color : '#ffffff';
            chartData.satelliteId = satelliteId;
            chartData.satelliteName = satelliteName;
            chartData.paramKey = param;
            chartDataArr.push(chartData);
          });
        });
        this.displayChartMulti_(chartDataArr, activeParams, this.lastAllData!, true);

        // 显示参数选择按钮
        this.showParameterButtons_();

        // 在图表显示后立即绑定canvas事件
        setTimeout(() => {
          const canvas = getEl('history-chart') as HTMLCanvasElement;
          if (canvas) {
            this.bindCanvasEvents_(canvas);
          }
        }, 100);
      } else {
        // 数据获取失败时重置UI状态
        this.hideParameterButtons_();
        this.hideSatelliteButtons_();
        this.clearChart_('获取数据失败，请检查参数设置');
        keepTrackApi.getUiManager()?.toast(data.message || '获取数据失败', ToastMsgType.error);
      }
    } catch (error) {
      // 网络请求失败时也重置UI状态
      this.hideParameterButtons_();
      this.hideSatelliteButtons_();
      this.clearChart_('网络请求失败，请检查网络连接');
      keepTrackApi.getUiManager()?.toast('网络请求失败', ToastMsgType.error);
      console.error('Error fetching history data:', error);
    }
  }

  // 修改extractChartData_签名，增加satelliteName参数和返回类型
  private extractChartData_(filteredData: any[], parameter: string, satelliteId?: string, satelliteName?: string) {
    const timestamps: string[] = [];
    const values: (number | null)[] = [];

    filteredData.forEach(record => {
      timestamps.push(record.time);

      if (parameter === 'sub_lon_deg') {
        // 暂时屏蔽星下点经度功能，避免卡死
        values.push(null);
      } else {
        // 修复数据解析：先检查orbital_elements是否存在，然后尝试直接从record获取
        let v = null;
        if (record.orbital_elements && typeof record.orbital_elements === 'object') {
          v = record.orbital_elements[parameter];
        } else {
          // 如果orbital_elements不存在，尝试直接从record获取
          v = record[parameter];
        }
        values.push(typeof v === 'number' && !isNaN(v) ? v : null);
      }
    });

    return { timestamps, values, satelliteId, satelliteName, color: '', paramKey: parameter };
  }





  // 显示参数选择按钮
  private showParameterButtons_(): void {
    const paramContainer = document.getElementById('param-buttons-container');
    if (paramContainer) {
      paramContainer.style.display = 'flex';
    }
  }

  // 隐藏参数选择按钮
  private hideParameterButtons_(): void {
    const paramContainer = document.getElementById('param-buttons-container');
    if (paramContainer) {
      paramContainer.style.display = 'none';
    }
  }

  // 隐藏卫星选择按钮
  private hideSatelliteButtons_(): void {
    const satelliteContainer = document.getElementById('satellite-buttons-container');
    if (satelliteContainer) {
      satelliteContainer.style.display = 'none';
    }
  }

  // 清空图表并绘制空的坐标轴
  private clearChart_(message: string = '等待数据加载...'): void {
    const canvas = getEl('history-chart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置canvas尺寸
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 960;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制空的坐标轴框架
    const padding = 60;
    const bottomPadding = 50;
    const rightPadding = 30;

    ctx.strokeStyle = '#444';
    ctx.lineWidth = 1;
    ctx.beginPath();
    // 左边框
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - bottomPadding);
    // 底边框
    ctx.lineTo(width - rightPadding, height - bottomPadding);
    ctx.stroke();

    // 绘制提示文字
    ctx.fillStyle = '#888';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(message, width / 2, height / 2);
  }

  // 修改displayChart_签名，支持传入显示区间
  private displayChart_(chartData: { timestamps: string[], values: (number | null)[] }, parameter: string, allData?: any[]): void {
    const canvas = getEl('history-chart') as HTMLCanvasElement;
    if (!canvas) { return; }
    const ctx = canvas.getContext('2d');
    if (!ctx) { return; }
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 960;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);
    const canvasWidth = canvas.width / dpr;
    const padding = 60;
    const bottomPadding = 50; // 覆盖到按钮区域
    const rightPadding = 30;
    const param = this.paramList.find(p => p.key === parameter) || this.paramList[0];
    // 只在首次加载新数据时重置X轴区间
    if (this.lastChartData !== chartData) {
      this.dataView.xMin = 0;
      this.dataView.xMax = 1;
      this.dataView.xRange = 1;
    }
    this.lastChartData = chartData;
    this.lastChartParam = parameter;
    this.lastAllData = allData || null;
    // 计算Y轴范围
    const validValues = chartData.values.filter((v): v is number => typeof v === 'number' && !isNaN(v));
    const globalMin = Math.min(...validValues);
    const globalMax = Math.max(...validValues);
    const valueRange = globalMax - globalMin;
    if (this.dataView.yRange === 1000) {
      const minPixelMargin = 2;
      const yPixelPerValue = (height - padding - bottomPadding) / Math.max(1, valueRange);
      const minMarginByPixel = minPixelMargin / Math.max(1, yPixelPerValue);
      const yMargin = Math.max(valueRange * 0.05, minMarginByPixel);
      this.dataView.yMin = globalMin - yMargin;
      this.dataView.yMax = globalMax + yMargin;
      this.dataView.yRange = this.dataView.yMax - this.dataView.yMin;
    }
    this._chartYParams = { globalMin, globalMax, valueRange, yZoom: 1, height, padding };
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    // 背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    // 网格
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    const gridStep = 50;
    for (let x = padding; x <= canvasWidth - rightPadding; x += gridStep) {
      ctx.beginPath(); ctx.moveTo(x, padding); ctx.lineTo(x, height - bottomPadding); ctx.stroke();
    }
    for (let y = padding; y <= height - bottomPadding; y += gridStep) {
      ctx.beginPath(); ctx.moveTo(padding, y); ctx.lineTo(canvasWidth - rightPadding, y); ctx.stroke();
    }
    // 主绘图区clip
    ctx.save();
    ctx.beginPath();
    ctx.rect(padding, padding, canvasWidth - padding - rightPadding, height - padding - bottomPadding);
    ctx.clip();
    // 数据线
    ctx.strokeStyle = param.color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    let firstPoint = true;
    for (let i = 0; i < chartData.values.length; i++) {
      const value = chartData.values[i];
      if (typeof value !== 'number' || isNaN(value)) continue;
      const x = padding + (canvasWidth - padding - rightPadding) * (i / (chartData.values.length - 1) - this.dataView.xMin) / this.dataView.xRange;
      const y = height - bottomPadding - (height - padding - bottomPadding) * (value - this.dataView.yMin) / this.dataView.yRange;
      const clampedX = Math.max(padding, Math.min(canvasWidth - rightPadding, x));
      const clampedY = Math.max(padding, Math.min(height - bottomPadding, y));
      if (firstPoint) { ctx.moveTo(clampedX, clampedY); firstPoint = false; } else { ctx.lineTo(clampedX, clampedY); }
    }
    ctx.stroke();
    // 数据点
    ctx.fillStyle = param.color;
    for (let i = 0; i < chartData.values.length; i++) {
      const value = chartData.values[i];
      if (typeof value !== 'number' || isNaN(value)) continue;
      const x = padding + (canvasWidth - padding - rightPadding) * (i / (chartData.values.length - 1) - this.dataView.xMin) / this.dataView.xRange;
      const y = height - bottomPadding - (height - padding - bottomPadding) * (value - this.dataView.yMin) / this.dataView.yRange;
      const clampedX = Math.max(padding, Math.min(canvasWidth - rightPadding, x));
      const clampedY = Math.max(padding, Math.min(height - bottomPadding, y));
      ctx.beginPath(); ctx.arc(clampedX, clampedY, 3, 0, 2 * Math.PI); ctx.fill();
    }
    ctx.restore(); // 结束clip
    // Y轴
    ctx.strokeStyle = '#ffffff'; ctx.lineWidth = 2;
    ctx.beginPath(); ctx.moveTo(padding, padding); ctx.lineTo(padding, height - bottomPadding); ctx.stroke();
    // X轴
    ctx.beginPath(); ctx.moveTo(padding, height - bottomPadding); ctx.lineTo(canvasWidth - rightPadding, height - bottomPadding); ctx.stroke();
    // Y轴刻度
    ctx.fillStyle = '#ffffff'; ctx.font = '12px Arial'; ctx.textAlign = 'right';
    const minYLabelSpacing = 32;
    const maxYTicks = Math.floor((height - padding - bottomPadding) / minYLabelSpacing);
    const yTicks = Math.max(2, Math.min(maxYTicks, 10));
    const yStep = (this.dataView.yMax - this.dataView.yMin) / yTicks;
    for (let i = 0; i <= yTicks; i++) {
      const y = padding + (height - padding - bottomPadding) * i / yTicks;
      const value = this.dataView.yMax - yStep * i;
      let label = value.toFixed(Math.abs(yStep) < 1 ? 2 : (Math.abs(yStep) < 10 ? 1 : 0));
      ctx.fillText(label, padding - 10, y + 4);
    }
    // X轴刻度
    ctx.textAlign = 'center';
    const minXLabelSpacing = 80;
    const maxXTicks = Math.floor((canvasWidth - padding - rightPadding) / minXLabelSpacing);
    const xTicks = Math.max(2, Math.min(maxXTicks, chartData.timestamps.length - 1));
    for (let i = 0; i <= xTicks; i++) {
      const dataIndex = this.dataView.xMin + (this.dataView.xMax - this.dataView.xMin) * i / xTicks;
      const timeIndex = Math.round(dataIndex * (chartData.timestamps.length - 1));
      if (timeIndex >= 0 && timeIndex < chartData.timestamps.length) {
        const timeStr = chartData.timestamps[timeIndex];
        let displayTime = '';
        if (timeStr) {
          if (xTicks > 6) displayTime = timeStr.slice(5, 16).replace('T', '\n');
          else displayTime = timeStr.slice(0, 16).replace('T', ' ');
        } else displayTime = `${timeIndex}`;
        const x = padding + (canvasWidth - padding - rightPadding) * i / xTicks;
        ctx.fillText(displayTime, x, height - bottomPadding + 12);
      }
    }
    // tooltip
    if (this.lastTooltip) {
      ctx.save();
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      ctx.fillStyle = 'rgba(0,0,0,0.8)';
      let tooltips = Array.isArray(this.lastTooltip) ? this.lastTooltip : [this.lastTooltip];
      for (const tip of tooltips) {
        const lines = tip.text.split('\n');
        let maxWidth = 0;
        for (const line of lines) maxWidth = Math.max(maxWidth, ctx.measureText(line).width);
        const boxWidth = maxWidth + 20;
        const boxHeight = lines.length * 15 + 16;
        let boxX = tip.x + 10;
        let boxY = tip.y - 30;
        if (boxX + boxWidth > canvasWidth) boxX = tip.x - boxWidth - 10;
        boxX = Math.max(padding, Math.min(boxX, canvasWidth - rightPadding - boxWidth));
        boxY = Math.max(padding, Math.min(boxY, height - padding - boxHeight));
        ctx.fillStyle = 'rgba(0,0,0,0.8)';
        ctx.fillRect(boxX, boxY, boxWidth, boxHeight);
        ctx.fillStyle = '#fff';
        for (let i = 0; i < lines.length; i++) ctx.fillText(lines[i], boxX + 10, boxY + 20 + i * 15);
      }
      ctx.restore();
    }
    // 统计信息和图例，始终在最上层
    const stats = this.calculateStats_(chartData.values);
    ctx.save();
    ctx.font = 'bold 16px Arial';
    ctx.shadowColor = 'rgba(0,0,0,0.7)';
    ctx.shadowBlur = 2;
    ctx.textAlign = 'left';
    const infoY = padding / 2;
    let infoX = padding - 29; // 左移约5mm
    // 先画白色部分
    ctx.fillStyle = '#fff';
    const prefix = `数据点: ${stats.count}    最大值: ${stats.max.toFixed(2)}    最小值: ${stats.min.toFixed(2)}    平均值: ${stats.mean.toFixed(2)}    峰值差: `;
    ctx.fillText(prefix, infoX, infoY);
    // 画橘黄色峰值差
    const prefixWidth = ctx.measureText(prefix).width;
    ctx.fillStyle = '#FFA500';
    ctx.fillText(`${(stats.max - stats.min).toFixed(2)}`, infoX + prefixWidth, infoY);
    ctx.textAlign = 'right';
    ctx.fillStyle = param.color;
    ctx.fillText(param.label, canvasWidth - rightPadding - 10, infoY);
    ctx.restore();
  }

  // 计算统计信息
  private calculateStats_(values: (number | null)[]): { count: number, max: number, min: number, mean: number } {
    const filtered = values.filter((v): v is number => typeof v === 'number' && !isNaN(v));
    if (filtered.length === 0) return { count: 0, max: 0, min: 0, mean: 0 };
    const max = Math.max(...filtered);
    const min = Math.min(...filtered);
    const mean = filtered.reduce((sum, v) => sum + v, 0) / filtered.length;
    return { count: filtered.length, max, min, mean };
  }

  // 截图功能


  // 新增多参数曲线绘制方法
  private displayChartMulti_(chartDataArr: { timestamps: string[], values: (number | null)[], satelliteId?: string, color?: string, paramKey?: string, satelliteName?: string }[], parameters: string[], allData?: any[], resetView = false, forceUpdateYRange = false): void {
    const canvas = getEl('history-chart') as HTMLCanvasElement;
    if (!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return;
    }
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 960;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);
    const canvasWidth = canvas.width / dpr;
    const padding = 60;
    const bottomPadding = 50; // 覆盖到按钮区域
    const rightPadding = 30;
    if (resetView) {
      this.dataView.xMin = 0;
      this.dataView.xMax = 1;
      this.dataView.xRange = 1;
    }
    const chartData = chartDataArr[0];
    this.lastChartData = chartData;
    this.lastChartParam = parameters[0];
    this.lastAllData = allData || null;
    this.lastChartDataArr = chartDataArr;
    this.lastChartParams = parameters;
    // 计算选中参数和卫星的Y轴范围
    const selectedParamsForRange = this.getSelectedParameters();
    let globalMin = Infinity, globalMax = -Infinity;

    chartDataArr.forEach(data => {
      // 只计算选中参数的数据范围
      if (!selectedParamsForRange.includes(data.paramKey || '')) {
        return;
      }

      // 只计算选中卫星的数据范围
      const satelliteId = String(data.satelliteId || 'unknown');
      if (!this.selectedSatellites.has(satelliteId)) {
        return;
      }

      data.values.filter((v): v is number => typeof v === 'number' && !isNaN(v)).forEach(v => {
        if (v < globalMin) globalMin = v;
        if (v > globalMax) globalMax = v;
      });
    });

    // 处理没有选中数据的情况
    if (globalMin === Infinity || globalMax === -Infinity) {
      globalMin = 0;
      globalMax = 100;
    }

    const valueRange = globalMax - globalMin;

    // 只在重置视图、首次显示或强制更新时更新Y轴范围，否则保持用户调整的范围
    if (resetView || forceUpdateYRange || (this.dataView.yMin === 0 && this.dataView.yMax === 1000)) {
      const minPixelMargin = 2;
      const yPixelPerValue = (height - 2 * padding) / Math.max(1, valueRange);
      const minMarginByPixel = minPixelMargin / Math.max(1, yPixelPerValue);
      const yMargin = Math.max(valueRange * 0.05, minMarginByPixel);
      this.dataView.yMin = globalMin - yMargin;
      this.dataView.yMax = globalMax + yMargin;
      this.dataView.yRange = this.dataView.yMax - this.dataView.yMin;
    }

    if (resetView) {
      this.globalXMin = 0;
      this.globalXMax = 1;
      this.scale = 1;
      this.dataView.xMin = this.globalXMin;
      this.dataView.xMax = this.globalXMax;
      this.dataView.xRange = this.globalXMax - this.globalXMin;
    }
    this._chartYParams = { globalMin, globalMax, valueRange, yZoom: 1, height, padding };
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    // 背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    // 网格
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    const gridStep = 50;
    for (let x = padding; x <= canvasWidth - rightPadding; x += gridStep) {
      ctx.beginPath(); ctx.moveTo(x, padding); ctx.lineTo(x, height - bottomPadding); ctx.stroke();
    }
    for (let y = padding; y <= height - bottomPadding; y += gridStep) {
      ctx.beginPath(); ctx.moveTo(padding, y); ctx.lineTo(canvasWidth - rightPadding, y); ctx.stroke();
    }
    // 主绘图区clip
    ctx.save();
    ctx.beginPath();
    ctx.rect(padding, padding, canvasWidth - padding - rightPadding, height - padding - bottomPadding);
    ctx.clip();
    // 多卫星多参数数据线 - 根据按钮选中状态过滤
    const selectedParams = this.getSelectedParameters();

    // 如果没有选中任何卫星，显示空图表
    if (this.selectedSatellites.size === 0) {
      ctx.restore();
      return;
    }

    chartDataArr.forEach((data) => {
      // 检查这个参数是否被选中
      if (!selectedParams.includes(data.paramKey || '')) {
        return;
      }

      // 检查这个卫星是否被选中，统一转换为字符串进行比较
      const satelliteId = String(data.satelliteId || 'unknown');
      if (!this.selectedSatellites.has(satelliteId)) {
        return;
      }
      const param = this.paramList.find(p => p.key === data.paramKey) || this.paramList[0];
      const color = data.color || param.color;

      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.beginPath();
      let firstPoint = true;
      for (let i = 0; i < data.timestamps.length; i++) {
        const value = data.values[i];
        if (typeof value !== 'number' || isNaN(value)) continue;
        const x = padding + (canvasWidth - padding - rightPadding) * (i / (data.timestamps.length - 1) - this.dataView.xMin) / this.dataView.xRange;
        const y = height - bottomPadding - (height - padding - bottomPadding) * (value - this.dataView.yMin) / this.dataView.yRange;
        if (firstPoint) { ctx.moveTo(x, y); firstPoint = false; } else { ctx.lineTo(x, y); }
      }
      ctx.stroke();
      ctx.fillStyle = color;
      for (let i = 0; i < data.timestamps.length; i++) {
        const value = data.values[i];
        if (typeof value !== 'number' || isNaN(value)) continue;
        const x = padding + (canvasWidth - padding - rightPadding) * (i / (data.timestamps.length - 1) - this.dataView.xMin) / this.dataView.xRange;
        const y = height - bottomPadding - (height - padding - bottomPadding) * (value - this.dataView.yMin) / this.dataView.yRange;
        ctx.beginPath(); ctx.arc(x, y, 2, 0, 2 * Math.PI); ctx.fill();
      }
    });
    ctx.restore(); // 结束clip
    // Y轴
    ctx.strokeStyle = '#ffffff'; ctx.lineWidth = 2;
    ctx.beginPath(); ctx.moveTo(padding, padding); ctx.lineTo(padding, height - bottomPadding); ctx.stroke();
    // X轴
    ctx.beginPath(); ctx.moveTo(padding, height - bottomPadding); ctx.lineTo(canvasWidth - rightPadding, height - bottomPadding); ctx.stroke();
    // Y轴刻度
    ctx.fillStyle = '#ffffff'; ctx.font = '12px Arial'; ctx.textAlign = 'right';
    const minYLabelSpacing = 32;
    const maxYTicks = Math.floor((height - padding - bottomPadding) / minYLabelSpacing);
    const yTicks = Math.max(2, Math.min(maxYTicks, 10));
    const yStep = (this.dataView.yMax - this.dataView.yMin) / yTicks;
    for (let i = 0; i <= yTicks; i++) {
      const y = padding + (height - padding - bottomPadding) * i / yTicks;
      const value = this.dataView.yMax - yStep * i;
      let label = value.toFixed(Math.abs(yStep) < 1 ? 2 : (Math.abs(yStep) < 10 ? 1 : 0));
      ctx.fillText(label, padding - 10, y + 4);
    }
    // X轴刻度
    ctx.textAlign = 'center';
    const xTicks = 10;
    for (let i = 0; i <= xTicks; i++) {
      const x = padding + (canvasWidth - padding - rightPadding) * i / xTicks;
      const dataIndex = this.dataView.xMin + (this.dataView.xMax - this.dataView.xMin) * i / xTicks;
      const timeIndex = Math.max(0, Math.min(chartData.timestamps.length - 1, Math.floor(dataIndex * (chartData.timestamps.length - 1))));
      if (timeIndex >= 0 && timeIndex < chartData.timestamps.length) {
        const timeStr = chartData.timestamps[timeIndex];
        const displayTime = timeStr ? timeStr.slice(0, 16).replace('T', ' ') : `${timeIndex}`;
        ctx.fillText(displayTime, x, height - bottomPadding + 12);
      } else {
        const relativePos = (dataIndex * 100).toFixed(0);
        ctx.fillText(`${relativePos}%`, x, height - bottomPadding + 12);
      }
    }
    // tooltip
    if (Array.isArray(this.lastTooltip) && this.lastTooltip.length > 0) {
      ctx.save();
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      this.lastTooltip.forEach((tip) => {
        const lines = tip.text.split('\n');
        let maxWidth = 0;
        for (const line of lines) maxWidth = Math.max(maxWidth, ctx.measureText(line).width);
        const boxWidth = maxWidth + 20;
        const boxHeight = lines.length * 15 + 16;
        let boxX = tip.x + 10;
        let boxY = tip.y - boxHeight - 10;
        // 优先右上方，若超出边界则调整
        if (boxX + boxWidth > canvasWidth) boxX = tip.x - boxWidth - 10;
        if (boxX < padding) boxX = padding;
        if (boxY < padding) boxY = tip.y + 20;
        if (boxY + boxHeight > height - padding) boxY = height - padding - boxHeight;
        ctx.fillStyle = 'rgba(0,0,0,0.8)';
        ctx.fillRect(boxX, boxY, boxWidth, boxHeight);
        ctx.fillStyle = '#fff';
        for (let i = 0; i < lines.length; i++) ctx.fillText(lines[i], boxX + 10, boxY + 20 + i * 15);
      });
      ctx.restore();
    } else if (this.lastTooltip && typeof this.lastTooltip === 'object' && 'text' in this.lastTooltip && 'x' in this.lastTooltip && 'y' in this.lastTooltip) {
      ctx.save();
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      const tip = this.lastTooltip as {x: number, y: number, text: string};
      const lines = tip.text.split('\n');
      let maxWidth = 0;
      for (const line of lines) maxWidth = Math.max(maxWidth, ctx.measureText(line).width);
      const boxWidth = maxWidth + 20;
      const boxHeight = lines.length * 15 + 16;
      let boxX = tip.x + 10;
      let boxY = tip.y - boxHeight - 10;
      // 优先右上方，若超出边界则调整
      if (boxX + boxWidth > canvasWidth) boxX = tip.x - boxWidth - 10;
      if (boxX < padding) boxX = padding;
      if (boxY < padding) boxY = tip.y + 20;
      if (boxY + boxHeight > height - padding) boxY = height - padding - boxHeight;
      ctx.fillStyle = 'rgba(0,0,0,0.8)';
      ctx.fillRect(boxX, boxY, boxWidth, boxHeight);
      ctx.fillStyle = '#fff';
      for (let i = 0; i < lines.length; i++) ctx.fillText(lines[i], boxX + 10, boxY + 20 + i * 15);
      ctx.restore();
    }
    // 统计信息和图例，始终在最上层
    ctx.save();
    ctx.font = '13px Arial';
    ctx.shadowColor = 'rgba(0,0,0,0.7)';
    ctx.shadowBlur = 2;
    ctx.textAlign = 'left';
    // 只显示选中曲线的统计信息
    let totalCount = 0;
    let statsMax = -Infinity;
    let statsMin = Infinity;
    let totalSum = 0;
    let stats;
    let statsPrefixColor = '#fff';
    let statsPrefixText = '';
    if (this.selectedCurveIndex != null && chartDataArr[this.selectedCurveIndex]) {
      const data = chartDataArr[this.selectedCurveIndex];
      stats = this.calculateStats_(data.values);
      totalCount = stats.count;
      statsMax = stats.max;
      statsMin = stats.min;
      totalSum = stats.mean * stats.count;
      const satelliteName = data.satelliteName || `卫星${data.satelliteId}`;
      const paramKey = data.paramKey;
      const param = this.paramList.find(p => p.key === paramKey) || this.paramList[0];
      // 格式：卫星名（norad）曲线属性
      statsPrefixText = `${satelliteName}（${data.satelliteId}）${param.label} `;
      // 使用曲线颜色
      statsPrefixColor = param.color;
    } else {
      chartDataArr.forEach(data => {
        // 只计算选中卫星的统计信息，统一转换为字符串进行比较
        const satelliteId = String(data.satelliteId || 'unknown');
        if (!this.selectedSatellites.has(satelliteId)) {
          return;
        }

        stats = this.calculateStats_(data.values);
        totalCount += stats.count;
        if (stats.max > statsMax) statsMax = stats.max;
        if (stats.min < statsMin) statsMin = stats.min;
        totalSum += stats.mean * stats.count;
      });
      statsPrefixText = '全部数据 ';
      statsPrefixColor = '#fff';
    }
    const globalMean = totalCount > 0 ? totalSum / totalCount : 0;
    const infoY = padding / 2;
    let infoX = padding - 29;
    // 先画蓝色卫星名
    ctx.fillStyle = statsPrefixColor;
    ctx.fillText(statsPrefixText, infoX, infoY);
    // 再画白色统计信息
    const prefixStats = `数据点: ${totalCount}    最大值: ${statsMax.toFixed(2)}    最小值: ${statsMin.toFixed(2)}    平均值: ${globalMean.toFixed(2)}    峰值差: `;
    const prefixWidth = ctx.measureText(statsPrefixText).width;
    ctx.fillStyle = '#fff';
    ctx.fillText(prefixStats, infoX + prefixWidth, infoY);
    ctx.fillStyle = '#FFA500';
    ctx.fillText(`${(statsMax - statsMin).toFixed(2)}`, infoX + prefixWidth + ctx.measureText(prefixStats).width, infoY);
    // 图例
    ctx.font = '13px Arial';
    ctx.textAlign = 'left'; // 改为左对齐，配合右上角位置
    const uniqueSatellites = new Set<string>();
    chartDataArr.forEach(data => {
      const satelliteId = String((data as any).satelliteId || 'unknown');
      // 只添加选中的卫星到图例，统一转换为字符串进行比较
      if (this.selectedSatellites.has(satelliteId)) {
        uniqueSatellites.add(satelliteId);
      }
    });
    let legendY = 20; // 图例贴近顶部边缘，距离顶部20px
    const legendX = canvasWidth - 400; // 图例放在右上角，预留400px宽度

    // 收集每个卫星的参数信息
    const satelliteInfos: {
      satelliteName: string,
      satelliteId: string,
      params: { param: any, color: string }[]
    }[] = [];

    Array.from(uniqueSatellites).forEach((satId) => {
      const satelliteData = chartDataArr.filter(data => (data as any).satelliteId === satId);
      if (satelliteData.length > 0) {
        const satelliteName = satelliteData[0]?.satelliteName || `卫星${satId}`;
        const params: { param: any, color: string }[] = [];

        // 收集该卫星的所有选中参数
        const addedParams = new Set<string>(); // 避免重复参数
        satelliteData.forEach((data) => {
          const paramKey = (data as any).paramKey;

          // 只收集选中参数的信息，且避免重复
          if (selectedParams.includes(paramKey) && !addedParams.has(paramKey)) {
            const param = this.paramList.find(p => p.key === paramKey) || this.paramList[0];
            params.push({
              param: param,
              color: param.color
            });
            addedParams.add(paramKey);
          }
        });

        if (params.length > 0) {
          satelliteInfos.push({
            satelliteName,
            satelliteId: satId,
            params
          });
        }
      }
    });

    // 多行显示：每个卫星一行，显示卫星名 + 所有选中的参数（用颜色区分）
    if (satelliteInfos.length > 0) {
      const lineHeight = 18; // 行高
      const rightMargin = 10; // 右边距

      satelliteInfos.forEach((satInfo, satIndex) => {
        const currentLegendY = legendY + satIndex * lineHeight;

        // 先计算该行的总宽度
        let totalWidth = 0;

        // 计算卫星名宽度
        ctx.fillStyle = '#ffffff';
        const satelliteText = `${satInfo.satelliteName}（${satInfo.satelliteId}） `;
        totalWidth += ctx.measureText(satelliteText).width;

        // 计算所有参数的宽度
        satInfo.params.forEach((info, index) => {
          if (index > 0) {
            totalWidth += ctx.measureText(' | ').width;
          }
          totalWidth += ctx.measureText(info.param.label).width;
        });

        // 从右边开始绘制，确保不超出边界
        let currentX = Math.min(legendX, canvasWidth - totalWidth - rightMargin);

        // 绘制卫星名（白色）
        ctx.fillStyle = '#ffffff';
        ctx.fillText(satelliteText, currentX, currentLegendY);
        currentX += ctx.measureText(satelliteText).width;

        // 绘制所有参数，每个参数用自己的颜色
        satInfo.params.forEach((info, index) => {
          if (index > 0) {
            // 绘制分隔符（白色）
            ctx.fillStyle = '#ffffff';
            const separator = ' | ';
            ctx.fillText(separator, currentX, currentLegendY);
            currentX += ctx.measureText(separator).width;
          }

          // 绘制参数名，使用参数颜色
          ctx.fillStyle = info.color;
          ctx.fillText(info.param.label, currentX, currentLegendY);
          currentX += ctx.measureText(info.param.label).width;
        });
      });
    }
    ctx.restore();
  }

  // 统计信息已集成到canvas右上角，无需单独HTML





  // 获取参数对应的颜色
  private getColorForParam(paramKey: string): string {
    const param = this.paramList.find(p => p.key === paramKey);
    return param ? param.color : '#ffffff';
  }

  // 获取当前选中的参数列表
  private getSelectedParameters(): string[] {
    const buttons = document.querySelectorAll('.param-btn');
    const selectedParams: string[] = [];

    buttons.forEach((btn) => {
      const button = btn as HTMLButtonElement;
      if (button.dataset.selected === 'true') {
        selectedParams.push(button.value);
      }
    });

    return selectedParams;
  }



  // 更新NORAD输入框
  private updateNoradInput(sccNum: string): void {
    const noradInput = document.getElementById('history-norad-id') as HTMLInputElement;
    if (noradInput && sccNum) {
      noradInput.value = sccNum;
    }
  }

  // 从当前选中的卫星更新NORAD编号
  private updateNoradFromSelectedSat(): void {
    try {
      const selectSatManager = keepTrackApi.getPluginByName('SelectSatManager') as any;
      if (selectSatManager?.selectedSat > -1) {
        const selectedSat = selectSatManager.getSelectedSat();
        if (selectedSat?.isSatellite()) {
          const sat = selectedSat as DetailedSatellite;
          this.updateNoradInput(sat.sccNum);
        }
      }
    } catch (error) {
      // 忽略错误，可能是插件还未初始化
      console.debug('SelectSatManager not ready yet:', error);
    }
  }









  // 卫星选择状态管理
  private selectedSatellites = new Set<string>();

  // 生成卫星选择按钮
  private generateSatelliteButtons_(satelliteIds: string[], satelliteNames: Map<string, string>): void {
    try {
      const container = document.getElementById('satellite-buttons-container');
      const buttonsContainer = document.getElementById('satellite-buttons');

      if (!container || !buttonsContainer) {
        console.warn('卫星按钮容器未找到');
        return;
      }

      // 即使只有一个卫星也显示按钮
      if (satelliteIds.length === 0) {
        container.style.display = 'none';
        this.selectedSatellites.clear();
        return;
      }

      // 显示容器并清空按钮
      container.style.display = 'flex';
      container.style.visibility = 'visible';
      buttonsContainer.innerHTML = '';

      // 初始化时所有卫星都被选中，统一转换为字符串类型
      this.selectedSatellites.clear();
      satelliteIds.forEach(id => this.selectedSatellites.add(String(id)));

      // 生成按钮并添加事件处理
      satelliteIds.forEach(satelliteId => {
        const satelliteName = satelliteNames.get(satelliteId) || `卫星${satelliteId}`;
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'satellite-btn';
        button.value = satelliteId;
        button.textContent = `${satelliteName} (${satelliteId})`;
        button.dataset.selected = 'true';

        // 设置按钮样式（与参数按钮一致的风格）
        const baseStyle = 'border-radius: 6px; padding: 6px 12px; font-size: 12px; cursor: pointer; min-width: 90px; transition: all 0.1s ease; user-select: none; outline: none; border: 1px solid;';
        button.style.cssText = baseStyle + ' background: #ffffff; color: #000000; border-color: #ffffff;';

        // 添加点击事件
        button.addEventListener('click', (e: Event) => {
          e.preventDefault();
          e.stopPropagation();

          const currentSatelliteId = button.value;
          const isSelected = button.dataset.selected === 'true';
          const newSelected = !isSelected;

          button.dataset.selected = newSelected.toString();

          // 更新按钮样式（与参数按钮一致）
          if (newSelected) {
            button.style.cssText = baseStyle + ' background: #ffffff; color: #000000; border-color: #ffffff;';
            this.selectedSatellites.add(String(currentSatelliteId));
          } else {
            button.style.cssText = baseStyle + ' background: transparent; color: #666666; border-color: #666666;';
            this.selectedSatellites.delete(String(currentSatelliteId));
          }

          // 重新绘制图表
          if (this.lastChartDataArr && this.lastChartParams && this.lastAllData) {
            this.displayChartMulti_(this.lastChartDataArr, this.lastChartParams, this.lastAllData, false, true);
          }
        });

        buttonsContainer.appendChild(button);
      });
    } catch (error) {
      console.error('生成卫星按钮时出错:', error);
    }
  }



  // 处理按钮点击
  private handleButtonClick(button: HTMLButtonElement): void {
    // 立即更新按钮状态，确保响应迅速
    const borderColor = this.getColorForParam(button.value);
    const isSelected = button.dataset.selected === 'true';

    if (isSelected) {
      // 取消选中
      button.style.background = 'transparent';
      button.style.color = borderColor;
      button.dataset.selected = 'false';
    } else {
      // 选中
      button.style.background = borderColor;
      button.style.color = '#fff';
      button.dataset.selected = 'true';
    }

    // 异步重绘图表，避免阻塞UI
    if (this.lastChartDataArr && this.lastChartParams && this.lastAllData) {
      requestAnimationFrame(() => {
        // 参数切换时强制更新Y轴范围到合适范围
        this.displayChartMulti_(this.lastChartDataArr!, this.lastChartParams!, this.lastAllData!, false, true);
      });
    }
  }

  // 清理卫星按钮容器
  private cleanupSatelliteButtons_(): void {
    const container = document.getElementById('satellite-buttons-container');
    if (container) {
      container.style.display = 'none';
    }
  }

  // 在插件隐藏时清理按钮
  onHide(): void {
    this.cleanupSatelliteButtons_();
  }
}



