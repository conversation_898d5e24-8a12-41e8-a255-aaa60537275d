import { EChartsData, GetSatType, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import waterfall2Png from '@public/img/icons/waterfall2.png';
import * as echarts from 'echarts';
import 'echarts-gl';
import { DetailedSatellite, SpaceObjectType } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

export class Inc2AltPlots extends KeepTrackPlugin {
  readonly id = 'Inc2AltPlots';
  dependencies_: string[] = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }

  bottomIconImg = waterfall2Png;
  bottomIconElementName = 'menu-inc2alt-plots';
  bottomIconCallback = () => {
    if (!this.isMenuButtonActive) {
      return;
    }
    const chartDom = getEl(this.plotCanvasId)!;

    this.createPlot(Inc2AltPlots.getPlotData(), chartDom);
  };

  menuMode: MenuMode[] = [MenuMode.ANALYSIS, MenuMode.ALL];

  plotCanvasId = 'plot-analysis-chart-inc2alt';
  chart: echarts.ECharts;

  sideMenuElementName = 'inc2alt-plots-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="inc2alt-plots-menu" class="side-menu-parent start-hidden text-select" style="width: 100vw; min-width: 100vw; max-width: 100vw;">
    <!-- 右上角关闭按钮 -->
    <div id="inc2alt-close-btn" style="position: fixed; top: 20px; right: 20px; z-index: 99999; width: 40px; height: 40px; background: transparent; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 28px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="plot-analysis-content" class="side-menu" style="width: 100vw; height: 100vh; padding: 20px;">
      <h5 class="center-align" style="color: white; margin-bottom: 20px;">倾角高度图</h5>
      <div id="${this.plotCanvasId}" style="width: 100%; height: calc(100vh - 100px);"></div>
    </div>
  </div>`;

  addHtml(): void {
    super.addHtml();
  }

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器 - 使用事件委托确保能工作
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'inc2alt-close-btn') {
        console.log('倾角-高度图关闭按钮被点击了！');
        e.preventDefault();
        e.stopPropagation();

        // 隐藏菜单
        const menu = document.getElementById('inc2alt-plots-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
          console.log('倾角-高度图菜单已隐藏');
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById(this.bottomIconElementName);
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
          console.log('倾角-高度图底部图标选中状态已取消');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
        console.log('倾角-高度图插件状态已重置');
      }
    });
  }

  createPlot(data: EChartsData, chartDom: HTMLElement) {
    // Dont Load Anything if the Chart is Closed
    if (!this.isMenuButtonActive) {
      return;
    }

    // Delete any old charts and start fresh
    if (!this.chart) {
      // Setup Configuration
      this.chart = echarts.init(chartDom);
      this.chart.on('click', (event) => {
        if ((event.data as unknown as { id: number })?.id > -1) {
          this.selectSatManager_.selectSat((event.data as unknown as { id: number })?.id);
        }
      });
    }

    // Setup Chart
    this.chart.setOption({
      title: {
        text: '倾角与高度散点图',
        textStyle: {
          fontSize: Math.round(16 / (parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--system-scale-factor')) || 1)),
          color: '#fff',
        },
      },
      legend: {
        show: true,
        textStyle: {
          color: '#fff',
        },
      },
      tooltip: {
        formatter: (params) => {
          const data = params.value;
          const color = params.color;
          const name = params.name;

          return `
            <div style="display: flex; flex-direction: column; align-items: flex-start;">
              <div style="display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: space-between; align-items: flex-end;">
                <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-bottom: 5px;"></div>
                <div style="font-weight: bold;"> ${name}</div>
              </div>
              <div><bold>倾角:</bold> ${data[1].toFixed(3)}°</div>
              <div><bold>轨道高度:</bold> ${data[0].toFixed(3)} km</div>
              <div><bold>轨道周期:</bold> ${data[2].toFixed(2)} min</div>
            </div>
          `;
        },
      },
      xAxis: {
        name: '高度(km)',
        type: 'value',
        position: 'bottom',
      },
      yAxis: {
        name: '倾角(°)',
        type: 'value',
        position: 'left',
      },
      zAxis: {
        name: '周期(min)',
        type: 'value',
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'slider',
          show: true,
          yAxisIndex: [0],
          left: '93%',
          start: 0,
          end: 80,
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'inside',
          yAxisIndex: [0],
          start: 0,
          end: 80,
        },
      ],
      visualMap: [
        {
          left: 'left',
          top: '10%',
          dimension: 2,
          min: 60,
          max: 360,
          itemWidth: Math.round(30 / (parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--system-scale-factor')) || 1)),
          itemHeight: Math.round(500 / (parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--system-scale-factor')) || 1)),
          calculable: true,
          precision: 0.05,
          text: ['周期(min)'],
          textGap: Math.round(30 / (parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--system-scale-factor')) || 1)),
          textStyle: {
            color: '#fff',
            fontSize: Math.round(12 / (parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--system-scale-factor')) || 1)),
          },
          inRange: {
            // symbolSize: [10, 70],
          },
          outOfRange: {
            // symbolSize: [10, 70],
            opacity: 0,
            symbol: 'none',
          },
          controller: {
            inRange: {
              color: ['#41577c'],
            },
            outOfRange: {
              color: ['#999'],
            },
          },
        },
      ],
      series: data.map((country) => ({
        type: 'scatter',
        name: country.name,
        data: country.value?.map((item) => ({
          name: item[3],
          id: item[4],
          value: [item[1], item[0], item[2]],
        })),
        symbolSize: 12,
        itemStyle: {
          borderWidth: 1,
          borderColor: 'rgba(255,255,255,0.8)',
        },
        emphasis: {
          itemStyle: {
            color: '#fff',
          },
        },
      })),
    });
  }

  static getPlotData(): EChartsData {
    const china = [] as unknown as [number, number, number, string, number][];
    const usa = [] as unknown as [number, number, number, string, number][];
    const france = [] as unknown as [number, number, number, string, number][];
    const russia = [] as unknown as [number, number, number, string, number][];
    const other = [] as unknown as [number, number, number, string, number][];
    const japan = [] as unknown as [number, number, number, string, number][];
    const india = [] as unknown as [number, number, number, string, number][];

    keepTrackApi.getCatalogManager().objectCache.forEach((obj) => {
      if (obj.type !== SpaceObjectType.PAYLOAD) {
        return;
      }

      let sat = obj as DetailedSatellite;

      if (sat.period > 250) {
        return;
      }

      sat = keepTrackApi.getCatalogManager().getSat(sat.id, GetSatType.POSITION_ONLY)!;
      const now = keepTrackApi.getTimeManager().simulationTimeObj;

      const alt = sat.lla(now)?.alt ?? 0;

      if (alt < 70) {
        return;
      } // TODO: USE THIS FOR FINDING DECAYS!

      switch (sat.country) {
        case 'United States of America':
        case 'United States':
        case 'US':
        case 'USA':
          usa.push([sat.inclination, alt, sat.period, sat.name, sat.id]);

          return;
        case 'France':
        case 'FR':
        case 'F':
          france.push([sat.inclination, alt, sat.period, sat.name, sat.id]);

          return;

        case 'Russian Federation':
        case 'CIS':
        case 'RU':
        case 'SU':
        case 'Russia':
          russia.push([sat.inclination, alt, sat.period, sat.name, sat.id]);

          return;
        case 'China':
        case 'China, People\'s Republic of':
        case 'Hong Kong Special Administrative Region, China':
        case 'China (Republic)':
        case 'PRC':
        case 'CN':
          china.push([sat.inclination, alt, sat.period, sat.name, sat.id]);
          return;

        case 'IN':
        case 'india':
          india.push([sat.inclination, alt, sat.period, sat.name, sat.id]);
          return;

        case 'J':
        case 'japan':
          japan.push([sat.inclination, alt, sat.period, sat.name, sat.id]);

          return;

        default:
          other.push([sat.inclination, alt, sat.period, sat.name, sat.id]);

      }
    });

    return [
      { name: '美国', value: usa },
      { name: '中国', value: china },
      { name: '俄罗斯', value: russia },
      { name: '法国', value: france },
      { name: '印度', value: india },
      { name: '日本', value: japan },
      { name: '其它', value: other },
    ] as unknown as EChartsData;
  }
}
