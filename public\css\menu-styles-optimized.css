/*
 * 优化的菜单样式系统
 * 替代 ultimate-menu-fix.css，提供精简高效的菜单样式
 * 创建时间: 2025-01-16
 * 目标: 统一所有菜单样式，减少代码重复
 */

/* ========================================
 * 1. CSS变量定义 - 🔥 正确的缩放变量设置
 * ======================================== */
:root {
  /* 🔥 缩放因子 - 提供默认值，JavaScript会动态更新 */
  --system-scale-factor: 1;

  /* 🔥 增大基础字体大小 - 使用缩放变量但提供合理默认值 */
  --base-font-size: calc(20px / var(--system-scale-factor, 1));
  --large-font-size: calc(24px / var(--system-scale-factor, 1));
  --xlarge-font-size: calc(28px / var(--system-scale-factor, 1));
  --icon-size: calc(32px / var(--system-scale-factor, 1));

  /* 菜单相关变量 - 根据系统缩放调整 */
  --menu-label-font-size: var(--base-font-size);
  --menu-input-height: calc(44px / var(--system-scale-factor, 1));
  --menu-spacing: calc(12px / var(--system-scale-factor, 1));
  --menu-label-color: #b3e5fc;
  --menu-input-color: white;
  --menu-border-color: rgba(255, 255, 255, 0.3);

  /* 底部菜单变量 */
  --bottom-icon-width: calc(115px / var(--system-scale-factor, 1));
  --bottom-filter-width: calc(185px / var(--system-scale-factor, 1));
  --bottom-menu-height: calc(120px / var(--system-scale-factor, 1));
}

/* ========================================
 * 2. 通用菜单容器样式
 * ======================================== */
[id$="-menu"]:not(#sat-infobox),
.side-menu-parent:not(#sat-infobox) {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

/* ========================================
 * 3. 输入字段统一样式
 * ======================================== */

/* 输入字段容器 - 增加顶部间距避免重叠 */
[id$="-menu"]:not(#sat-infobox) .input-field,
.side-menu-parent:not(#sat-infobox) .input-field {
  position: relative !important;
  margin-bottom: calc(20px / var(--system-scale-factor)) !important;
  padding-top: calc(25px / var(--system-scale-factor)) !important;
  min-height: calc(60px / var(--system-scale-factor)) !important;
}

/* 所有标签统一样式 - 恢复Materialize行为 */
[id$="-menu"]:not(#sat-infobox) .input-field label:not(.switch label):not(.lever),
.side-menu-parent:not(#sat-infobox) .input-field label:not(.switch label):not(.lever) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  font-size: var(--menu-label-font-size) !important;
  font-weight: 400 !important;
  color: var(--menu-label-color) !important;
  pointer-events: none !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
  transform-origin: 0% 100% !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  transform: translateY(calc(12px / var(--system-scale-factor))) !important;
  cursor: text !important;
  line-height: 1.2 !important;
}

/* 输入框统一样式 */
[id$="-menu"]:not(#sat-infobox) .input-field input[type="text"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="number"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="email"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="password"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="text"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="number"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="email"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="password"] {
  height: var(--menu-input-height) !important;
  margin-top: calc(3px / var(--system-scale-factor)) !important;
  padding: calc(4px / var(--system-scale-factor)) 0 !important;
  font-size: var(--menu-label-font-size) !important;
  line-height: 1.4 !important;
  border: none !important;
  border-bottom: 1px solid var(--menu-border-color) !important;
  background: transparent !important;
  color: var(--menu-input-color) !important;
  box-shadow: none !important;
}

/* 选择框统一样式 */
[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper input.select-dropdown,
.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper input.select-dropdown {
  height: var(--menu-input-height) !important;
  min-height: var(--menu-input-height) !important;
  margin-top: calc(8px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) 0 !important;
  font-size: var(--menu-label-font-size) !important;
  line-height: 1.4 !important;
  border: none !important;
  border-bottom: 1px solid var(--menu-border-color) !important;
  background: transparent !important;
  color: var(--menu-input-color) !important;
  opacity: 1 !important;
}

/* 选择框标签特殊处理 - 修复重叠问题 */
[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper + label,
[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper ~ label,
.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper + label,
.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper ~ label {
  transform: translateY(calc(-20px / var(--system-scale-factor))) scale(0.8) !important;
  transform-origin: 0 0 !important;
  color: var(--menu-label-color) !important;
  top: calc(-5px / var(--system-scale-factor)) !important;
  z-index: 2 !important;
}

/* ========================================
 * 4. 激活状态样式 - 标签移到输入框上方
 * ======================================== */
[id$="-menu"]:not(#sat-infobox) .input-field label.active,
[id$="-menu"]:not(#sat-infobox) .input-field input:focus + label,
[id$="-menu"]:not(#sat-infobox) .input-field input:not(:placeholder-shown) + label,
[id$="-menu"]:not(#sat-infobox) .input-field input[type]:not(:placeholder-shown) + label,
.side-menu-parent:not(#sat-infobox) .input-field label.active,
.side-menu-parent:not(#sat-infobox) .input-field input:focus + label,
.side-menu-parent:not(#sat-infobox) .input-field input:not(:placeholder-shown) + label,
.side-menu-parent:not(#sat-infobox) .input-field input[type]:not(:placeholder-shown) + label {
  transform: translateY(calc(-14px / var(--system-scale-factor))) scale(0.8) !important;
  transform-origin: 0 0 !important;
  color: var(--menu-label-color) !important;
}

/* ========================================
 * 5. 开关样式
 * ======================================== */
.switch {
  margin: calc(15px / var(--system-scale-factor)) 0 !important;
  padding: calc(5px / var(--system-scale-factor)) 0 !important;
}

.switch label {
  font-size: var(--menu-label-font-size) !important;
  color: var(--menu-label-color) !important;
  display: flex !important;
  align-items: center !important;
  cursor: pointer !important;
  line-height: 1.4 !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  padding: 0 !important;
  margin: 0 !important;
  pointer-events: auto !important;
}

.switch label .lever {
  margin: 0 calc(15px / var(--system-scale-factor)) !important;
  vertical-align: middle !important;
  flex-shrink: 0 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  /* 🔥 添加蓝色背景 */
  background-color: #2196f3 !important;
  border-radius: calc(15px / var(--system-scale-factor)) !important;
  border: none !important;
}

/* 🔥 开关按钮激活状态 - 覆盖Materialize默认样式 */
.side-menu .switch label input[type=checkbox]:checked + .lever,
[id$="-menu"] .switch label input[type=checkbox]:checked + .lever {
  background-color: #2196f3 !important; /* 蓝色背景 */
}

/* 🔥 开关按钮滑块 - 覆盖Materialize默认样式 */
.side-menu .switch label .lever:after,
[id$="-menu"] .switch label .lever:after {
  background-color: white !important;
  border: none !important;
  border-radius: 50% !important;
  width: calc(20px / var(--system-scale-factor)) !important;
  height: calc(20px / var(--system-scale-factor)) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

/* 🔥 侧边菜单按钮蓝色背景 - 覆盖Materialize默认样式 */
.side-menu .btn,
.side-menu .btn-small,
.side-menu .btn-large,
.side-menu button,
[id$="-menu"] .btn,
[id$="-menu"] .btn-small,
[id$="-menu"] .btn-large,
[id$="-menu"] button {
  background-color: rgba(33, 150, 243, 0.8) !important; /* 🔥 蓝色背景 */
  background: rgba(33, 150, 243, 0.8) !important;
  color: white !important;
  border: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.2s ease !important;
}

.side-menu .btn:hover,
.side-menu .btn-small:hover,
.side-menu .btn-large:hover,
.side-menu button:hover,
[id$="-menu"] .btn:hover,
[id$="-menu"] .btn-small:hover,
[id$="-menu"] .btn-large:hover,
[id$="-menu"] button:hover {
  background-color: rgba(33, 150, 243, 1.0) !important; /* 🔥 悬停时更深的蓝色 */
  background: rgba(33, 150, 243, 1.0) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* ========================================
 * 6. 下拉框优化
 * ======================================== */
/* 🔧 强制修复下拉框高度 - 终极优先级 */
.dropdown-content {
  max-height: calc(500px / var(--system-scale-factor)) !important;
  min-height: calc(200px / var(--system-scale-factor)) !important;
  height: auto !important;
  min-width: calc(250px / var(--system-scale-factor)) !important;
  width: auto !important;
  background: var(--color-dark-background) !important;
  border: 1px solid var(--menu-border-color) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  overflow-y: auto !important;
  z-index: 9999 !important;
}

.dropdown-content li {
  min-height: calc(60px / var(--system-scale-factor)) !important;
  height: calc(60px / var(--system-scale-factor)) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
  padding: calc(18px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
  font-size: calc(18px / var(--system-scale-factor)) !important;
  display: flex !important;
  align-items: center !important;
  color: white !important;
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.dropdown-content li:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dropdown-content li > a,
.dropdown-content li > span {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
  color: white !important;
  padding: 0 !important;
  display: block !important;
  width: 100% !important;
}

/* 🔥🔥🔥 终极下拉框修复 - 覆盖所有CSS文件 🔥🔥🔥 */
html body .dropdown-content,
html body div .dropdown-content,
html body * .dropdown-content,
body .dropdown-content,
.side-menu .dropdown-content,
#settings-menu .dropdown-content {
  max-height: calc(500px / var(--system-scale-factor)) !important;
  min-height: calc(200px / var(--system-scale-factor)) !important;
  height: auto !important;
  overflow-y: auto !important;
  background: #001a33 !important;
  border: 1px solid #003366 !important;
  z-index: 9999 !important;
}

html body .dropdown-content li,
html body div .dropdown-content li,
html body * .dropdown-content li,
body .dropdown-content li,
.side-menu .dropdown-content li,
#settings-menu .dropdown-content li {
  min-height: calc(60px / var(--system-scale-factor)) !important;
  height: calc(60px / var(--system-scale-factor)) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
  padding: calc(18px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
  font-size: calc(18px / var(--system-scale-factor)) !important;
  display: flex !important;
  align-items: center !important;
  color: white !important;
}

html body .dropdown-content li > span,
html body div .dropdown-content li > span,
html body * .dropdown-content li > span,
body .dropdown-content li > span,
.side-menu .dropdown-content li > span,
#settings-menu .dropdown-content li > span {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
  color: white !important;
}

/* 🔥 强制修复所有按钮缩放 - 使用缩放变量和强选择器 */
html body .side-menu button,
html body #settings-menu button,
html body .side-menu .btn,
html body #settings-menu .btn,
html body .side-menu .ui-button,
html body #settings-menu .ui-button,
.side-menu button,
#settings-menu button,
.side-menu .btn,
#settings-menu .btn,
.side-menu .ui-button,
#settings-menu .ui-button {
  font-size: var(--base-font-size) !important;
  height: calc(44px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
  min-height: calc(44px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复顶部菜单图标缩放 */
.top-menu-icons img,
#sound-icon,
#legend-icon img,
#tutorial-icon img,
#fullscreen-icon img {
  width: calc(30px / var(--system-scale-factor, 1)) !important;
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  margin: calc(3px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复搜索框缩放 - 完全透明背景 */
#search {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; /* 只保留底部细线 */
  border-radius: 0 !important; /* 移除圆角 */
  background: transparent !important; /* 🔥 确保完全透明背景 */
  background-color: transparent !important; /* 🔥 强制透明背景色 */
  background-image: none !important; /* 🔥 移除背景图片 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
  -webkit-box-shadow: none !important; /* 移除webkit阴影 */
}

/* 🔥 修复搜索框placeholder文字缩放 */
#search::placeholder {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  background: transparent !important; /* 🔥 确保placeholder背景透明 */
  background-color: transparent !important;
}

/* 🔥 修复搜索框输入文字缩放 - 聚焦状态也保持透明 */
#search:focus,
#search:active {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important; /* 聚焦时稍微亮一点的底部线 */
  background: transparent !important; /* 🔥 聚焦时也保持透明背景 */
  background-color: transparent !important; /* 🔥 强制透明背景色 */
  background-image: none !important; /* 🔥 移除背景图片 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
  -webkit-box-shadow: none !important; /* 移除webkit阴影 */
}

/* 🔥 修复搜索清除图标缩放 */
#clear-search {
  width: calc(18px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  right: calc(8px / var(--system-scale-factor, 1)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 🔥 强制修复时间管理器缩放 - 使用缩放变量和强选择器 */
html body #datetime,
html body #jday,
html body #datetime-text,
html body #datetime-title,
html body #non-realtime-indicator,
html body .datetime-container,
html body .time-machine,
html body .ui-timepicker-wrapper,
#datetime,
#jday,
#datetime-text,
#datetime-title,
#non-realtime-indicator,
.datetime-container,
.time-machine,
.ui-timepicker-wrapper {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  color: white !important;
}

/* 🔥 非实时指示器专门样式 */
html body #non-realtime-indicator,
#non-realtime-indicator {
  background-color: #ffeb3b !important;
  color: #333 !important;
  padding: calc(2px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(3px / var(--system-scale-factor, 1)) !important;
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  cursor: pointer !important;
  margin-left: calc(5px / var(--system-scale-factor, 1)) !important;
  font-family: 'Microsoft YaHei', '微软雅黑', sans-serif !important;
}

/* 🔥 修复顶部菜单日期时间一致性 */
html body #jday,
#jday {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  color: white !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  display: block !important;
}

/* 🔥 修复竖线分隔符缩放 */
html body #datetime-text,
#datetime-text {
  border-left: calc(2px / var(--system-scale-factor, 1)) solid var(--color-dark-text-accent) !important;
  padding-left: calc(12px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 🔥 为传感器选择区域添加一致的竖线分隔符 */
html body #sensor-selected-container,
#sensor-selected-container {
  border-left: calc(2px / var(--system-scale-factor, 1)) solid var(--color-dark-text-accent) !important;
  padding-left: calc(12px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 统一传感器选择区域字体大小 */
html body #sensor-selected,
#sensor-selected {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 统一日期字体族和大小 */
html body #jday,
#jday {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  width: calc(200px / var(--system-scale-factor, 1)) !important;
  min-width: calc(200px / var(--system-scale-factor, 1)) !important;
  max-width: calc(200px / var(--system-scale-factor, 1)) !important;
}

#datetime input,
.datetime-container input,
.time-machine input,
.ui-timepicker-wrapper input {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) !important;
}

#datetime button,
.datetime-container button,
.time-machine button,
.ui-timepicker-wrapper button {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

/* 🔥 强制修复sat-info-box所有元素缩放 - 使用更大字体和强选择器 */
html body #sat-infobox,
html body .sat-info-box,
#sat-infobox,
.sat-info-box {
  font-size: var(--base-font-size) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}

html body #sat-infobox .sat-info-title,
html body .sat-info-box .sat-info-title,
#sat-infobox .sat-info-title,
.sat-info-box .sat-info-title {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
}

html body #sat-infobox .sat-info-content,
html body .sat-info-box .sat-info-content,
html body #sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-value,
#sat-infobox .sat-info-content,
.sat-info-box .sat-info-content,
#sat-infobox .sat-info-key,
#sat-infobox .sat-info-value {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

#sat-infobox button,
.sat-info-box button {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 终极修复：所有图标和特殊元素缩放 🔥🔥🔥 */

/* 🔥 1. 强制修复所有material-icons图标缩放 - 使用强选择器 */
html body .material-icons,
html body .side-menu .material-icons,
html body #settings-menu .material-icons,
html body #sat-infobox .material-icons,
html body .sat-info-box .material-icons,
.material-icons,
.side-menu .material-icons,
#settings-menu .material-icons,
#sat-infobox .material-icons,
.sat-info-box .material-icons {
  font-size: var(--icon-size) !important;
  line-height: var(--icon-size) !important;
  width: var(--icon-size) !important;
  height: var(--icon-size) !important;
}

/* 🔥 时间管理器简化缩放 - 只缩放字体 */

/* 1. 顶部时间显示区域 */
#datetime-text,
#datetime-input,
#datetime-input-form,
#datetime-input-tb,
#non-realtime-indicator {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 2. 时间管理器容器 - 只缩放字体，不缩放容器 */
#ui-datepicker-div,
.ui-timepicker-div {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 3. 时间管理器子元素缩放 */
.ui-timepicker-div .ui-slider,
.ui-timepicker-div table,
.ui-timepicker-div td,
.ui-timepicker-div tr {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 4. 按钮缩放和对齐修复 */
#ui-datepicker-div button,
.ui-datepicker button,
.ui-timepicker-div button {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  min-height: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(16px / var(--system-scale-factor, 1)) !important;
  vertical-align: middle !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 calc(2px / var(--system-scale-factor, 1)) !important;
  border: none !important;
  outline: none !important;
}

/* 4. 表格单元格缩放 */
#ui-datepicker-div td,
#ui-datepicker-div th,
.ui-datepicker td,
.ui-datepicker th {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
}

/* 5. 日历表格布局 - 保持原有布局 */
.ui-datepicker-calendar {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: calc(5px / var(--system-scale-factor, 1)) 0 !important;
  border-spacing: 0 !important;
}

.ui-datepicker-calendar td {
  width: 14.28% !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  vertical-align: middle !important;
  cursor: pointer !important;
  pointer-events: auto !important;
  border: calc(1px / var(--system-scale-factor, 1)) solid transparent !important;
}

.ui-datepicker-calendar th {
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  padding: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 确保日历链接和文本可以正确点击 */
.ui-datepicker-calendar td a,
.ui-datepicker-calendar td .ui-datepicker-cal-day,
.ui-datepicker-calendar td .ui-datepicker-jday {
  cursor: pointer !important;
  pointer-events: auto !important;
  display: block !important;
  text-decoration: none !important;
  user-select: none !important;
}

/* 6. 时间选择器简化布局 */
.ui_tpicker_hour,
.ui_tpicker_minute,
.ui_tpicker_second,
.ui_tpicker_proprate {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  padding: calc(5px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
}

.ui-slider-access {
  margin-left: calc(10px / var(--system-scale-factor, 1)) !important;
  margin-right: 0px !important;
  display: flex !important;
  flex-direction: row !important;
  gap: calc(4px / var(--system-scale-factor, 1)) !important;
}

.ui-slider-access button {
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  width: calc(24px / var(--system-scale-factor, 1)) !important;
  height: calc(24px / var(--system-scale-factor, 1)) !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(3px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 时间管理器标签缩放 */
.ui_tpicker_time_label,
.ui_tpicker_hour_label,
.ui_tpicker_minute_label,
.ui_tpicker_second_label,
.ui_tpicker_proprate_label,
.ui-timepicker-div dt {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  color: white !important;
}

/* 修复时间输入框缩放 */
.ui_tpicker_time_input {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  height: calc(24px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  padding: calc(4px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 修复滑动条缩放和布局 */
.ui_tpicker_hour_slider,
.ui_tpicker_minute_slider,
.ui_tpicker_second_slider,
.ui_tpicker_proprate_slider {
  width: calc(150px / var(--system-scale-factor, 1)) !important; /* 增加宽度 */
  height: calc(8px / var(--system-scale-factor, 1)) !important;
  display: inline-block !important;
  margin: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  flex: 1 !important;
  min-width: calc(100px / var(--system-scale-factor, 1)) !important; /* 增加最小宽度 */
  background: #2196f3 !important; /* 确保蓝色背景 */
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复滑动条滑块 - 强化优先级 */
.ui-timepicker-div .ui-slider-handle,
.ui-datepicker .ui-slider-handle,
#ui-datepicker-div .ui-slider-handle,
.ui-slider-handle {
  width: calc(20px / var(--system-scale-factor, 1)) !important;
  height: calc(20px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(10px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(-10px / var(--system-scale-factor, 1)) !important;
  margin-top: calc(-10px / var(--system-scale-factor, 1)) !important;
  border: calc(2px / var(--system-scale-factor, 1)) solid #fff !important;
  background: #ff9800 !important; /* 橙色点 */
  background-color: #ff9800 !important;
  top: 50% !important;
  cursor: pointer !important;
}

/* 修复滑动条轨道 - 强化优先级 */
.ui-timepicker-div .ui-slider,
.ui-datepicker .ui-slider,
#ui-datepicker-div .ui-slider,
.ui-slider {
  background: #2196f3 !important; /* 蓝色横线 */
  background-color: #2196f3 !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  border: none !important;
  height: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* === 9. 最终完善和清理 === */

/* 确保所有时间管理器元素都有正确的字体 */
#ui-datepicker-div,
.ui-timepicker-div,
#ui-datepicker-div *,
.ui-timepicker-div * {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 时间管理器按钮蓝色背景 - 只针对时间管理器 */
#ui-datepicker-div .ui-datepicker-current,
#ui-datepicker-div .ui-datepicker-pause,
#ui-datepicker-div .ui-datepicker-close,
#ui-datepicker-div .ui-datepicker-buttonpane button {
  background: #2196f3 !important; /* 蓝色背景 */
  background-color: #2196f3 !important;
  color: white !important;
  border: 1px solid #1976d2 !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

#ui-datepicker-div .ui-datepicker-current:hover,
#ui-datepicker-div .ui-datepicker-pause:hover,
#ui-datepicker-div .ui-datepicker-close:hover,
#ui-datepicker-div .ui-datepicker-buttonpane button:hover {
  background: #1976d2 !important; /* 深蓝色悬浮 */
  background-color: #1976d2 !important;
}

/* 确保时间管理器在不同屏幕尺寸下的响应式 */
@media (max-width: 768px) {
  #ui-datepicker-div {
    max-width: calc(320px / var(--system-scale-factor, 1)) !important;
    min-width: calc(280px / var(--system-scale-factor, 1)) !important;
  }

  .ui-timepicker-div {
    max-width: calc(300px / var(--system-scale-factor, 1)) !important;
    min-width: calc(260px / var(--system-scale-factor, 1)) !important;
  }
}

/* 时间管理器动画效果 */
#ui-datepicker-div,
.ui-timepicker-div {
  transition: all 0.2s ease !important;
}

.ui-datepicker-calendar td a {
  transition: background-color 0.15s ease !important;
}

/* 确保时间管理器在高DPI屏幕上的清晰度 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #ui-datepicker-div,
  .ui-timepicker-div {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}

/* 修复日历导航按钮 */
.ui-datepicker-prev,
.ui-datepicker-next {
  width: calc(32px / var(--system-scale-factor, 1)) !important;
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
}

/* 🔧 专门修复时间管理器+-按钮对齐 */
.ui-timepicker-div .ui_tpicker_hour_slider,
.ui-timepicker-div .ui_tpicker_minute_slider,
.ui-timepicker-div .ui_tpicker_second_slider,
.ui-timepicker-div .ui-slider {
  margin: calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

/* 时间调整按钮容器对齐 - 修复标签与滑动条冲突 */
.ui-timepicker-div dt,
.ui-timepicker-div dd {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important; /* 改为左对齐，避免冲突 */
  margin: calc(4px / var(--system-scale-factor, 1)) 0 !important;
  gap: calc(10px / var(--system-scale-factor, 1)) !important; /* 添加间距 */
}

/* 时间管理器标签样式 */
.ui-timepicker-div dt {
  min-width: calc(60px / var(--system-scale-factor, 1)) !important;
  flex-shrink: 0 !important; /* 防止标签被压缩 */
  text-align: left !important;
}

/* 时间管理器滑动条容器 */
.ui-timepicker-div dd {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  gap: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 时间输入框和按钮组对齐 */
.ui-timepicker-div .ui_tpicker_time,
.ui-timepicker-div .ui_tpicker_hour,
.ui-timepicker-div .ui_tpicker_minute,
.ui-timepicker-div .ui_tpicker_second {
  display: flex !important;
  align-items: center !important;
  gap: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复日历标题 */
.ui-datepicker-title {
  font-size: calc(18px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
}

/* 修复日历头部布局 */
.ui-datepicker-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复设置菜单标题文字缩放 */
html body #settings-menu h4,
html body #settings-menu h5,
html body #settings-menu .menu-title,
html body #settings-menu h5.center-align,
#settings-menu h4,
#settings-menu h5,
#settings-menu .menu-title,
#settings-menu h5.center-align {
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(34px / var(--system-scale-factor, 1)) !important;
  margin: calc(24px / var(--system-scale-factor, 1)) 0 calc(18px / var(--system-scale-factor, 1)) 0 !important;
  color: white !important;
  font-weight: bold !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  text-align: center !important;
}

/* 🔥 修复非实时窗口缩放和颜色 */
html body #non-realtime-indicator,
#non-realtime-indicator {
  background-color: #ffeb3b !important;
  color: #333 !important;
  padding: calc(4px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(6px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  cursor: pointer !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.3s ease !important;
  pointer-events: auto !important; /* 🔥 确保可以接收点击事件 */
  user-select: none !important; /* 🔥 防止文本选择 */
  z-index: 1000 !important; /* 🔥 确保在最上层 */
}

#non-realtime-indicator:hover {
  background-color: #fdd835 !important;
  color: #000 !important;
}

/* 🔥 修复顶部菜单传感器显示框高度问题 */
html body #sensor-selected-container,
#sensor-selected-container {
  height: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 calc(12px / var(--system-scale-factor, 1)) !important;
  margin: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  box-sizing: border-box !important;
}

html body #sensor-selected,
#sensor-selected {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 🔥 3. 强制修复sat-info-box顶部菜单和图标缩放 - 增大字体 */
#sat-info-title,
#sat-info-header {
  font-size: var(--base-font-size) !important;
  min-height: calc(50px / var(--system-scale-factor)) !important;
}

#sat-info-title-name {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor)) !important;
}

#sat-info-close-btn {
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(24px / var(--system-scale-factor)) !important;
  font-size: calc(20px / var(--system-scale-factor)) !important;
}

#sat-add-watchlist,
#sat-remove-watchlist {
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
}

#sat-infobox-fi {
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(18px / var(--system-scale-factor)) !important;
}

.sat-info-section-header {
  height: calc(25px / var(--system-scale-factor)) !important;
  font-size: calc(18px / var(--system-scale-factor)) !important;
  margin-bottom: calc(5px / var(--system-scale-factor)) !important;
}

.section-collapse {
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
  font-size: calc(20px / var(--system-scale-factor)) !important;
}

/* 4. 强制修复侧菜单设置图标和子菜单缩放 */
.side-menu-parent .material-icons,
#settings-menu .material-icons,
.side-menu .material-icons {
  font-size: calc(24px / var(--system-scale-factor)) !important;
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(24px / var(--system-scale-factor)) !important;
}

/* 设置按钮特殊处理 */
.side-menu button .material-icons,
#settings-menu button .material-icons {
  font-size: calc(20px / var(--system-scale-factor)) !important;
  width: calc(20px / var(--system-scale-factor)) !important;
  height: calc(20px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 5. 彻底修复sat-info-box宽度问题 - 覆盖所有响应式CSS */
#sat-infobox {
  width: 100% !important;
  max-width: calc(450px / var(--system-scale-factor)) !important;
}

/* 🔥 强制覆盖responsive-sm.css中的宽度设置 */
@media (min-width: 640px) {
  #sat-infobox {
    width: calc(70% / var(--system-scale-factor)) !important;
    max-width: calc(420px / var(--system-scale-factor)) !important;
  }
}

/* 🔥 强制覆盖responsive-md.css中的宽度设置 */
@media (min-width: 768px) {
  #sat-infobox {
    width: calc(50% / var(--system-scale-factor)) !important;
    max-width: calc(500px / var(--system-scale-factor)) !important;
  }
}

/* 🔥 大屏幕进一步优化宽度 */
@media (min-width: 1024px) {
  #sat-infobox {
    width: calc(40% / var(--system-scale-factor)) !important;
    max-width: calc(550px / var(--system-scale-factor)) !important;
  }
}

/* sat-info-box背景已通过修复根本原因解决，无需暴力覆盖 */

/* 6. 强制修复所有子菜单缩放 */
.sat-infobox-links {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  padding: calc(2px / var(--system-scale-factor)) calc(10px / var(--system-scale-factor)) !important;
}

.sat-info-row {
  margin-bottom: 0px !important; /* 🔥 移除底部margin，避免缝隙 */
  padding: calc(2px / var(--system-scale-factor)) calc(10px / var(--system-scale-factor)) !important;
  min-height: calc(24px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 7. 强制修复鼠标悬停信息框(tooltip)缩放 */
[data-tooltip]:before {
  font-size: var(--base-font-size) !important;
  padding: calc(8px / var(--system-scale-factor)) !important;
  border-width: calc(3px / var(--system-scale-factor)) !important;
  width: calc(150px / var(--system-scale-factor)) !important;
  margin-left: calc(-75px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  /* 🔥 修复tooltip重叠问题 */
  z-index: 9999 !important;
  margin-top: calc(-10px / var(--system-scale-factor)) !important; /* 向上偏移避免重叠 */
}

/* 🔥 特别修复sat-info-box中的tooltip */
#sat-infobox [data-tooltip]:before {
  z-index: 10000 !important; /* 更高的z-index */
  margin-top: calc(-15px / var(--system-scale-factor)) !important; /* 更大的向上偏移 */
  background: rgba(0, 0, 0, 0.9) !important; /* 更深的背景色确保可见性 */
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 🔥 修复悬停框位置 */
[data-tooltip][data-position='bottom']:before {
  bottom: calc(-120% / var(--system-scale-factor)) !important;
}

/* 🔥 修复UI tooltip缩放 */
.ui-tooltip {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  max-width: calc(300px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复Material tooltip缩放 */
.material-tooltip {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复鼠标悬停信息框缩放 */
#sat-hoverbox {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

#sat-hoverbox span {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复悬停框中的国旗缩放 */
#hoverbox-fi,
.fi {
  width: calc(24px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复系统提示信息(Toast)缩放 */
.toast {
  font-size: calc(18px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(10px / var(--system-scale-factor, 1)) !important;
  min-width: calc(300px / var(--system-scale-factor, 1)) !important;
  max-width: calc(400px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}

#toast-container {
  top: calc(75px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复层级菜单缩放 */
#legend-hover-menu {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(10px / var(--system-scale-factor, 1)) !important;
  top: calc(var(--top-menu-height) / var(--system-scale-factor, 1)) !important;
}

#legend-hover-menu li {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(5px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 颜色图例方块缩放 - 超高优先级 */
.Square-Box {
  width: calc(30px / var(--system-scale-factor, 1)) !important;
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(15px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(20px / var(--system-scale-factor, 1)) !important;
  border-width: calc(2px / var(--system-scale-factor, 1)) !important;
  border-style: solid !important;
  box-shadow: 0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
              0 0px calc(8px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  cursor: pointer !important;
}

/* 🔥 设置菜单颜色按钮缩放 - 超高优先级 */
html body #settings-color-payload,
html body #settings-color-rocketBody,
html body #settings-color-debris,
html body #settings-color-inview,
html body #settings-color-missile,
html body #settings-color-missileInview,
html body #settings-color-special,
#settings-color-payload,
#settings-color-rocketBody,
#settings-color-debris,
#settings-color-inview,
#settings-color-missile,
#settings-color-missileInview,
#settings-color-special {
  width: calc(50px / var(--system-scale-factor, 1)) !important;
  height: calc(50px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(25px / var(--system-scale-factor, 1)) !important;
  border-width: calc(2px / var(--system-scale-factor, 1)) !important;
  border-style: solid !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
              0 0px calc(8px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  cursor: pointer !important;
  margin: calc(8px / var(--system-scale-factor, 1)) auto !important;
  display: block !important;
}

/* 🔥 修复搜索结果窗口缩放 */
#search-results {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  padding-top: calc(10px / var(--system-scale-factor, 1)) !important;
}

.search-result {
  padding: calc(6px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

.truncate-search {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 统一传感器菜单列表字体 */
#sensor-list-content ul li {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

#sensor-list-content ul li span {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 统一badge（国家名称）字体 */
#sensor-list-content .badge,
#sensor-list-content .dark-blue-badge {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: normal !important;
}

[data-tooltip][data-position='top']:before {
  bottom: calc(140% / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 8. 强制修复登录界面相关元素缩放 */
.container,
.form-group,
.btn,
button,
input[type="text"],
input[type="password"],
input[type="email"] {
  font-size: var(--base-font-size) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
}

.container h1,
.container h2 {
  font-size: var(--xlarge-font-size) !important;
  line-height: calc(32px / var(--system-scale-factor)) !important;
}

/* 🔥 修复登录按钮和输入框 */
.btn,
button {
  padding: calc(12px / var(--system-scale-factor)) calc(20px / var(--system-scale-factor)) !important;
  min-height: calc(44px / var(--system-scale-factor)) !important;
}

input[type="text"],
input[type="password"],
input[type="email"] {
  padding: calc(12px / var(--system-scale-factor)) !important;
  min-height: calc(44px / var(--system-scale-factor)) !important;
}

/* ========================================
 * 7. 特殊菜单处理
 * ======================================== */

/* 历史轨道菜单 - 水平布局 */
#history-track-menu .input-field label,
#geo-longitude-history-menu .input-field label {
  position: static !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  font-size: var(--menu-label-font-size) !important;
  line-height: calc(32px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  color: #ffffff !important;
  margin: 0 calc(6px / var(--system-scale-factor)) 0 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
}

/* 历史轨道页面输入框 - 保持原有样式 */
#history-track-menu input {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  margin: 0 calc(20px / var(--system-scale-factor, 1)) 0 0 !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  border: none !important;
  border-bottom: 1px solid #9e9e9e !important;
  background: transparent !important;
  color: white !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 历史经度页面输入框 - 使用新样式 */
#geo-longitude-history-menu input,
#geo-longitude-norad-id,
#geo-longitude-start-date,
#geo-longitude-end-date,
#geo-longitude-range {
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(36px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  margin: calc(5px / var(--system-scale-factor, 1)) 0 !important;
  display: block !important;
  width: calc(200px / var(--system-scale-factor, 1)) !important;
  border: 1px solid #9e9e9e !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 修复历史页面标签缩放 */
#history-track-menu label,
#geo-longitude-history-menu label {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  color: #b3e5fc !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  margin-bottom: calc(5px / var(--system-scale-factor, 1)) !important;
  display: block !important;
}

/* 🔥 修复历史轨道和经度页面图标按钮缩放 */
#history-track-close-btn,
#geo-longitude-history-close-btn {
  width: calc(40px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  top: calc(20px / var(--system-scale-factor, 1)) !important;
  right: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复历史页面为全屏高度 */
#history-track-menu,
#geo-longitude-history-menu {
  height: 100vh !important;
  max-height: 100vh !important;
  overflow-y: auto !important;
}

#history-get-data,
#geo-longitude-get-data {
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(36px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

#geo-longitude-download-btn {
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(36px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

/* 修复历史轨道页面参数和卫星按钮缩放 */
.param-btn,
.satellite-btn,
#history-track-menu .param-btn,
#history-track-menu .satellite-btn {
  padding: calc(6px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  min-width: calc(90px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(6px / var(--system-scale-factor, 1)) !important;
  margin: calc(2px / var(--system-scale-factor, 1)) !important;
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复历史轨道页面标题缩放 */
#history-track-menu h5,
#geo-longitude-history-menu h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  line-height: calc(30px / var(--system-scale-factor, 1)) !important;
  margin: calc(20px / var(--system-scale-factor, 1)) 0 calc(15px / var(--system-scale-factor, 1)) 0 !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 修复历史轨道页面分隔线缩放 */
#history-track-menu .divider,
#geo-longitude-history-menu .divider {
  margin: calc(15px / var(--system-scale-factor, 1)) 0 !important;
  height: calc(1px / var(--system-scale-factor, 1)) !important;
}

/* 设置菜单间距优化 - 防止重叠 */
#settings-menu .row {
  margin-bottom: calc(25px / var(--system-scale-factor)) !important;
  clear: both !important;
}

#settings-menu .input-field {
  margin-bottom: calc(35px / var(--system-scale-factor)) !important;
  min-height: calc(65px / var(--system-scale-factor)) !important;
  padding-top: calc(15px / var(--system-scale-factor)) !important;
  padding-bottom: calc(10px / var(--system-scale-factor)) !important;
}

/* 🔥 修复侧边菜单选项间距缩放 */
.side-menu .row,
.side-menu .input-field,
.side-menu .switch,
.side-menu .col {
  margin-bottom: calc(20px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

.side-menu ul li {
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  margin: calc(4px / var(--system-scale-factor, 1)) 0 !important;
}

/* 修复侧边菜单分隔线 */
.side-menu .divider {
  margin: calc(15px / var(--system-scale-factor, 1)) 0 !important;
  height: calc(2px / var(--system-scale-factor, 1)) !important;
}

/* 修复侧边菜单按钮缩放 */
.side-menu button,
.side-menu .btn,
.side-menu input[type="button"],
.side-menu input[type="submit"] {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  padding: calc(10px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  margin: calc(8px / var(--system-scale-factor, 1)) 0 !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复历史经度页面内联样式的图标高度缩放 */
#geo-longitude-close-btn {
  width: calc(40px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  top: calc(20px / var(--system-scale-factor, 1)) !important;
  right: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复历史经度页面表单元素缩放 */
#geo-longitude-form label {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(12px / var(--system-scale-factor, 1)) !important;
}

#geo-longitude-form input {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(16px / var(--system-scale-factor, 1)) !important;
}

#geo-longitude-form div {
  margin-right: calc(24px / var(--system-scale-factor, 1)) !important;
}

#geo-longitude-get-data {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复历史经度页面图表容器缩放 */
#geo-longitude-chart-container {
  height: calc(1080px / var(--system-scale-factor, 1)) !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

#geo-longitude-stats-title {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  margin: calc(12px / var(--system-scale-factor, 1)) 0 calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

#geo-longitude-echarts-container {
  height: calc(80px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复全屏图表菜单的缩放 - 使用反向缩放逻辑 */
#inc2lon-plots-menu,
#inc2alt-plots-menu,
#lat2lon-plots-menu,
#time2lon-plots-menu {
  /* 全屏容器不需要transform缩放，而是内部元素使用反向缩放 */
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
  background: rgba(0, 0, 0, 0.95) !important;
}

/* 修复全屏图表菜单的关闭按钮缩放 */
#inc2lon-close-btn,
#inc2alt-close-btn,
#lat2lon-close-btn,
#time2lon-close-btn {
  width: calc(40px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  top: calc(20px / var(--system-scale-factor, 1)) !important;
  right: calc(20px / var(--system-scale-factor, 1)) !important;
  position: fixed !important;
  z-index: 10000 !important;
}

/* 修复全屏图表菜单的标题缩放 */
#inc2lon-plots-menu h5,
#inc2alt-plots-menu h5,
#lat2lon-plots-menu h5,
#time2lon-plots-menu h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(20px / var(--system-scale-factor, 1)) !important;
  color: white !important;
  text-align: center !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 修复全屏图表菜单的内容区域缩放 */
#inc2lon-plots-menu #plot-analysis-content,
#inc2alt-plots-menu #plot-analysis-content,
#lat2lon-plots-menu #plot-analysis-content,
#time2lon-plots-menu #plot-analysis-content {
  width: 100vw !important;
  height: 100vh !important;
  padding: calc(20px / var(--system-scale-factor, 1)) !important;
  box-sizing: border-box !important;
}

/* 修复全屏图表容器缩放 */
#plot-analysis-chart-inc2lon,
#plot-analysis-chart-inc2alt,
#plot-analysis-chart-lat2lon,
#plot-analysis-chart-time2lon {
  width: 100% !important;
  height: calc(100vh - 100px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 系统性缩放修复 - 检查遗漏的控件 🔥🔥🔥 */

/* 修复Toast消息缩放 */
.toast,
#toast-container .toast {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(15px / var(--system-scale-factor, 1)) !important;
  border-radius: 0 !important;
  min-height: calc(48px / var(--system-scale-factor, 1)) !important;
}

/* 修复搜索框缩放 - 完全透明 */
#search,
.search-input,
#search-holder input {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; /* 只保留底部细线 */
  border-radius: 0 !important; /* 移除圆角 */
  background: transparent !important; /* 🔥 确保完全透明背景 */
  background-color: transparent !important; /* 🔥 强制透明背景色 */
  background-image: none !important; /* 🔥 移除背景图片 */
  backdrop-filter: none !important; /* 🔥 移除模糊效果 */
  -webkit-backdrop-filter: none !important; /* 🔥 移除webkit模糊效果 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
  -webkit-box-shadow: none !important; /* 移除webkit阴影 */
}

/* 修复搜索结果缩放 */
.search-result,
.search-results li {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复右键菜单缩放 */
.context-menu,
.right-btn-menu {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

.context-menu li,
.right-btn-menu li {
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复模态框缩放 */
.modal,
.modal-content {
  border-radius: calc(8px / var(--system-scale-factor, 1)) !important;
}

.modal-content {
  padding: calc(20px / var(--system-scale-factor, 1)) !important;
}

.modal h4,
.modal h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(16px / var(--system-scale-factor, 1)) !important;
}

.modal p,
.modal div {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}



/* 修复顶部菜单缩放 */
.menu-item {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  height: calc(var(--nav-bar-height) / var(--system-scale-factor, 1)) !important;
}

/* 🔥 恢复原始底部菜单滑动系统，但默认显示 */

/* 确保底部菜单容器有正确的基础样式 */
#nav-footer {
  position: fixed !important;
  z-index: 100 !important;
  width: 100% !important;
}

/* 恢复原始滑动类，但修正定位值 */
.footer-slide-down {
  bottom: calc(-130px / var(--system-scale-factor, 1)) !important;
  /* 🔥 移除固定高度，允许拖动调整 */
  top: auto !important;
  transition: bottom 1s ease !important;
}

.footer-slide-up {
  bottom: 0px !important;
  top: auto !important;
  transition: bottom 1s ease !important;
}

.footer-slide-trans {
  transition: bottom 1s ease !important;
}

/* 确保底部菜单内容容器正确显示 */
#bottom-icons-container {
  width: 100% !important;
  height: var(--bottom-menu-height) !important;
  display: block !important;
  background: transparent !important;
  position: relative !important;
}

/* 🔥🔥🔥 底部菜单过滤器布局 - 专注于flex布局 🔥🔥🔥 */
#bottom-icons-filter {
  /* 核心flex布局 */
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;

  /* 布局属性 */
  width: var(--bottom-filter-width, 185px) !important;
  height: var(--bottom-menu-height, 120px) !important;
  float: left !important;
  padding: 10px !important;
  z-index: 11 !important;
  position: absolute !important;
  overflow-y: scroll !important;
}

/* 🔥🔥🔥 底部菜单核心布局 - 专注于grid布局，透明效果由transparency-unified.css处理 🔥🔥🔥 */
#bottom-icons {
  /* 核心grid布局 */
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;

  /* 布局属性 */
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

/* 修复Logo缩放 */
#logo-primary {
  width: calc(4vw / var(--system-scale-factor, 1)) !important;
  max-width: calc(150px / var(--system-scale-factor, 1)) !important;
  min-width: calc(70px / var(--system-scale-factor, 1)) !important;
  top: calc((var(--top-menu-height) + 10px) / var(--system-scale-factor, 1)) !important;
  left: calc(10px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 终极底部菜单修复 - 文件末尾最高优先级 🔥🔥🔥 */
/* 这些规则必须在文件最后，确保覆盖所有其他CSS */

/* 底部菜单图标区域 - 横向grid布局 */
html[lang] body #bottom-icons,
html body #bottom-icons,
body #bottom-icons,
#bottom-icons {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
}

/* 底部菜单过滤器 - 左侧垂直布局 */
html[lang] body #bottom-icons-filter,
html body #bottom-icons-filter,
body #bottom-icons-filter,
#bottom-icons-filter {
  display: flex !important;
  flex-direction: column !important;
  width: var(--bottom-filter-width, 185px) !important;
  float: left !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  z-index: 11 !important;
}

/* 底部菜单容器 */
html[lang] body #bottom-icons-container,
html body #bottom-icons-container,
body #bottom-icons-container,
#bottom-icons-container {
  display: block !important;
  width: 100% !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  position: relative !important;
  border: none !important; /* 移除所有边框 */
  padding: 0 !important; /* 移除内边距 */
  margin: 0 !important; /* 移除外边距 */
}

/* 拖动区域样式 - 确保不干扰主画布事件 */
.drag-resize-handle {
  pointer-events: auto !important;
  z-index: 1000 !important;
  cursor: n-resize !important; /* 确保显示正确的光标 */
  background: rgba(255, 255, 255, 0.1) !important; /* 轻微背景便于识别 */
  border-top: 1px solid rgba(255, 255, 255, 0.3) !important;
  height: 4px !important; /* 增加高度便于拖动 */
}

/* 确保主画布区域的鼠标事件正常 */
#keeptrack-canvas {
  pointer-events: auto !important;
  z-index: 1 !important;
}

/* 页脚容器 */
html[lang] body #nav-footer,
html body #nav-footer,
body #nav-footer,
#nav-footer {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important; /* 移除所有边框 */
  padding: 0 !important; /* 移除内边距 */
  margin: 0 !important; /* 移除外边距 */
}

/* 🔥🔥🔥 终极强制修复 - 使用最高优先级选择器 🔥🔥🔥 */
html[lang="en"] body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#bottom-icons,
html[lang] body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang] body div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang] body div#bottom-icons-container div#bottom-icons,
html[lang] body div#bottom-icons,
html body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html body div#nav-footer div#bottom-icons-container div#bottom-icons,
html body div#bottom-icons-container div#bottom-icons,
html body div#bottom-icons,
body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
body div#nav-footer div#bottom-icons-container div#bottom-icons,
body div#bottom-icons-container div#bottom-icons,
body div#bottom-icons,
footer div#nav-footer div#bottom-icons-container div#bottom-icons,
div#nav-footer div#bottom-icons-container div#bottom-icons,
div#bottom-icons-container div#bottom-icons,
div#bottom-icons,
#nav-footer #bottom-icons-container #bottom-icons,
#bottom-icons-container #bottom-icons,
#bottom-icons {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

/* 修复表格缩放 */
table,
.table {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

table th,
table td,
.table th,
.table td {
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复复选框和单选框缩放 */
input[type="checkbox"],
input[type="radio"] {
  width: calc(18px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 修复标签缩放 */
label {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}

/* 修复进度条缩放 */
.progress,
.progress-bar {
  height: calc(20px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(10px / var(--system-scale-factor, 1)) !important;
}

/* 修复工具提示缩放 */
.tooltip,
[data-tooltip]:before {
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复分页器缩放 */
.pagination,
.pagination li {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

.pagination li a {
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 修复面包屑导航缩放 */
.breadcrumb {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
}

/* 修复徽章缩放 */
.badge,
.chip {
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  padding: calc(4px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(12px / var(--system-scale-factor, 1)) !important;
}

/* 修复卡片缩放 */
.card {
  border-radius: calc(8px / var(--system-scale-factor, 1)) !important;
}

.card-content {
  padding: calc(16px / var(--system-scale-factor, 1)) !important;
}

.card-title {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(12px / var(--system-scale-factor, 1)) !important;
}

/* 修复选项卡缩放 */
.tabs {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

.tabs .tab a {
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 修复手风琴缩放 */
.collapsible-header {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
}

.collapsible-body {
  padding: calc(16px / var(--system-scale-factor, 1)) !important;
}

/* 修复侧边导航缩放 */
.sidenav {
  width: calc(300px / var(--system-scale-factor, 1)) !important;
}

.sidenav li a {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
}





/* ========================================
 * 8. 自动填充样式修复
 * ======================================== */
[id$="-menu"] input:-webkit-autofill,
[id$="-menu"] input:-webkit-autofill:hover,
[id$="-menu"] input:-webkit-autofill:focus,
[id$="-menu"] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px #1a1a1a inset !important;
  -webkit-box-shadow: 0 0 0 30px #1a1a1a inset !important;
  -webkit-text-fill-color: white !important;
  transition: none !important;
  animation: none !important;
}

/* 🔥🔥🔥 终极底部菜单修复 - 确保grid布局生效 🔥🔥🔥 */
/* 注意：透明效果已移至transparency-unified.css，这里只处理布局 */

#bottom-icons {
  /* 强制grid布局 */
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;

  /* 布局属性 */
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

#bottom-icons-filter {
  /* 强制flex布局 */
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;

  /* 布局属性 */
  width: var(--bottom-filter-width, 185px) !important;
  height: var(--bottom-menu-height, 120px) !important;
  float: left !important;
  padding: 10px !important;
  z-index: 11 !important;
  position: absolute !important;
  overflow-y: scroll !important;
}
