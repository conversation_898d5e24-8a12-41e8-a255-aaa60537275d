const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function simpleGeoTest() {
  try {
    console.log('简单GEO测试...\n');
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 30000,
    });

    // 1. 直接查询GEO记录（不加时间限制）
    console.log('1. 直接查询GEO记录（不加时间限制）...');

    // 尝试不同的查询方式
    console.log('尝试使用 .keyword 字段...');
    const geoResult = await client.search({
      index: esConfig.index,
      size: 5,
      query: {
        term: { "orbital_elements.object_type.keyword": "GEO" }
      },
      sort: [{ time: { order: 'desc' } }]
    });
    
    console.log(`找到 ${geoResult.hits.total.value} 条GEO记录`);
    
    if (geoResult.hits?.hits?.length > 0) {
      console.log('\n最新的5条GEO记录:');
      geoResult.hits.hits.forEach((hit, index) => {
        const record = hit._source;
        console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name}`);
        console.log(`   时间: ${record.time}`);
        console.log(`   经度: ${record.orbital_elements?.subsat_long}°`);
        console.log('');
      });
      
      // 2. 测试带时间范围的查询
      console.log('2. 测试带时间范围的查询...');
      const latestRecord = geoResult.hits.hits[0]._source;
      const latestTime = new Date(latestRecord.time);
      const oneYearBefore = new Date(latestTime);
      oneYearBefore.setFullYear(latestTime.getFullYear() - 1);
      
      console.log(`查询时间范围: ${oneYearBefore.toISOString()} 到 ${latestTime.toISOString()}`);

      const timeRangeResult = await client.search({
        index: esConfig.index,
        size: 10,
        query: {
          bool: {
            must: [
              { range: { time: { gte: oneYearBefore.toISOString(), lte: latestTime.toISOString() } } },
              { term: { "orbital_elements.object_type.keyword": "GEO" } },
              { exists: { field: "orbital_elements.subsat_long" } }
            ]
          }
        },
        sort: [{ time: 'desc' }]
      });
      
      console.log(`时间范围内找到 ${timeRangeResult.hits.total.value} 条GEO记录`);
      
      if (timeRangeResult.hits?.hits?.length > 0) {
        console.log('\n时间范围内的前10条记录:');
        timeRangeResult.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`${index + 1}. NORAD: ${record.norad_id}, 名称: ${record.satellite_name}`);
          console.log(`   时间: ${record.time}`);
          console.log(`   经度: ${record.orbital_elements?.subsat_long}°`);
          console.log('');
        });
        
        // 3. 测试API
        console.log('3. 测试API接口...');
        const startDate = oneYearBefore.toISOString().split('T')[0];
        const endDate = latestTime.toISOString().split('T')[0];
        
        console.log(`API测试: /api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
        
        try {
          const fetch = require('node-fetch');
          const response = await fetch(`http://localhost:3001/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
          
          if (response.ok) {
            const data = await response.json();
            console.log(`✓ API成功返回 ${data.data?.length || 0} 条记录`);
            
            if (data.data && data.data.length > 0) {
              console.log('\nAPI返回的前3条记录:');
              data.data.slice(0, 3).forEach((record, index) => {
                console.log(`${index + 1}. NORAD: ${record.norad_id}`);
                console.log(`   名称: ${record.satellite_name || record.object_name}`);
                console.log(`   时间: ${record.time}`);
                console.log(`   经度: ${record.subsat_long}°`);
                console.log('');
              });
              
              console.log('✅ GEO卫星经度历史功能测试成功！');
            }
          } else {
            console.log(`✗ API请求失败: ${response.status}`);
            const errorText = await response.text();
            console.log('错误:', errorText);
          }
        } catch (apiError) {
          console.log('✗ API测试失败:', apiError.message);
          console.log('请确保API服务器正在运行');
        }
      }
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

simpleGeoTest();
