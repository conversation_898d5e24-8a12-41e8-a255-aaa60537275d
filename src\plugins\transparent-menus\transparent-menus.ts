/**
 * 透明菜单插件 - 强制设置菜单背景为透明
 */

import { KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { KeepTrackPlugin } from '@app/plugins/KeepTrackPlugin';

export class TransparentMenus extends KeepTrackPlugin {
  readonly id = 'TransparentMenus';
  dependencies_ = [];

  init() {
    super.init();
  }

  addHtml() {
    super.addHtml();
  }

  addJs() {
    super.addJs();

    // 在UI初始化完成后设置透明背景
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => {
      this.setTransparentBackgrounds();

      // 🔧 优化：只在特定事件时重新应用样式，而不是定时器
      this.setupEventBasedStyleApplication();
    });
  }

  private setTransparentBackgrounds() {
    try {
      // 底部菜单相关元素
      const bottomMenuElements = [
        'bottom-icons',
        'bottom-icons-filter',
        'bottom-icons-container',
        'nav-footer'
      ];

      bottomMenuElements.forEach(elementId => {
        const element = getEl(elementId);
        if (element) {
          element.style.setProperty('background', 'transparent', 'important');
          element.style.setProperty('background-color', 'transparent', 'important');
          element.style.setProperty('background-image', 'none', 'important');
          element.style.setProperty('backdrop-filter', 'blur(10px)', 'important');
          element.style.setProperty('-webkit-backdrop-filter', 'blur(10px)', 'important');

          // 确保底部菜单可见和可交互 - 特殊处理bottom-icons为grid布局
          if (elementId === 'bottom-icons') {
            element.style.setProperty('display', 'grid', 'important');
            element.style.setProperty('grid-template-columns', 'repeat(auto-fill, 80px)', 'important');
            element.style.setProperty('justify-content', 'center', 'important');
            element.style.setProperty('column-gap', '2px', 'important');
          } else {
            element.style.setProperty('display', 'block', 'important');
          }
          element.style.setProperty('visibility', 'visible', 'important');
          element.style.setProperty('pointer-events', 'auto', 'important');
          element.style.setProperty('opacity', '1', 'important');

          // 强制移除任何可能的CSS类
          try {
            element.classList.remove('dark-background', 'menu-background');
          } catch (e) {
            // 忽略classList错误
          }
        }
      });

    // 侧边菜单相关元素 - 强化透明设置
    const sideMenuElements = [
      'side-menu-parent',
      'side-menu',
      'side-menu-settings'
    ];

    sideMenuElements.forEach(elementId => {
      const element = getEl(elementId);
      if (element) {
        element.style.setProperty('background', 'transparent', 'important');
        element.style.setProperty('background-color', 'transparent', 'important');
        element.style.setProperty('background-image', 'none', 'important');
        element.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        element.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        element.style.setProperty('border', 'none', 'important');
        element.style.setProperty('box-shadow', 'none', 'important');
      }
    });

    // 强制设置所有侧边菜单类的元素
    const sideMenuSelectors = [
      '.side-menu-parent',
      '.side-menu',
      '.side-menu-settings',
      '[id$="-menu"]'
    ];

    sideMenuSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('background-image', 'none', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('border', 'none', 'important');
        htmlElement.style.setProperty('box-shadow', 'none', 'important');
      });
    });

    // sat-info-box主容器 - 无色透明模糊，只依靠模糊效果
    const satInfobox = getEl('sat-infobox');
    if (satInfobox) {
      satInfobox.style.setProperty('background', 'transparent', 'important');
      satInfobox.style.setProperty('background-color', 'transparent', 'important');
      satInfobox.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
      satInfobox.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
      satInfobox.style.border = 'none';
      satInfobox.style.boxShadow = 'none';
    }

    // 🔧 时间管理器窗口透明模糊 - 与sat-info-box一致
    const timeManagerSelectors = [
      '#ui-datepicker-div',
      '.ui-timepicker-div',
      '.ui-datepicker',
      '.ui-timepicker-wrapper'
    ];

    timeManagerSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('border', 'none', 'important');
        htmlElement.style.setProperty('box-shadow', 'none', 'important');
        htmlElement.style.setProperty('border-radius', '8px', 'important');
      });
    });

    // 🔧 搜索结果窗口透明模糊 - 与sat-info-box一致
    const searchResultsSelectors = [
      '#search-results',
      '.search-results',
      '.search-container',
      '.search-dropdown'
    ];

    searchResultsSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('border', 'none', 'important');
        htmlElement.style.setProperty('box-shadow', 'none', 'important');
        htmlElement.style.setProperty('border-radius', '8px', 'important');
      });
    });

    // sat-info-header和sat-info-title保持原有背景，不设置为透明
    const satInfoHeader = getEl('sat-info-header');
    if (satInfoHeader) {
      // 移除任何透明设置，让CSS控制
      satInfoHeader.style.removeProperty('background');
      satInfoHeader.style.removeProperty('background-color');
      satInfoHeader.style.removeProperty('background-image');
    }

    const satInfoTitle = getEl('sat-info-title');
    if (satInfoTitle) {
      // 强制设置深蓝黑色背景
      satInfoTitle.style.setProperty('background', '#0d47a1', 'important');
      satInfoTitle.style.setProperty('background-color', '#0d47a1', 'important');
      satInfoTitle.style.setProperty('backdrop-filter', 'blur(10px)', 'important');
      satInfoTitle.style.setProperty('-webkit-backdrop-filter', 'blur(10px)', 'important');


    }

    // 卫星悬停信息框 - 移除边框
    const hoverBoxElements = [
      'sat-hoverbox',
      'sat-minibox'
    ];

    hoverBoxElements.forEach(elementId => {
      const element = getEl(elementId);
      if (element) {
        element.style.border = 'none';
        element.style.borderWidth = '0';
        element.style.boxShadow = 'none';
      }
    });

    // 🔧 保护搜索相关元素，确保功能正常
    const searchProtectedElements = [
      'search', 'clear-search', 'search-icon', 'search-holder',
      'search-results', 'search-clear'
    ];

    searchProtectedElements.forEach(elementId => {
      const element = getEl(elementId);
      if (element) {
        // 确保搜索元素的事件和样式不被干扰
        element.style.setProperty('pointer-events', 'auto', 'important');
        element.style.setProperty('z-index', '9999', 'important');
        element.style.setProperty('opacity', '1', 'important');
        element.style.setProperty('visibility', 'visible', 'important');
      }
    });

    // 底部菜单项目 - 强制设置菜单图标背景透明，移除悬停效果
    const bmenuItems = document.querySelectorAll('.bmenu-item, .bmenu-filter-item, .bmenu-item-inner, .bmenu-filter-item-inner');
    bmenuItems.forEach(item => {
      const element = item as HTMLElement;
      element.style.setProperty('background', 'transparent', 'important');
      element.style.setProperty('background-color', 'transparent', 'important');
      element.style.setProperty('background-image', 'none', 'important');
      element.style.setProperty('border', 'none', 'important');
      element.style.setProperty('outline', 'none', 'important');
      element.style.setProperty('box-shadow', 'none', 'important');
      element.style.setProperty('transition', 'none', 'important');

      // 移除悬停效果 - 不添加任何事件监听器
    });

    // sat-info-box分区标题和参数行 - 只处理.sat-info-row，不处理section-header
    const satInfoRows = document.querySelectorAll('.sat-info-row');
    satInfoRows.forEach(row => {
      const element = row as HTMLElement;
      element.style.background = 'transparent';
      element.style.backgroundColor = 'transparent';
      element.style.backgroundImage = 'none';
      element.style.transition = 'background-color 0.3s ease';

      // 添加悬停事件
      element.addEventListener('mouseenter', () => {
        element.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
      });

      element.addEventListener('mouseleave', () => {
        element.style.backgroundColor = 'transparent';
      });
    });

    // 单独处理section-header，保持原有样式和悬停效果
    const sectionHeaders = document.querySelectorAll('.sat-info-section-header:not(#sat-info-title)');
    sectionHeaders.forEach(header => {
      const element = header as HTMLElement;
      // 不设置透明背景，保持原有样式
      element.style.transition = 'background-color 0.2s ease';

      // 确保悬停效果正常 - 使用深蓝黑色
      element.addEventListener('mouseenter', () => {
        element.style.setProperty('background-color', '#0d47a1', 'important');
      });

      element.addEventListener('mouseleave', () => {
        element.style.setProperty('background-color', 'transparent', 'important'); // 恢复透明背景
      });
    });

    // sat-info-title已在上面设置，这里不需要重复

      // 强制设置所有可能的底部菜单元素
      this.forceTransparentBySelectors();

      // 延迟再次应用样式，确保覆盖其他CSS
      setTimeout(() => {
        this.applySatInfoBoxStyles();
        this.applyTimeManagerStyles();
        this.applySearchResultsStyles();
      }, 100);
    } catch (error) {
      console.warn('透明背景设置出错:', error);
    }
  }

  private applySatInfoBoxStyles(): void {
    // 强制应用sat-info-box样式 - 无色透明模糊
    const satInfobox = getEl('sat-infobox');
    if (satInfobox) {
      satInfobox.style.setProperty('background', 'transparent', 'important');
      satInfobox.style.setProperty('background-color', 'transparent', 'important');
    }

    const satInfoTitle = getEl('sat-info-title');
    if (satInfoTitle) {
      satInfoTitle.style.setProperty('background', '#0d47a1', 'important');
      satInfoTitle.style.setProperty('background-color', '#0d47a1', 'important');
    }
  }

  private applyTimeManagerStyles(): void {
    // 🔧 强制应用时间管理器透明样式
    const timeManagerSelectors = [
      '#ui-datepicker-div',
      '.ui-timepicker-div',
      '.ui-datepicker',
      '.ui-timepicker-wrapper'
    ];

    timeManagerSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('border', 'none', 'important');
        htmlElement.style.setProperty('box-shadow', 'none', 'important');
        htmlElement.style.setProperty('border-radius', '8px', 'important');
      });
    });
  }

  private applySearchResultsStyles(): void {
    // 🔧 强制应用搜索结果透明样式
    const searchResultsSelectors = [
      '#search-results',
      '.search-results',
      '.search-container',
      '.search-dropdown'
    ];

    searchResultsSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(15px)', 'important');
        htmlElement.style.setProperty('border', 'none', 'important');
        htmlElement.style.setProperty('box-shadow', 'none', 'important');
        htmlElement.style.setProperty('border-radius', '8px', 'important');
      });
    });
  }

  private setupEventBasedStyleApplication(): void {
    // 🔧 优化：使用事件驱动而不是MutationObserver

    // 监听底部菜单相关事件
    keepTrackApi.on(KeepTrackApiEvents.bottomMenuClick, () => {
      // 延迟应用样式，确保DOM更新完成
      setTimeout(() => this.setTransparentBackgrounds(), 50);
    });

    // 监听窗口大小变化事件
    keepTrackApi.on(KeepTrackApiEvents.resize, () => {
      setTimeout(() => this.setTransparentBackgrounds(), 50);
    });

    // 🔧 重写原生方法来拦截时间管理器和搜索结果的创建
    this.interceptUICreation();
  }

  private interceptUICreation(): void {
    // 🔧 移除原生方法拦截，避免影响其他功能
    // 只在特定事件时应用样式，不拦截原生方法
  }

  private forceTransparentBySelectors() {
    // 使用多种选择器强制设置透明背景
    const selectors = [
      '#bottom-icons',
      '#bottom-icons-filter',
      '#bottom-icons-container',
      '#nav-footer',
      'footer#nav-footer',
      'footer',
      '[id="bottom-icons"]',
      '[id="bottom-icons-filter"]',
      '[id="bottom-icons-container"]',
      '[id="nav-footer"]',
      '.side-menu-parent',
      '.side-menu',
      '.side-menu-settings'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.setProperty('background', 'transparent', 'important');
        htmlElement.style.setProperty('background-color', 'transparent', 'important');
        htmlElement.style.setProperty('background-image', 'none', 'important');
        htmlElement.style.setProperty('backdrop-filter', 'blur(10px)', 'important');
        htmlElement.style.setProperty('-webkit-backdrop-filter', 'blur(10px)', 'important');
      });
    });
  }
}
