import { KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { isThisNode } from '@app/static/isThisNode';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { TopMenu } from '../top-menu/top-menu';
import { Calendar } from './calendar';

export class DateTimeManager extends KeepTrackPlugin {
  readonly id = 'DateTimeManager';
  dependencies_ = [TopMenu.name];
  isEditTimeOpen = false;
  private readonly dateTimeContainerId_ = 'datetime';
  private readonly dateTimeInputTbId_ = 'datetime-input-tb';
  calendar: Calendar;

  init(): void {
    super.init();

    keepTrackApi.on(KeepTrackApiEvents.uiManagerInit, this.uiManagerInit.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, this.uiManagerFinal.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.updateDateTime, this.updateDateTime.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.onKeepTrackReady, () => this.updateDateTime(keepTrackApi.getTimeManager().simulationTimeObj));
  }

  updateDateTime(date: Date) {
    const dateTimeInputTb = document.getElementById(this.dateTimeInputTbId_) as HTMLInputElement;

    if (dateTimeInputTb && !isThisNode()) {
      dateTimeInputTb.value = date.toISOString().split('T')[0]; // Format the date as yyyy-mm-dd
    }

    //  Jday isn't initalized right away, so we need to check if it exists
    if (!getEl('jday')) {
      return;
    }

    const simulationDate = keepTrackApi.getTimeManager().simulationTimeObj;
    const year = simulationDate.getUTCFullYear();
    const month = (simulationDate.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = simulationDate.getUTCDate().toString().padStart(2, '0');

    getEl('jday')!.innerHTML = `UTC ${year}-${month}-${day}`;
    getEl('jday')!.style.fontFamily = '"Microsoft YaHei", "微软雅黑", sans-serif';
  }

  datetimeTextClick(): void {
    const simulationDateObj = new Date(keepTrackApi.getTimeManager().simulationTimeObj);
    const timeManagerInstance = keepTrackApi.getTimeManager();

    timeManagerInstance.synchronize();

    this.updateDateTime(simulationDateObj);
    this.calendar.setDate(simulationDateObj);
    this.calendar.toggleDatePicker();

    if (!this.isEditTimeOpen) {
      const datetimeInput = getEl('datetime-input');
      const datetimeInputTb = getEl(this.dateTimeInputTbId_);

      if (datetimeInput && datetimeInputTb) {
        datetimeInput.style.display = 'block';
        (datetimeInputTb as HTMLInputElement).focus();
        this.isEditTimeOpen = true;
      }

    }
  }

  uiManagerInit() {
    // 延迟执行以确保TopMenu已经创建了nav-wrapper
    setTimeout(() => {
      const NavWrapper = getEl('nav-wrapper');

      if (!NavWrapper) {
        console.warn('nav-wrapper not found, TopMenu plugin may not be enabled');
        return;
      }

      // 检查是否已经存在nav-mobile，避免重复创建
      if (getEl('nav-mobile')) {
        console.log('nav-mobile already exists, skipping creation');
        return;
      }

      NavWrapper.insertAdjacentHTML(
        'afterbegin',
        keepTrackApi.html`
          <div id="nav-mobile">
            <div id="jday" style="color: white; font-size: 14px; padding: 8px; display: block;"></div>
            <div id="${this.dateTimeContainerId_}">
              <div id="datetime-text" class="waves-effect waves-light" style="font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;">Placeholder Text</div>
              <div id="datetime-input">
                <form id="datetime-input-form">
                  <input type="text" id="${this.dateTimeInputTbId_}" readonly="true" />
                </form>
              </div>
            </div>
            <div id="non-realtime-indicator" class="start-hidden">非实时</div>
          </div>`,
      );

      // 立即更新日期显示
      setTimeout(() => {
        this.updateDateTime(keepTrackApi.getTimeManager().simulationTimeObj);

        // 🔥 在DOM创建后初始化非实时指示器的点击事件
        const timeManager = keepTrackApi.getTimeManager();
        if (timeManager) {
          console.log('🔍 [DATE-TIME-MANAGER] 初始化非实时指示器点击事件');
          timeManager.initializeNonRealtimeIndicatorClick();
          timeManager.updateNonRealtimeIndicator();
        }
      }, 100);
    }, 100); // 增加延迟确保TopMenu完全加载
  }

  uiManagerFinal() {
    console.log('DateTimeManager uiManagerFinal called');
    console.log('TopMenu plugin config:', settingsManager.plugins.TopMenu);
    if (!settingsManager.plugins.TopMenu?.enabled) {
      console.log('TopMenu plugin not enabled, skipping DateTimeManager final setup');
      return;
    }

    this.calendar = new Calendar('datetime-input-form');

    document.getElementById('datetime-text')?.addEventListener('click', this.datetimeTextClick.bind(this));

    const datetimeInputTb = document.getElementById(this.dateTimeInputTbId_);

    if (datetimeInputTb && !isThisNode()) {
      datetimeInputTb.addEventListener('change', () => {
        if (this.isEditTimeOpen) {
          // const datetimeInputElement = document.getElementById('datetime-input');

          /*
           * TODO: Why was this originally !datetimeInputElement???
           * if (datetimeInputElement) {
           * datetimeInputElement.style.display = 'none';
           * }
           */
          setTimeout(() => {
            this.isEditTimeOpen = false;
          }, 500);

          try {
            const uiManagerInstance = keepTrackApi.getUiManager();

            uiManagerInstance.updateNextPassOverlay(true);
          } catch {
            // Intentionally ignored
          }
        }
      });
    }
  }
}
