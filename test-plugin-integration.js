/**
 * 测试GEO卫星经度历史插件集成
 */

const fs = require('fs');
const path = require('path');

function testPluginIntegration() {
  console.log('开始测试插件集成...\n');
  
  let allTestsPassed = true;
  
  // 测试1: 检查插件文件是否存在
  console.log('测试1: 检查插件文件');
  const pluginFile = 'src/plugins/geo-longitude-history/geo-longitude-history.ts';
  if (fs.existsSync(pluginFile)) {
    console.log('✓ 插件文件存在');
  } else {
    console.log('✗ 插件文件不存在');
    allTestsPassed = false;
  }
  
  // 测试2: 检查API支持（使用现有的es-history API）
  console.log('\n测试2: 检查API支持');
  const apiFile = 'src/api/es-history.routes.ts';
  if (fs.existsSync(apiFile)) {
    const content = fs.readFileSync(apiFile, 'utf8');
    if (content.includes('geo_only')) {
      console.log('✓ API已支持GEO卫星查询');
    } else {
      console.log('✗ API未支持GEO卫星查询');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ es-history API文件不存在');
    allTestsPassed = false;
  }
  
  // 测试3: 检查插件是否已注册
  console.log('\n测试3: 检查插件注册');
  const pluginsFile = 'src/plugins/plugins.ts';
  if (fs.existsSync(pluginsFile)) {
    const content = fs.readFileSync(pluginsFile, 'utf8');
    if (content.includes('GeoLongitudeHistoryPlugin')) {
      console.log('✓ 插件已在plugins.ts中注册');
    } else {
      console.log('✗ 插件未在plugins.ts中注册');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ plugins.ts文件不存在');
    allTestsPassed = false;
  }
  
  // 测试4: 检查插件配置
  console.log('\n测试4: 检查插件配置');
  const configFile = 'src/settings/default-plugins.ts';
  if (fs.existsSync(configFile)) {
    const content = fs.readFileSync(configFile, 'utf8');
    if (content.includes('GeoLongitudeHistoryPlugin')) {
      console.log('✓ 插件已在default-plugins.ts中配置');
    } else {
      console.log('✗ 插件未在default-plugins.ts中配置');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ default-plugins.ts文件不存在');
    allTestsPassed = false;
  }
  
  // 测试5: 检查类型定义
  console.log('\n测试5: 检查类型定义');
  const typeFile = 'src/plugins/keeptrack-plugins-configuration.ts';
  if (fs.existsSync(typeFile)) {
    const content = fs.readFileSync(typeFile, 'utf8');
    if (content.includes('GeoLongitudeHistoryPlugin')) {
      console.log('✓ 插件类型已在keeptrack-plugins-configuration.ts中定义');
    } else {
      console.log('✗ 插件类型未在keeptrack-plugins-configuration.ts中定义');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ keeptrack-plugins-configuration.ts文件不存在');
    allTestsPassed = false;
  }
  
  // 测试6: 检查本地化
  console.log('\n测试6: 检查本地化');
  const localeFile = 'src/locales/zh.json';
  if (fs.existsSync(localeFile)) {
    const content = fs.readFileSync(localeFile, 'utf8');
    if (content.includes('GeoLongitudeHistoryPlugin')) {
      console.log('✓ 插件本地化已在zh.json中添加');
    } else {
      console.log('✗ 插件本地化未在zh.json中添加');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ zh.json文件不存在');
    allTestsPassed = false;
  }
  
  // 测试7: 检查API路由支持
  console.log('\n测试7: 检查API路由支持');
  const serverFile = 'src/api/server.ts';
  if (fs.existsSync(serverFile)) {
    const content = fs.readFileSync(serverFile, 'utf8');
    if (content.includes('es-history')) {
      console.log('✓ es-history API路由已在server.ts中注册（支持GEO查询）');
    } else {
      console.log('✗ es-history API路由未在server.ts中注册');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ server.ts文件不存在');
    allTestsPassed = false;
  }
  
  // 测试8: 检查插件代码语法
  console.log('\n测试8: 检查插件代码基本语法');
  try {
    const pluginContent = fs.readFileSync(pluginFile, 'utf8');
    
    // 检查基本的类定义
    if (pluginContent.includes('export class GeoLongitudeHistoryPlugin')) {
      console.log('✓ 插件类定义正确');
    } else {
      console.log('✗ 插件类定义有问题');
      allTestsPassed = false;
    }
    
    // 检查必要的方法
    const requiredMethods = ['bottomIconCallback', 'addJs', 'handleGetData_', 'displayChart_'];
    let methodsOk = true;
    for (const method of requiredMethods) {
      if (!pluginContent.includes(method)) {
        console.log(`✗ 缺少必要方法: ${method}`);
        methodsOk = false;
        allTestsPassed = false;
      }
    }
    if (methodsOk) {
      console.log('✓ 必要方法都存在');
    }
    
  } catch (error) {
    console.log('✗ 读取插件文件时出错:', error.message);
    allTestsPassed = false;
  }
  
  // 总结
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('🎉 所有测试通过！插件集成成功！');
    console.log('\n下一步:');
    console.log('1. 启动开发服务器');
    console.log('2. 确保ES数据库配置正确');
    console.log('3. 在浏览器中测试新功能');
  } else {
    console.log('❌ 部分测试失败，请检查上述错误');
  }
  
  return allTestsPassed;
}

// 运行测试
if (require.main === module) {
  testPluginIntegration();
}

module.exports = { testPluginIntegration };
