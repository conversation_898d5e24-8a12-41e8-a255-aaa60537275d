/**
 * 测试API返回的数据格式
 */

const fetch = require('node-fetch');

async function testAPIDataFormat() {
  console.log('测试API返回的数据格式...\n');
  
  try {
    const baseUrl = 'http://198.18.0.1:3001';
    
    // 使用较大的时间范围确保有数据
    const endDate = '2025-07-19';
    const startDate = '2024-07-19';
    
    console.log(`查询时间范围: ${startDate} 到 ${endDate}`);
    console.log(`API URL: ${baseUrl}/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
    
    const response = await fetch(`${baseUrl}/api/es-history?start=${startDate}&end=${endDate}&geo_only=true`);
    
    if (!response.ok) {
      console.log(`✗ API请求失败: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.log('错误信息:', errorText);
      return;
    }
    
    const data = await response.json();
    
    console.log(`✓ API请求成功`);
    console.log(`✓ 返回记录数: ${data.data?.length || 0}`);
    
    if (data.data && data.data.length > 0) {
      console.log('\n数据格式检查:');
      
      // 检查前3条记录的数据格式
      const sampleRecords = data.data.slice(0, 3);
      
      sampleRecords.forEach((record, index) => {
        console.log(`\n记录 ${index + 1}:`);
        console.log(`  norad_id: ${record.norad_id} (${typeof record.norad_id})`);
        console.log(`  time: ${record.time} (${typeof record.time})`);
        console.log(`  satellite_name: ${record.satellite_name} (${typeof record.satellite_name})`);
        console.log(`  object_type: ${record.object_type} (${typeof record.object_type})`);
        console.log(`  subsat_long: ${record.subsat_long} (${typeof record.subsat_long})`);
        
        // 检查是否还有嵌套结构
        if (record.orbital_elements) {
          console.log(`  ⚠️  仍有嵌套结构 orbital_elements`);
          console.log(`    orbital_elements.object_type: ${record.orbital_elements.object_type}`);
          console.log(`    orbital_elements.subsat_long: ${record.orbital_elements.subsat_long}`);
        }
        
        console.log(`  所有字段: ${Object.keys(record).join(', ')}`);
      });
      
      // 统计数据质量
      console.log('\n数据质量统计:');
      
      const validGeoRecords = data.data.filter(record => {
        return record.object_type === 'GEO' &&
               record.subsat_long !== null &&
               record.subsat_long !== undefined &&
               record.subsat_long !== '' &&
               !isNaN(parseFloat(record.subsat_long));
      });
      
      console.log(`  总记录数: ${data.data.length}`);
      console.log(`  有效GEO记录数: ${validGeoRecords.length}`);
      console.log(`  数据有效率: ${((validGeoRecords.length / data.data.length) * 100).toFixed(2)}%`);
      
      if (validGeoRecords.length > 0) {
        console.log('\n✅ 数据格式正确，前端插件应该能正常处理');
        
        // 显示经度范围
        const longitudes = validGeoRecords.map(r => parseFloat(r.subsat_long));
        const minLong = Math.min(...longitudes);
        const maxLong = Math.max(...longitudes);
        console.log(`  经度范围: ${minLong.toFixed(2)}° 到 ${maxLong.toFixed(2)}°`);
        
        // 显示不同卫星数量
        const uniqueSatellites = new Set(validGeoRecords.map(r => r.norad_id));
        console.log(`  不同卫星数量: ${uniqueSatellites.size}`);
        
      } else {
        console.log('\n❌ 没有有效的GEO经度数据');
        
        // 分析问题
        console.log('\n问题分析:');
        const withObjectType = data.data.filter(r => r.object_type === 'GEO');
        console.log(`  object_type为GEO的记录: ${withObjectType.length}`);
        
        const withSubsatLong = data.data.filter(r => r.subsat_long !== null && r.subsat_long !== undefined);
        console.log(`  有subsat_long字段的记录: ${withSubsatLong.length}`);
        
        const withValidLong = data.data.filter(r => !isNaN(parseFloat(r.subsat_long)));
        console.log(`  subsat_long为有效数字的记录: ${withValidLong.length}`);
      }
      
    } else {
      console.log('❌ API返回空数据');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.log('\n请确保:');
    console.log('1. API服务器正在运行 (npm run start:api)');
    console.log('2. ES数据库连接正常');
    console.log('3. 字段映射配置正确');
  }
}

// 运行测试
testAPIDataFormat().catch(console.error);
