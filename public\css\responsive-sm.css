@media (min-width: 640px) {
  .side-menu-parent {
    width: 280px;
    min-width: 265px;
  }

  #lookanglesmultisite-menu {
    width: 450px;
  }

  #dops-menu {
    width: 400px;
  }

  #menu-satellite-collision,
  #FindSatPlugin-bottom-icon {
    display: inline;
  }

  .sat-info-value {
    width: 65%;
  }

  footer {
    margin-top: 0px !important;
    position: fixed !important;
    bottom: 0px !important;
    /* 🔥 移除固定高度，允许拖动调整 */
    width: 100%;
    z-index: 100 !important;
  }

  #bottom-icons-container {
    width: 100%;
    height: var(--bottom-menu-height);
  }

  #footer-toggle-wrapper {
    width: 100%;
    /* bottom: 120px; */
    z-index: 1;
    position: relative;
  }

  .footer-slide-down {
    bottom: calc(-120px / var(--system-scale-factor, 1)) !important;
    /* 🔥 移除固定高度，允许拖动调整 */
    top: auto !important;
  }

  .footer-slide-trans {
    transition: 1s;
  }

  .footer-slide-up {
    bottom: 0px !important;
    top: auto !important;
  }

  .ui-timepicker-div dl dd div {
    width: calc(150px / var(--system-scale-factor, 1)) !important;
  }

  #datetime-text {
    border-width: 0px 0px 0px calc(1px / var(--system-scale-factor, 1));
    border-style: solid;
    border-color: var(--color-dark-text-accent);
    padding: 0px calc(10px / var(--system-scale-factor, 1)) 0px calc(10px / var(--system-scale-factor, 1));
    vertical-align: bottom;
    font-size: calc(20px / var(--system-scale-factor, 1)) !important;
    line-height: calc(24px / var(--system-scale-factor, 1)) !important;
  }

  #jday {
    display: block;
    font-size: calc(20px / var(--system-scale-factor, 1)) !important;
    line-height: calc(24px / var(--system-scale-factor, 1)) !important;
    color: white !important;
    padding: calc(5px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  }

  #toast-container {
    min-width: 0px;
    right: 5%;
  }

  .sat-infobox-links {
    display: block;
    justify-content: flex-start;
    text-align: unset;
  }

  #sat-infobox {
    /* 🔥 移除强制位置设置，让Draggabilly控制位置 */
    width: calc(70% / var(--system-scale-factor, 1)) !important;
    max-width: calc(420px / var(--system-scale-factor, 1)) !important;
  }

  #polar-plot {
    width: 95%;
  }

  #sensor-timeline-sensor-list,
  #multi-site-look-angles-sensor-list {
    display: grid;
    justify-content: space-evenly;
    justify-items: stretch;
    grid-row-gap: 10px;
    grid-column-gap: 10px;
  }
}