import { ClickDragOptions } from '@app/plugins/KeepTrackPlugin';

// 使用全局拖动状态，避免循环依赖
declare global {
  interface Window {
    isDragging?: boolean;
  }
}

export const clickAndDragWidth = (el: HTMLElement | null, options: ClickDragOptions = {
  isDraggable: true,
}): HTMLDivElement | null => {
  if (!el) {
    return null;
  }

  const minWidth = options.minWidth ?? 280;
  const maxWidth = options.maxWidth ?? 450;

  let width = el.style.width ? parseInt(el.style.width) : el.clientWidth;

  width = width < minWidth ? minWidth : width;
  width = width > maxWidth ? maxWidth : width;
  el.style.width = `${width}px`;
  el.style.display = 'block';

  window.isDragging = false;

  if (options.isDraggable) {
    // create new element on right edge
    const edgeEl = createElWidth_(el);

    addEventsWidth_(edgeEl, el, width, minWidth, maxWidth, options);

    return edgeEl;
  }

  return null;
};

export const clickAndDragHeight = (el: HTMLElement, maxHeight?: number, callback?: () => void): void => {
  console.log('🔍 [底部菜单拖动调试] clickAndDragHeight被调用');
  console.log('🔍 [底部菜单拖动] 目标元素:', el);
  console.log('🔍 [底部菜单拖动] maxHeight:', maxHeight);

  if (!el) {
    console.error('❌ [底部菜单拖动] 目标元素为null');
    return;
  }

  // 检查元素的CSS属性
  const computedStyle = window.getComputedStyle(el);
  console.log('🔍 [底部菜单拖动] 目标元素CSS属性:', {
    position: computedStyle.position,
    pointerEvents: computedStyle.pointerEvents,
    zIndex: computedStyle.zIndex,
    display: computedStyle.display,
    visibility: computedStyle.visibility,
    width: computedStyle.width,
    height: computedStyle.height
  });

  window.isDragging = false;

  // create new element on right edge
  console.log('🔍 [底部菜单拖动] 创建拖动手柄...');
  const edgeEl = createElHeight_(el);
  console.log('✅ [底部菜单拖动] 拖动手柄创建完成:', edgeEl);

  addEventsHeight_(edgeEl, el, callback, maxHeight);
};

const addEventsWidth_ = (edgeEl: HTMLDivElement, el: HTMLElement, width: number, minWidth: number, maxWidth: number, options: ClickDragOptions) => {
  const { attachedElement, leftOffset } = options;
  let startX: number;
  let startWidth: number;

  edgeEl.addEventListener('mousedown', (e: MouseEvent) => {
    Object.assign(edgeEl.style, {
      width: '100vw',
      height: '100vh',
      position: 'fixed',
    } as CSSStyleDeclaration);
    edgeEl.style.right = '';

    startX = e.clientX;
    startWidth = el.clientWidth;
    window.isDragging = true;
  });
  edgeEl.addEventListener('mouseup', () => {
    window.isDragging = false;
    Object.assign(edgeEl.style, {
      height: '100%',
      width: '8px',
      right: '0px',
      position: 'absolute',
    } as CSSStyleDeclaration);

    if (options.callback) {
      options.callback();
    }
  });
  edgeEl.addEventListener('mousemove', (e: MouseEvent) => {
    if (window.isDragging) {
      requestAnimationFrame(() => {
        width = startWidth + e.clientX - startX;
        width = width < minWidth ? minWidth : width;
        width = width > maxWidth ? maxWidth : width;
        el.style.width = `${width}px`;

        if (attachedElement && !leftOffset) {
          attachedElement.style.left = `${el.getBoundingClientRect().right}px`;
        }
      });
    }
  });
};

const createElWidth_ = (el: HTMLElement) => {
  const edgeEl = document.createElement('div');

  edgeEl.style.position = 'relative';
  edgeEl.style.height = '100%';
  edgeEl.style.width = '8px';
  edgeEl.style.right = '0px';
  edgeEl.style.cursor = 'w-resize';
  edgeEl.style.zIndex = '9999';
  edgeEl.style.marginLeft = 'auto';
  edgeEl.style.cursor = 'w-resize';

  el.appendChild(edgeEl);

  return edgeEl;
};

const addEventsHeight_ = (edgeEl: HTMLDivElement, el: HTMLElement, callback?: () => void, maxHeight?: number) => {
  console.log('🔍 [拖动事件] 开始添加拖动事件监听器...');
  let startY: number;
  let startHeight: number;
  let height: number;

  // 🔥 将mousemove和mouseup事件绑定到document，确保全局捕获
  const handleMouseMove = (e: MouseEvent) => {
    if (window.isDragging) {
      console.log('🎯 [拖动事件] mousemove事件触发 (拖动中):', {
        clientY: e.clientY,
        startY,
        deltaY: e.clientY - startY
      });

      requestAnimationFrame(() => {
        height = startHeight - (e.clientY - startY);
        height = maxHeight ? Math.min(height, maxHeight) : height;
        height = height < 0 ? 0 : height;

        // 🔍 详细检查元素状态
        console.log('🔍 [拖动事件] 目标元素信息:', {
          elementId: el.id,
          elementClass: el.className,
          tagName: el.tagName
        });

        console.log('🔍 [拖动事件] 设置高度前:', {
          currentHeight: el.style.height,
          offsetHeight: el.offsetHeight,
          clientHeight: el.clientHeight,
          scrollHeight: el.scrollHeight
        });

        // 🔥 使用setProperty with important来强制覆盖所有CSS规则
        el.style.setProperty('height', `${height}px`, 'important');

        // 🔥 同时更新内部容器的高度，避免空隙
        if (el.id === 'nav-footer') {
          const bottomIconsContainer = el.querySelector('#bottom-icons-container') as HTMLElement;
          const bottomIcons = el.querySelector('#bottom-icons') as HTMLElement;

          if (bottomIconsContainer) {
            bottomIconsContainer.style.setProperty('height', `${height}px`, 'important');
          }
          if (bottomIcons) {
            bottomIcons.style.setProperty('max-height', `${height - 20}px`, 'important'); // 减去padding
          }
        }

        console.log('🔍 [拖动事件] 设置高度后:', {
          newStyleHeight: el.style.height,
          offsetHeight: el.offsetHeight,
          clientHeight: el.clientHeight,
          scrollHeight: el.scrollHeight
        });

        console.log('🎯 [拖动事件] 高度更新:', {
          startHeight,
          newHeight: height,
          maxHeight
        });
      });
    }
  };

  const handleMouseUp = () => {
    if (window.isDragging) {
      console.log('🎯 [拖动事件] mouseup事件触发，结束拖动');
      window.isDragging = false;

      // 恢复拖动手柄原始样式
      Object.assign(edgeEl.style, {
        width: '100%',
        height: '4px',
        position: 'absolute',
      } as CSSStyleDeclaration);

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (callback) {
        console.log('🎯 [拖动事件] 执行回调函数');
        callback();
      }
    }
  };

  edgeEl.addEventListener('mousedown', (e: MouseEvent) => {
    console.log('🎯 [拖动事件] mousedown事件触发:', {
      target: e.target,
      currentTarget: e.currentTarget,
      clientX: e.clientX,
      clientY: e.clientY,
      button: e.button
    });

    // 只在拖动区域内才处理事件
    if (e.target === edgeEl && e.currentTarget === edgeEl) {
      console.log('✅ [拖动事件] 拖动区域点击确认，开始拖动');

      // 阻止事件传播，但只针对拖动区域
      e.stopPropagation();
      e.preventDefault();

      // 🔥 不再修改拖动手柄的尺寸，保持原样
      startY = e.clientY;
      startHeight = el.clientHeight;
      window.isDragging = true;

      console.log('🎯 [拖动事件] 拖动状态设置:', {
        startY,
        startHeight,
        isDragging: window.isDragging
      });

      // 🔥 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      console.log('❌ [拖动事件] 点击目标不匹配，忽略事件');
    }
  });
  // 🔥 移除旧的事件监听器，现在使用全局事件监听器

  // 添加额外的事件监听器用于调试
  edgeEl.addEventListener('mouseenter', () => {
    console.log('🎯 [拖动事件] 鼠标进入拖动区域');
    // 🔍 检查拖动手柄的状态
    const rect = edgeEl.getBoundingClientRect();
    const style = window.getComputedStyle(edgeEl);
    console.log('🔍 [拖动手柄状态] 位置:', {
      x: rect.x, y: rect.y, width: rect.width, height: rect.height
    });
    console.log('🔍 [拖动手柄状态] 样式:', {
      position: style.position,
      zIndex: style.zIndex,
      pointerEvents: style.pointerEvents,
      visibility: style.visibility,
      opacity: style.opacity
    });
  });

  edgeEl.addEventListener('mouseleave', () => {
    console.log('🎯 [拖动事件] 鼠标离开拖动区域');
  });

  // 🔍 添加点击测试
  edgeEl.addEventListener('click', (e) => {
    console.log('🎯 [拖动事件] 点击事件触发:', e);
    console.log('🔍 [拖动事件] 点击位置:', e.clientX, e.clientY);
  });

  console.log('✅ [拖动事件] 所有事件监听器添加完成');
};

const createElHeight_ = (el: HTMLElement) => {
  console.log('🔍 [拖动手柄创建] 开始创建拖动手柄...');
  const edgeEl = document.createElement('div');

  edgeEl.style.position = 'absolute';
  edgeEl.style.width = '100%';
  edgeEl.style.height = '4px'; /* 增加高度便于拖动 */
  edgeEl.style.top = '0px';
  edgeEl.style.cursor = 'n-resize';
  edgeEl.style.zIndex = '1001'; /* 提高z-index确保可拖动 */
  edgeEl.style.marginBottom = 'auto';
  edgeEl.style.marginLeft = 'auto';
  edgeEl.style.marginRight = 'auto';
  edgeEl.style.background = 'rgba(255, 255, 255, 0.2)'; /* 增加背景便于识别 */
  edgeEl.style.borderTop = '1px solid rgba(255, 255, 255, 0.5)'; /* 增强边框线 */
  edgeEl.style.pointerEvents = 'auto'; /* 确保拖动区域可以接收鼠标事件 */

  // 添加类名以便识别
  edgeEl.className = 'drag-resize-handle';

  console.log('🔍 [拖动手柄创建] 手柄样式设置完成:', {
    position: edgeEl.style.position,
    width: edgeEl.style.width,
    height: edgeEl.style.height,
    cursor: edgeEl.style.cursor,
    zIndex: edgeEl.style.zIndex,
    background: edgeEl.style.background,
    pointerEvents: edgeEl.style.pointerEvents,
    className: edgeEl.className
  });

  el.appendChild(edgeEl);
  console.log('✅ [拖动手柄创建] 手柄已添加到父元素');

  // 验证手柄是否正确添加
  const addedHandle = el.querySelector('.drag-resize-handle');
  console.log('🔍 [拖动手柄创建] 验证手柄是否存在:', addedHandle);

  // 🔍 检查父元素的子元素，看是否有遮挡
  console.log('🔍 [拖动手柄创建] 父元素的所有子元素:');
  Array.from(el.children).forEach((child, index) => {
    const childRect = child.getBoundingClientRect();
    const childStyle = window.getComputedStyle(child);
    console.log(`  子元素${index}:`, {
      tagName: child.tagName,
      className: child.className,
      id: child.id,
      zIndex: childStyle.zIndex,
      position: childStyle.position,
      rect: { x: childRect.x, y: childRect.y, width: childRect.width, height: childRect.height }
    });
  });

  if (addedHandle) {
    const computedStyle = window.getComputedStyle(addedHandle);
    console.log('🔍 [拖动手柄创建] 手柄计算后的CSS属性:', {
      position: computedStyle.position,
      width: computedStyle.width,
      height: computedStyle.height,
      cursor: computedStyle.cursor,
      zIndex: computedStyle.zIndex,
      background: computedStyle.background,
      pointerEvents: computedStyle.pointerEvents,
      top: computedStyle.top,
      left: computedStyle.left
    });
  }

  return edgeEl;
};

// 全局事件监听器已完全移除，避免初始化问题
