<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 太空物体模拟平台</title>

    <!-- 🔧 登录界面缩放检测和应用 -->
    <script>
        // 登录界面缩放检测
        function initLoginScaling() {
            const devicePixelRatio = window.devicePixelRatio || 1;
            const screenWidth = screen.width;
            const windowWidth = window.innerWidth;

            let scale = 1;
            if (devicePixelRatio > 1.1) {
                scale = devicePixelRatio;
            } else if (screenWidth > windowWidth * 1.2) {
                scale = screenWidth / windowWidth;
            }

            scale = Math.max(1, Math.min(3, scale));

            // 设置CSS变量
            document.documentElement.style.setProperty('--system-scale-factor', scale.toString());

            console.log('🔍 登录界面缩放因子:', scale);

            // 应用合理的缩放样式
            const style = document.createElement('style');
            style.textContent = `
                :root { --system-scale-factor: ${scale}; }

                body, * {
                    font-size: calc(16px / var(--system-scale-factor)) !important;
                    line-height: calc(20px / var(--system-scale-factor)) !important;
                }

                h1, h2 {
                    font-size: calc(24px / var(--system-scale-factor)) !important;
                    line-height: calc(30px / var(--system-scale-factor)) !important;
                }

                .btn, button {
                    font-size: calc(16px / var(--system-scale-factor)) !important;
                    padding: calc(10px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
                    min-height: calc(40px / var(--system-scale-factor)) !important;
                }

                input[type="text"],
                input[type="password"],
                input[type="email"] {
                    font-size: calc(14px / var(--system-scale-factor)) !important;
                    padding: calc(10px / var(--system-scale-factor)) !important;
                    min-height: calc(36px / var(--system-scale-factor)) !important;
                }

                .toast {
                    font-size: calc(14px / var(--system-scale-factor)) !important;
                    padding: calc(10px / var(--system-scale-factor)) calc(14px / var(--system-scale-factor)) !important;
                }
            `;
            document.head.appendChild(style);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initLoginScaling);
        window.addEventListener('resize', initLoginScaling);
    </script>

    <style>
        :root {
            --system-scale-factor: 1;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: Arial, sans-serif;
            /* 背景图片配置 - 无滤镜，完全透明 */
            background: url('img/wallpaper/secure.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: calc(40px / var(--system-scale-factor));
            border-radius: calc(15px / var(--system-scale-factor));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: calc(500px / var(--system-scale-factor)); /* 增加宽度 */
            width: 100%;
            opacity: 0.9;
        }
        .tabs {
            display: flex;
            margin-bottom: calc(30px / var(--system-scale-factor));
            border-bottom: calc(2px / var(--system-scale-factor)) solid rgba(255,255,255,0.2);
        }
        .tab {
            flex: 1;
            padding: calc(15px / var(--system-scale-factor));
            text-align: center;
            cursor: pointer;
            border-bottom: calc(2px / var(--system-scale-factor)) solid transparent;
            transition: all 0.3s;
            font-size: calc(16px / var(--system-scale-factor));
        }
        .tab.active {
            border-bottom-color: white;
            background: rgba(255,255,255,0.1);
        }
        .form-container {
            display: none;
        }
        .form-container.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: background 0.3s;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }
        .toast {
            background: rgba(0, 191, 255, 0.8);
            color: white;
            padding: 16px 20px;
            border-radius: 0;
            margin-bottom: 10px;
            font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', '思源黑体', sans-serif;
            font-weight: normal;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: none;
            min-width: 300px;
            max-width: 400px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease-in-out;
            pointer-events: auto;
        }
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        .toast.success {
            background: rgba(76, 175, 80, 0.1) !important; /* 🔥 与系统保持一致的半透明背景 */
            color: white;
            border: none; /* 🔥 移除边框，与系统保持一致 */
        }
        .toast.error {
            background: rgba(244, 67, 54, 0.1) !important; /* 🔥 与系统保持一致的半透明背景 */
            color: white;
            border: none; /* 🔥 移除边框，与系统保持一致 */
        }
        .toast.info {
            background: rgba(33, 150, 243, 0.1) !important; /* 🔥 与系统保持一致的半透明背景 */
            color: white;
            border: none; /* 🔥 移除边框，与系统保持一致 */
        }
        .toast-close {
            float: right;
            margin-left: 15px;
            cursor: pointer;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.7);
        }
        .toast-close:hover {
            color: white;
        }
        .footer-info {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', '思源黑体', sans-serif;
            font-size: 14px;
            font-weight: normal;
            color: white;
            background: none;
        }
        .footer-info span {
            margin: 0 20px;
            display: inline-block;
        }
        .footer-info a {
            color: white;
            text-decoration: none;
        }
        .footer-info a:hover {
            text-decoration: underline;
        }
        .password-rules {
            margin-top: 5px;
        }
        .password-rules small {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            line-height: 1.4;
        }
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        .strength-weak { color: #f44336; }
        .strength-medium { color: #ff9800; }
        .strength-strong { color: #4caf50; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px;">太空物体模拟平台</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('login')">登录</div>
            <div class="tab" onclick="switchTab('register')">注册</div>
        </div>
        
        <!-- Toast容器将通过JavaScript动态创建 -->
        
        <!-- 登录表单 -->
        <div id="loginForm" class="form-container active">
            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" id="loginUsername" required autocomplete="username">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="loginPassword" required autocomplete="current-password">
                </div>
                <button type="submit" class="btn">登录</button>
            </form>
        </div>
        
        <!-- 注册表单 -->
        <div id="registerForm" class="form-container">
            <form onsubmit="handleRegister(event)">
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" id="registerUsername" required autocomplete="username">
                </div>
                <div class="form-group">
                    <label>邮箱:</label>
                    <input type="email" id="registerEmail" required autocomplete="email">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="registerPassword" required autocomplete="new-password" oninput="validatePassword()">
                    <div class="password-rules">
                        <small>密码要求：至少8位，包含大小写字母、数字和特殊字符</small>
                        <div id="passwordStrength" class="password-strength"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label>确认密码:</label>
                    <input type="password" id="confirmPassword" required autocomplete="new-password">
                </div>
                <button type="submit" class="btn">注册</button>
            </form>
        </div>
        
    </div>

    <!-- 底部公司信息 -->
    <div class="footer-info">
        <span>北京星地探索科技有限公司</span>
        <span><EMAIL></span>
        <span><a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2025133697号-9</a></span>
    </div>

    <script>
        let API_BASE = '';

        async function getApiUrl() {
            // 使用统一配置加载器
            if (window.configLoader) {
                try {
                    API_BASE = await window.configLoader.getAuthUrl();
                    console.log('从配置加载器获取API地址:', API_BASE);
                    return;
                } catch (error) {
                    console.log('配置加载器获取失败:', error.message);
                }
            }

            // 回退到默认地址（使用5001端口）
            API_BASE = `http://${window.location.hostname}:5001/api/auth`;
            console.log('使用默认API地址:', API_BASE);
        }

        function showMessage(text, type = 'error') {
            // 创建或获取toast容器
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container';
                document.body.appendChild(container);
            }

            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            // 添加关闭按钮
            const closeBtn = document.createElement('span');
            closeBtn.className = 'toast-close';
            closeBtn.innerHTML = '×';
            closeBtn.onclick = () => removeToast(toast);

            // 设置内容
            const textNode = document.createTextNode(text);
            toast.appendChild(textNode);
            toast.appendChild(closeBtn);

            // 添加到容器
            container.appendChild(toast);

            // 触发动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // 自动移除
            setTimeout(() => {
                removeToast(toast);
            }, 5000);
        }

        function removeToast(toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }

        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.form-container').forEach(form => form.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(`${tabName}Form`).classList.add('active');
        }

        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!username || !password) {
                showMessage('请输入用户名和密码');
                return;
            }
            
            showMessage('登录中...', 'success');
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    localStorage.setItem('apiBaseUrl', API_BASE);

                    showMessage('登录成功！正在跳转...', 'success');
                    setTimeout(() => {
                        // 根据用户角色跳转
                        if (data.user && data.user.role === 'admin') {
                            window.location.href = '/admin-simple.html';
                        } else {
                            // 普通用户跳转到主系统
                            window.location.href = '/';
                        }
                    }, 1000);
                } else {
                    const error = await response.json();
                    showMessage('登录失败: ' + (error.error || '未知错误'));
                }
            } catch (err) {
                showMessage('连接失败: ' + err.message);
            }
        }

        async function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!username || !email || !password || !confirmPassword) {
                showMessage('请填写所有字段');
                return;
            }
            
            if (password !== confirmPassword) {
                showMessage('密码和确认密码不匹配');
                return;
            }
            
            if (password.length < 8) {
                showMessage('密码至少需要8位字符');
                return;
            }
            
            showMessage('注册中...', 'success');
            
            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, email, password })
                });
                
                if (response.ok) {
                    showMessage('注册申请已提交，请等待管理员审批', 'success');
                    document.getElementById('registerForm').querySelector('form').reset();
                } else {
                    const error = await response.json();
                    showMessage('注册失败: ' + (error.error || '未知错误'));
                }
            } catch (err) {
                showMessage('连接失败: ' + err.message);
            }
        }

        // 密码强度验证
        function validatePassword() {
            const password = document.getElementById('registerPassword').value;
            const strengthDiv = document.getElementById('passwordStrength');

            if (!password) {
                strengthDiv.innerHTML = '';
                return;
            }

            const checks = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            const passedChecks = Object.values(checks).filter(Boolean).length;
            let strength = '';
            let className = '';

            if (passedChecks < 3) {
                strength = '密码强度：弱';
                className = 'strength-weak';
            } else if (passedChecks < 5) {
                strength = '密码强度：中等';
                className = 'strength-medium';
            } else {
                strength = '密码强度：强';
                className = 'strength-strong';
            }

            const details = [];
            if (!checks.length) details.push('至少8位');
            if (!checks.uppercase) details.push('大写字母');
            if (!checks.lowercase) details.push('小写字母');
            if (!checks.number) details.push('数字');
            if (!checks.special) details.push('特殊字符');

            if (details.length > 0) {
                strength += ` (缺少: ${details.join('、')})`;
            }

            strengthDiv.innerHTML = `<span class="${className}">${strength}</span>`;
        }

        document.addEventListener('DOMContentLoaded', async () => {
            await getApiUrl();
            const token = localStorage.getItem('authToken');
            if (token) {
                window.location.href = '/';
            }
        });
    </script>

    <!-- 引入统一配置加载器 -->
    <script src="js/config-loader.js"></script>
    <script src="js/login.js"></script>
</body>
</html>
