/**
 * 测试GEO经度历史界面修复
 */

const fs = require('fs');

function testGeoUIFixes() {
  console.log('测试GEO经度历史界面修复...\n');
  
  let allTestsPassed = true;
  
  // 测试1: 检查插件文件是否存在
  console.log('测试1: 检查插件文件');
  const pluginFile = 'src/plugins/geo-longitude-history/geo-longitude-history.ts';
  if (fs.existsSync(pluginFile)) {
    console.log('✓ 插件文件存在');
  } else {
    console.log('✗ 插件文件不存在');
    allTestsPassed = false;
  }
  
  // 测试2: 检查标题布局修复
  console.log('\n测试2: 检查标题布局修复');
  if (fs.existsSync(pluginFile)) {
    const content = fs.readFileSync(pluginFile, 'utf8');
    
    // 检查是否使用了与历史轨道数据相同的布局
    if (content.includes('flex-direction: row') && content.includes('align-items: center')) {
      console.log('✓ 标题布局已修复为横向排列');
    } else {
      console.log('✗ 标题布局未修复');
      allTestsPassed = false;
    }
    
    // 检查是否有统计信息显示
    if (content.includes('geo-longitude-stats-title')) {
      console.log('✓ 已添加统计信息显示');
    } else {
      console.log('✗ 未添加统计信息显示');
      allTestsPassed = false;
    }
  }
  
  // 测试3: 检查时间轴修复
  console.log('\n测试3: 检查时间轴修复');
  if (fs.existsSync(pluginFile)) {
    const content = fs.readFileSync(pluginFile, 'utf8');
    
    // 检查时间轴方向（底部最新，顶部历史）
    if (content.includes('height - bottomPadding - timeRatio * chartHeight')) {
      console.log('✓ 时间轴方向已修复（底部最新，顶部历史）');
    } else {
      console.log('✗ 时间轴方向未修复');
      allTestsPassed = false;
    }
    
    // 检查是否只显示日期
    if (content.includes('toLocaleDateString') && content.includes('year: \'numeric\'')) {
      console.log('✓ 时间标签已修复为只显示日期');
    } else {
      console.log('✗ 时间标签未修复');
      allTestsPassed = false;
    }
  }
  
  // 测试4: 检查经度范围自适应
  console.log('\n测试4: 检查经度范围自适应');
  if (fs.existsSync(pluginFile)) {
    const content = fs.readFileSync(pluginFile, 'utf8');
    
    // 检查是否有经度范围计算方法
    if (content.includes('calculateLongitudeRange_')) {
      console.log('✓ 已添加经度范围自适应计算');
    } else {
      console.log('✗ 未添加经度范围自适应计算');
      allTestsPassed = false;
    }
    
    // 检查是否使用了自适应的经度范围
    if (content.includes('longitudeRange.min') && content.includes('longitudeRange.max')) {
      console.log('✓ 图表已使用自适应经度范围');
    } else {
      console.log('✗ 图表未使用自适应经度范围');
      allTestsPassed = false;
    }
  }
  
  // 测试5: 检查鼠标拖动功能
  console.log('\n测试5: 检查鼠标拖动功能');
  if (fs.existsSync(pluginFile)) {
    const content = fs.readFileSync(pluginFile, 'utf8');
    
    // 检查拖动相关属性
    if (content.includes('isDragging') && content.includes('panOffset')) {
      console.log('✓ 已添加拖动状态管理');
    } else {
      console.log('✗ 未添加拖动状态管理');
      allTestsPassed = false;
    }
    
    // 检查鼠标事件处理
    if (content.includes('handleCanvasMouseDown_') && content.includes('handleCanvasMouseUp_')) {
      console.log('✓ 已添加鼠标拖动事件处理');
    } else {
      console.log('✗ 未添加鼠标拖动事件处理');
      allTestsPassed = false;
    }
    
    // 检查重绘功能
    if (content.includes('redrawChart_')) {
      console.log('✓ 已添加图表重绘功能');
    } else {
      console.log('✗ 未添加图表重绘功能');
      allTestsPassed = false;
    }
    
    // 检查鼠标样式
    if (content.includes('canvas.style.cursor = \'grab\'') || content.includes('canvas.style.cursor = \'grabbing\'')) {
      console.log('✓ 已设置拖动鼠标样式');
    } else {
      console.log('✗ 未设置拖动鼠标样式');
      allTestsPassed = false;
    }
  }
  
  // 测试6: 检查API数据格式修复
  console.log('\n测试6: 检查API数据格式修复');
  const apiFile = 'src/api/es-history.routes.ts';
  if (fs.existsSync(apiFile)) {
    const content = fs.readFileSync(apiFile, 'utf8');
    
    // 检查是否有数据转换逻辑
    if (content.includes('transformedRecords') && content.includes('orbital_elements')) {
      console.log('✓ API已添加数据扁平化转换');
    } else {
      console.log('✗ API未添加数据扁平化转换');
      allTestsPassed = false;
    }
    
    // 检查字段映射
    if (content.includes('orbital_elements.object_type.keyword')) {
      console.log('✓ API已使用正确的字段映射');
    } else {
      console.log('✗ API未使用正确的字段映射');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ API文件不存在');
    allTestsPassed = false;
  }
  
  // 测试7: 检查字段映射配置
  console.log('\n测试7: 检查字段映射配置');
  const configFile = 'es-field-mapping.json';
  if (fs.existsSync(configFile)) {
    const content = fs.readFileSync(configFile, 'utf8');
    const config = JSON.parse(content);
    
    if (config.elasticsearch.index === 'orbital_tle') {
      console.log('✓ 配置文件使用正确的索引名');
    } else {
      console.log('✗ 配置文件索引名不正确');
      allTestsPassed = false;
    }
    
    if (config.elasticsearch.fields.object_type === 'orbital_elements.object_type.keyword') {
      console.log('✓ 配置文件使用正确的字段路径');
    } else {
      console.log('✗ 配置文件字段路径不正确');
      allTestsPassed = false;
    }
  } else {
    console.log('✗ 字段映射配置文件不存在');
    allTestsPassed = false;
  }
  
  // 总结
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('✅ 所有测试通过！GEO经度历史界面修复完成');
    console.log('\n修复内容总结:');
    console.log('1. ✓ 标题布局修复 - 与历史轨道数据页面一致');
    console.log('2. ✓ 时间轴方向修复 - 底部最新，顶部历史');
    console.log('3. ✓ 时间标签修复 - 只显示日期');
    console.log('4. ✓ 经度范围自适应 - 根据数据自动调整');
    console.log('5. ✓ 鼠标拖动功能 - 支持图表平移');
    console.log('6. ✓ API数据格式修复 - 扁平化数据结构');
    console.log('7. ✓ 字段映射配置 - 正确的ES字段路径');
    console.log('8. ✓ 统计信息显示 - 显示查询结果摘要');
    
    console.log('\n现在可以测试完整功能:');
    console.log('1. 启动服务: npm run start:api && npm run dev');
    console.log('2. 打开GEO经度历史功能');
    console.log('3. 设置时间范围（建议6个月到1年）');
    console.log('4. 点击"获取数据"');
    console.log('5. 测试鼠标拖动功能');
  } else {
    console.log('❌ 部分测试失败，请检查上述问题');
  }
  
  return allTestsPassed;
}

// 运行测试
if (require.main === module) {
  testGeoUIFixes();
}

module.exports = { testGeoUIFixes };
