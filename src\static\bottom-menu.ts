import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl, showEl } from '@app/lib/get-el';
import { SoundNames } from '@app/plugins/sounds/SoundNames';
import { errorManagerInstance } from '@app/singletons/errorManager';
import barChart4BarsPng from '@public/img/icons/bar-chart-4-bars.png';
import developerModePng from '@public/img/icons/developer-mode.png';
import localCafePng from '@public/img/icons/local-cafe.png';
import sciencePng from '@public/img/icons/science.png';
import settingsPng from '@public/img/icons/settings.png';

export class BottomMenu {
  static readonly basicMenuId = 'menu-filter-basic';
  static readonly advancedMenuId = 'menu-filter-advanced';
  static readonly analysisMenuId = 'menu-filter-analysis';
  static readonly experimentalMenuId = 'menu-filter-experimental';
  static readonly settingsMenuId = 'menu-filter-settings';
  static readonly allMenuId = 'menu-filter-all';

  // 🔥🔥🔥 全局调试函数 - 可以在浏览器控制台调用 window.debugBottomIcons()
  static debugBottomIcons() {
    const bottomIcons = getEl('bottom-icons');
    if (bottomIcons) {
      console.log('🔥🔥🔥 底部菜单调试信息:');
      console.log('元素存在:', !!bottomIcons);
      console.log('子元素数量:', bottomIcons.children.length);
      console.log('计算样式:', {
        display: window.getComputedStyle(bottomIcons).display,
        gridTemplateColumns: window.getComputedStyle(bottomIcons).gridTemplateColumns,
        justifyContent: window.getComputedStyle(bottomIcons).justifyContent,
        width: window.getComputedStyle(bottomIcons).width,
        height: window.getComputedStyle(bottomIcons).height,
        float: window.getComputedStyle(bottomIcons).float
      });
      console.log('内联样式:', {
        display: bottomIcons.style.display,
        gridTemplateColumns: bottomIcons.style.gridTemplateColumns,
        justifyContent: bottomIcons.style.justifyContent,
        width: bottomIcons.style.width,
        height: bottomIcons.style.height,
        float: bottomIcons.style.float
      });
      console.log('CSS变量值:', {
        bottomIconWidth: getComputedStyle(document.documentElement).getPropertyValue('--bottom-icon-width'),
        bottomFilterWidth: getComputedStyle(document.documentElement).getPropertyValue('--bottom-filter-width')
      });
      console.log('容器尺寸信息:', {
        containerWidth: bottomIcons.offsetWidth,
        containerHeight: bottomIcons.offsetHeight,
        parentWidth: bottomIcons.parentElement?.offsetWidth,
        childrenCount: bottomIcons.children.length,
        firstChildWidth: (bottomIcons.children[0] as HTMLElement)?.offsetWidth,
        firstChildHeight: (bottomIcons.children[0] as HTMLElement)?.offsetHeight
      });
      return bottomIcons;
    } else {
      console.error('❌ bottom-icons元素不存在');
      return null;
    }
  }

  // 🔥🔥🔥 调试拖动区域函数
  static debugDragArea() {
    const bottomContainer = getEl('bottom-icons-container');
    if (bottomContainer) {
      console.log('🔥🔥🔥 拖动区域调试信息:');
      console.log('bottom-icons-container存在:', !!bottomContainer);

      const dragElements = bottomContainer.querySelectorAll('div[style*="cursor: n-resize"]');
      console.log('拖动区域元素数量:', dragElements.length);

      dragElements.forEach((dragEl, index) => {
        const dragElement = dragEl as HTMLElement;
        console.log(`拖动区域 ${index}:`, {
          cursor: dragElement.style.cursor,
          position: dragElement.style.position,
          width: dragElement.style.width,
          height: dragElement.style.height,
          top: dragElement.style.top,
          zIndex: dragElement.style.zIndex,
          background: dragElement.style.background,
          pointerEvents: dragElement.style.pointerEvents,
          visibility: dragElement.style.visibility,
          opacity: dragElement.style.opacity
        });
      });

      return { bottomContainer, dragElements };
    } else {
      console.error('❌ bottom-icons-container元素不存在');
      return null;
    }
  }

  static init() {
    // 强制启用底部菜单，忽略设置
    console.log('🔥 强制初始化底部菜单，忽略isDisableBottomMenu设置');
    keepTrackApi.on(KeepTrackApiEvents.uiManagerInit, BottomMenu.createBottomMenu);
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, BottomMenu.addBottomMenuFilterButtons);
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, BottomMenu.updateBottomMenuVisibility_);
  }
  static createBottomMenu(): void {
    // 修复DOM结构：直接在已存在的nav-footer中添加内容，而不是创建新的nav-footer
    const navFooter = getEl('nav-footer');
    if (!navFooter) {
      console.error('❌ nav-footer元素不存在！');
      return;
    }


    // 添加底部菜单内容到现有的nav-footer中
    const bottomMenuContent = keepTrackApi.html`
      <div id="bottom-icons-container">
        <div id="bottom-icons-filter">
          <div id="${BottomMenu.allMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="All Plugins" src="" delayedsrc="${developerModePng}" />
            </div>
            <span class="bmenu-filter-title">所有菜单</span>
          </div>
          <div id="${BottomMenu.basicMenuId}" class="bmenu-filter-item bmenu-item-selected">
            <div class="bmenu-filter-item-inner">
              <img alt="Basic Menu" src="" delayedsrc="${localCafePng}" />
            </div>
            <span class="bmenu-filter-title">基础菜单</span>
          </div>
          <div id="${BottomMenu.advancedMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Advanced Menu" src="" delayedsrc="${developerModePng}" />
            </div>
            <span class="bmenu-filter-title">高级菜单</span>
          </div>
          <div id="${BottomMenu.analysisMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Analysis Menu" src="" delayedsrc="${barChart4BarsPng}" />
            </div>
            <span class="bmenu-filter-title">分析菜单</span>
          </div>
          <div id="${BottomMenu.settingsMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Settings Menu" src="" delayedsrc="${settingsPng}" />
            </div>
            <span class="bmenu-filter-title">设置菜单</span>
          </div>
          <div id="${BottomMenu.experimentalMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Experimental Menu" src="" delayedsrc="${sciencePng}" />
            </div>
            <span class="bmenu-filter-title">实验菜单</span>
          </div>

        </div>
        <div id="bottom-icons"></div>
      </div>
    `;

    // 将内容插入到现有的nav-footer中
    navFooter.insertAdjacentHTML('beforeend', bottomMenuContent);

    console.log('✅ 底部菜单DOM结构创建完成');

    // 🔥 恢复原始滑动系统，但默认设置为显示状态
    navFooter.classList.remove('footer-slide-down');
    navFooter.classList.add('footer-slide-up');
    navFooter.style.setProperty('visibility', 'visible', 'important');
    navFooter.style.setProperty('display', 'block', 'important');

    // 立即设置透明背景和grid布局
    setTimeout(() => {
      BottomMenu.forceTransparentBackgrounds();
      BottomMenu.forceGridLayout();
    }, 0);

    // 🔥🔥🔥 将调试函数暴露到全局
    (window as any).debugBottomIcons = BottomMenu.debugBottomIcons;
    (window as any).debugDragArea = BottomMenu.debugDragArea;
    (window as any).debugBottomMenuDrag = BottomMenu.debugBottomMenuDrag;
  }

  private static forceTransparentBackgrounds() {
    try {
      const elementIds = [
        'bottom-icons',
        'bottom-icons-filter',
        'bottom-icons-container',
        'nav-footer'
      ];

      elementIds.forEach(id => {
        const element = getEl(id);
        if (element) {
          // 恢复透明模糊效果
          element.style.setProperty('background', 'transparent', 'important');
          element.style.setProperty('background-color', 'transparent', 'important');
          element.style.setProperty('background-image', 'none', 'important');
          element.style.setProperty('backdrop-filter', 'blur(10px)', 'important');
          element.style.setProperty('-webkit-backdrop-filter', 'blur(10px)', 'important');
          element.style.setProperty('border', 'none', 'important');
          element.style.setProperty('box-shadow', 'none', 'important');
          element.style.setProperty('pointer-events', 'auto', 'important'); // 确保可以点击
          element.style.setProperty('visibility', 'visible', 'important'); // 确保可见
          element.style.setProperty('opacity', '1', 'important'); // 确保不透明

          // 🔥 特殊处理：保护拖动区域不被透明设置影响
          if (id === 'bottom-icons-container') {
            // 查找拖动区域元素（cursor: n-resize）
            const dragElements = element.querySelectorAll('div[style*="cursor: n-resize"], .drag-handle, .resize-handle');
            dragElements.forEach(dragEl => {
              const dragElement = dragEl as HTMLElement;
              dragElement.style.setProperty('background', 'rgba(255, 255, 255, 0.2)', 'important');
              dragElement.style.setProperty('background-color', 'rgba(255, 255, 255, 0.2)', 'important');
              dragElement.style.setProperty('pointer-events', 'auto', 'important');
              dragElement.style.setProperty('z-index', '9999', 'important');
              dragElement.style.setProperty('opacity', '1', 'important');
              dragElement.style.setProperty('visibility', 'visible', 'important');
              dragElement.style.setProperty('position', 'absolute', 'important');
              dragElement.style.setProperty('top', '0', 'important');
              dragElement.style.setProperty('left', '0', 'important');
              dragElement.style.setProperty('right', '0', 'important');
              dragElement.style.setProperty('height', '8px', 'important');
              dragElement.style.setProperty('cursor', 'n-resize', 'important');
            });
          }

          // 🔥 特殊处理：强制设置bottom-icons为grid布局 - 调整列宽
          if (id === 'bottom-icons') {
            element.style.setProperty('display', 'grid', 'important');
            element.style.setProperty('grid-template-columns', 'repeat(auto-fill, 80px)', 'important');
            element.style.setProperty('justify-content', 'center', 'important');
            element.style.setProperty('column-gap', '2px', 'important');
            console.log('🔥 强制设置bottom-icons为grid布局');
          } else {
            element.style.setProperty('display', 'block', 'important'); // 强制显示
          }

          // 特别处理nav-footer，确保显示状态
          if (id === 'nav-footer') {
            element.classList.remove('footer-slide-down');
            element.classList.add('footer-slide-up');
          }

          console.log(`🔥 底部菜单元素 ${id} 状态:`, {
            display: element.style.display,
            visibility: element.style.visibility,
            opacity: element.style.opacity,
            bottom: window.getComputedStyle(element).bottom
          });
        } else {
          console.error(`❌ 找不到底部菜单元素: ${id}`);
        }
      });

      // 设置菜单图标背景透明，移除悬停效果和边框
      const menuIconSelectors = [
        '.bmenu-item',
        '.bmenu-filter-item',
        '.bmenu-item-inner',
        '.bmenu-filter-item-inner'
      ];

      menuIconSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            const htmlElement = element as HTMLElement;
            htmlElement.style.setProperty('background', 'transparent', 'important');
            htmlElement.style.setProperty('background-color', 'transparent', 'important');
            htmlElement.style.setProperty('background-image', 'none', 'important');
            htmlElement.style.setProperty('border', 'none', 'important');
            htmlElement.style.setProperty('outline', 'none', 'important');
            htmlElement.style.setProperty('box-shadow', 'none', 'important');
            htmlElement.style.setProperty('transition', 'none', 'important');

            // 移除悬停效果 - 不添加任何事件监听器
          });
        } catch (e) {
          console.warn(`设置 ${selector} 背景时出错:`, e);
        }
      });

      console.log('🔥 强制设置菜单图标背景透明，移除悬停效果');
    } catch (error) {
      console.warn('强制透明背景设置出错:', error);
    }
  }

  private static forceGridLayout() {
    try {
      const bottomIcons = getEl('bottom-icons');
      if (bottomIcons) {
        const beforeStyle = window.getComputedStyle(bottomIcons);
        console.log('🔥🔥🔥 forceGridLayout开始 - 当前计算样式:');
        console.log('  display:', beforeStyle.display);
        console.log('  gridTemplateColumns:', beforeStyle.gridTemplateColumns);
        console.log('  justifyContent:', beforeStyle.justifyContent);
        console.log('  width:', beforeStyle.width);
        console.log('  float:', beforeStyle.float);

        console.log('🔥🔥🔥 forceGridLayout开始 - 当前内联样式:', {
          display: bottomIcons.style.display,
          gridTemplateColumns: bottomIcons.style.gridTemplateColumns,
          justifyContent: bottomIcons.style.justifyContent,
          width: bottomIcons.style.width,
          float: bottomIcons.style.float
        });

        // 🔥 强制设置grid布局 - 调整列宽和间距
        bottomIcons.style.setProperty('display', 'grid', 'important');
        bottomIcons.style.setProperty('grid-template-columns', 'repeat(auto-fill, 80px)', 'important');
        bottomIcons.style.setProperty('justify-content', 'center', 'important');
        bottomIcons.style.setProperty('width', 'calc(100% - 185px)', 'important');
        bottomIcons.style.setProperty('float', 'right', 'important');
        bottomIcons.style.setProperty('row-gap', '2px', 'important');
        bottomIcons.style.setProperty('column-gap', '2px', 'important');
        bottomIcons.style.setProperty('padding', '5px', 'important');

        const afterStyle = window.getComputedStyle(bottomIcons);
        console.log('🔥🔥🔥 forceGridLayout完成 - 设置后计算样式:');
        console.log('  display:', afterStyle.display);
        console.log('  gridTemplateColumns:', afterStyle.gridTemplateColumns);
        console.log('  justifyContent:', afterStyle.justifyContent);
        console.log('  width:', afterStyle.width);
        console.log('  float:', afterStyle.float);

        console.log('🔥🔥🔥 forceGridLayout完成 - 设置后内联样式:', {
          display: bottomIcons.style.display,
          gridTemplateColumns: bottomIcons.style.gridTemplateColumns,
          justifyContent: bottomIcons.style.justifyContent,
          width: bottomIcons.style.width,
          float: bottomIcons.style.float
        });
      } else {
        console.error('❌ 找不到bottom-icons元素');
      }
    } catch (error) {
      console.warn('强制grid布局设置出错:', error);
    }
  }

  private static updateBottomMenuVisibility_() {
    const navFooter = getEl('nav-footer');
    //const bottomIcons = getEl('bottom-icons');

    // 强制启用底部菜单，忽略设置
    if (navFooter) {
      showEl('nav-footer');
      navFooter.style.visibility = 'visible';
      navFooter.style.setProperty('display', 'block', 'important');
    }

    const bottomContainer = getEl('bottom-icons-container');

    if (bottomContainer) {
      const bottomHeight = bottomContainer.offsetHeight;

      document.documentElement.style.setProperty('--bottom-menu-top', `${bottomHeight}px`);
    }

    // 每次更新可见性时都强制设置透明背景和grid布局
    BottomMenu.forceTransparentBackgrounds();
    BottomMenu.forceGridLayout();

    // 强制显示底部菜单
    BottomMenu.forceShowBottomMenu();
  }

  private static forceShowBottomMenu() {
    setTimeout(() => {
      const navFooter = getEl('nav-footer');
      if (navFooter) {
        // 🔥 恢复原始滑动系统，确保显示状态
        navFooter.classList.remove('footer-slide-down');
        navFooter.classList.add('footer-slide-up');
        navFooter.style.setProperty('display', 'block', 'important');
        navFooter.style.setProperty('visibility', 'visible', 'important');
        navFooter.style.setProperty('opacity', '1', 'important');

        console.log('🔥 恢复底部菜单滑动系统完成');
      }
    }, 200);
  }

  private static deselectAllBottomMenuFilterButtons_() {
    const menuIds = [
      BottomMenu.basicMenuId, BottomMenu.advancedMenuId, BottomMenu.analysisMenuId,
      BottomMenu.experimentalMenuId, BottomMenu.settingsMenuId, BottomMenu.allMenuId,
    ];

    const menuElements = menuIds.map((id) => getEl(id));

    if (menuElements.every((el) => el !== null)) {
      menuElements.forEach((el) => el.classList.remove('bmenu-item-selected'));
    } else {
      errorManagerInstance.warn('Failed to find all bottom menu filter buttons');
    }
  }

  private static onBottomMenuFilterClick_(menuButtonDom: HTMLElement, menuMode: MenuMode) {
    keepTrackApi.getSoundManager()?.play(SoundNames.MENU_BUTTON);
    settingsManager.activeMenuMode = menuMode;
    this.deselectAllBottomMenuFilterButtons_();
    menuButtonDom.classList.add('bmenu-item-selected');
    keepTrackApi.emit(KeepTrackApiEvents.bottomMenuModeChange);
  }

  static addBottomMenuFilterButtons() {
    const menuBasicDom = getEl(BottomMenu.basicMenuId);
    const menuAdvancedDom = getEl(BottomMenu.advancedMenuId);
    const menuAnalysisDom = getEl(BottomMenu.analysisMenuId);
    const menuExperimentalDom = getEl(BottomMenu.experimentalMenuId);
    const menuSettingsDom = getEl(BottomMenu.settingsMenuId);
    const menuAllDom = getEl(BottomMenu.allMenuId);

    if (menuBasicDom && menuAdvancedDom && menuAnalysisDom && menuAllDom && menuExperimentalDom && menuSettingsDom) {
      menuBasicDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuBasicDom, MenuMode.BASIC));
      menuAdvancedDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuAdvancedDom, MenuMode.ADVANCED));
      menuAnalysisDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuAnalysisDom, MenuMode.ANALYSIS));
      menuExperimentalDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuExperimentalDom, MenuMode.EXPERIMENTAL));
      menuSettingsDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuSettingsDom, MenuMode.SETTINGS));
      menuAllDom.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(menuAllDom, MenuMode.ALL));

      keepTrackApi.emit(KeepTrackApiEvents.bottomMenuModeChange);
    } else {
      errorManagerInstance.warn('Failed to find all bottom menu filter buttons');
    }

    const wheel = (dom: EventTarget, deltaY: number) => {
      const domEl = dom as HTMLElement;
      const step = 0.15;
      const pos = domEl.scrollTop;
      const nextPos = pos + step * deltaY;

      domEl.scrollTop = nextPos;
    };

    ['bottom-icons', 'bottom-icons-filter'].forEach((divIdWithScroll) => {

      getEl(divIdWithScroll)!.addEventListener(
        'wheel',
        (event: WheelEvent) => {
          event.preventDefault(); // Prevent default scroll behavior
          if (event.currentTarget) {
            wheel(event.currentTarget, event.deltaY);
          }
        },
        { passive: false }, // Must be false to allow preventDefault()
      );
    });
  }

  static debugBottomMenuDrag() {
    console.log('🔍 DEBUG: 底部菜单拖动系统检查');

    const bottomContainer = getEl('bottom-icons-container');
    if (!bottomContainer) {
      console.error('❌ bottom-icons-container 未找到');
      return;
    }

    console.log('✅ bottom-icons-container 存在:', {
      offsetHeight: bottomContainer.offsetHeight,
      clientHeight: bottomContainer.clientHeight,
      scrollHeight: bottomContainer.scrollHeight,
      style: bottomContainer.style.cssText,
      position: bottomContainer.style.position,
      zIndex: bottomContainer.style.zIndex
    });

    // 检查拖动区域
    const dragHandles = bottomContainer.querySelectorAll('.drag-resize-handle, div[style*="cursor: n-resize"]');
    console.log('🔍 拖动手柄检查:', {
      count: dragHandles.length,
      handles: Array.from(dragHandles).map((handle, index) => ({
        index,
        className: handle.className,
        cursor: (handle as HTMLElement).style.cursor,
        position: (handle as HTMLElement).style.position,
        zIndex: (handle as HTMLElement).style.zIndex,
        pointerEvents: (handle as HTMLElement).style.pointerEvents,
        width: (handle as HTMLElement).style.width,
        height: (handle as HTMLElement).style.height,
        top: (handle as HTMLElement).style.top,
        background: (handle as HTMLElement).style.background
      }))
    });

    // 检查是否调用了clickAndDragHeight
    console.log('🔍 检查clickAndDragHeight是否被调用...');

    // 模拟点击测试
    if (dragHandles.length > 0) {
      const firstHandle = dragHandles[0] as HTMLElement;
      console.log('🔍 测试第一个拖动手柄的事件响应...');

      // 创建模拟鼠标事件
      const mouseEvent = new MouseEvent('mousedown', {
        clientX: 100,
        clientY: 100,
        bubbles: true,
        cancelable: true
      });

      firstHandle.dispatchEvent(mouseEvent);
      console.log('✅ 模拟mousedown事件已发送');
    } else {
      console.error('❌ 没有找到拖动手柄，可能clickAndDragHeight没有被调用');
    }
  }
}
