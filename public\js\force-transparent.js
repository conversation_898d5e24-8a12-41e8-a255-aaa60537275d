// 强制透明菜单 - 立即执行脚本
(function() {
  'use strict';
  
  console.log('🔥 强制透明菜单脚本开始执行');
  
  function forceTransparent() {
    try {
      // 底部菜单元素ID列表
      const elementIds = [
        'bottom-icons',
        'bottom-icons-filter',
        'bottom-icons-container',
        'nav-footer'
      ];

      // 通过ID强制设置透明
      elementIds.forEach(id => {
        try {
          const element = document.getElementById(id);
          if (element) {
            element.style.setProperty('background', 'transparent', 'important');
            element.style.setProperty('background-color', 'transparent', 'important');
            element.style.setProperty('background-image', 'none', 'important');
            console.log(`✓ 强制设置 ${id} 为透明`);
          }
        } catch (e) {
          console.warn(`设置 ${id} 透明时出错:`, e);
        }
      });
    
    // 通过选择器强制设置透明
    const selectors = [
      '#bottom-icons',
      '#bottom-icons-filter', 
      '#bottom-icons-container',
      '#nav-footer',
      'footer#nav-footer',
      'footer',
      '[id="bottom-icons"]',
      '[id="bottom-icons-filter"]',
      '[id="bottom-icons-container"]'
    ];
    
      selectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            element.style.setProperty('background', 'transparent', 'important');
            element.style.setProperty('background-color', 'transparent', 'important');
            element.style.setProperty('background-image', 'none', 'important');
          });
        } catch (e) {
          console.warn(`设置 ${selector} 透明时出错:`, e);
        }
      });

      // 强制设置菜单图标背景透明
      const menuIconSelectors = [
        '.bmenu-item',
        '.bmenu-filter-item',
        '.bmenu-item-inner',
        '.bmenu-filter-item-inner'
      ];

      menuIconSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            element.style.setProperty('background', 'transparent', 'important');
            element.style.setProperty('background-color', 'transparent', 'important');
            element.style.setProperty('background-image', 'none', 'important');
          });
        } catch (e) {
          console.warn(`设置菜单图标 ${selector} 透明时出错:`, e);
        }
      });

      console.log('🔥 强制透明设置完成（包括菜单图标）');
    } catch (error) {
      console.warn('强制透明设置出错:', error);
    }
  }
  
  // 立即执行
  forceTransparent();
  
  // DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', forceTransparent);
  } else {
    forceTransparent();
  }
  
  // 页面完全加载后执行
  window.addEventListener('load', forceTransparent);

  // 🔧 优化：移除定时器，只在特定事件时执行
  // 监听窗口大小变化
  window.addEventListener('resize', function() {
    setTimeout(forceTransparent, 100);
  });

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      setTimeout(forceTransparent, 100);
    }
  });
  
  console.log('🔥 强制透明菜单脚本初始化完成');
})();
