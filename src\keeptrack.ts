/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

/* eslint-disable no-unreachable */

import blueMarbleJpg from '@public/img/wallpaper/blue-marble.jpg';
import cubesatJpg from '@public/img/wallpaper/cubesat.jpg';
import earthJpg from '@public/img/wallpaper/Earth.jpg';
import issJpg from '@public/img/wallpaper/iss.jpg';
import moonJpg from '@public/img/wallpaper/moon.jpg';
import observatoryJpg from '@public/img/wallpaper/observatory.jpg';
import rocketJpg from '@public/img/wallpaper/rocket.jpg';
import rocket2Jpg from '@public/img/wallpaper/rocket2.jpg';
import rocket3Jpg from '@public/img/wallpaper/rocket3.jpg';
import rocket4Jpg from '@public/img/wallpaper/rocket4.jpg';
import satJpg from '@public/img/wallpaper/sat.jpg';
import sat2Jpg from '@public/img/wallpaper/sat2.jpg';
import telescopeJpg from '@public/img/wallpaper/telescope.jpg';
import thuleJpg from '@public/img/wallpaper/thule.jpg';

import 'material-icons/iconfont/material-icons.css';


import { Milliseconds } from 'ootk';
import { keepTrackContainer } from './container';
import { KeepTrackApiEvents, Singletons } from './interfaces';
import { keepTrackApi } from './keepTrackApi';
import { getEl } from './lib/get-el';
import { SelectSatManager } from './plugins/select-sat-manager/select-sat-manager';
import { SensorManager } from './plugins/sensor/sensorManager';
import { settingsManager, SettingsManagerOverride } from './settings/settings';
import { VERSION } from './settings/version.js';
import { Camera, CameraType } from './singletons/camera';
import { CameraControlWidget } from './singletons/camera-control-widget';
import { CatalogManager } from './singletons/catalog-manager';
import { ColorSchemeManager } from './singletons/color-scheme-manager';
import { DemoManager } from './singletons/demo-mode';
import { DotsManager } from './singletons/dots-manager';
import { LineManager, lineManagerInstance } from './singletons/draw-manager/line-manager';
import { ErrorManager, errorManagerInstance } from './singletons/errorManager';
import { GroupsManager } from './singletons/groups-manager';
import { HoverManager } from './singletons/hover-manager';
import { InputManager } from './singletons/input-manager';
import { OrbitManager } from './singletons/orbitManager';
import { Scene } from './singletons/scene';
import { TimeManager } from './singletons/time-manager';
import { UiManager } from './singletons/uiManager';
import { WebGLRenderer } from './singletons/webgl-renderer';
import { BottomMenu } from './static/bottom-menu';
import { CatalogLoader } from './static/catalog-loader';
import { isThisNode } from './static/isThisNode';
import { SensorMath } from './static/sensor-math';
import { SplashScreen } from './static/splash-screen';

export class KeepTrack {
  /** An image is picked at random and then if the screen is bigger than 1080p then it loads the next one in the list */
  private static readonly splashScreenImgList_ =
    [blueMarbleJpg, moonJpg, observatoryJpg, thuleJpg, rocketJpg, rocket2Jpg, telescopeJpg, issJpg, rocket3Jpg, rocket4Jpg, cubesatJpg, satJpg, sat2Jpg, earthJpg];

  isReady = false;
  private isUpdateTimeThrottle_: boolean;
  private lastGameLoopTimestamp_ = <Milliseconds>0;
  private readonly settingsOverride_: SettingsManagerOverride;

  colorManager: ColorSchemeManager;
  demoManager: DemoManager;
  dotsManager: DotsManager;
  errorManager: ErrorManager;
  lineManager: LineManager;
  colorSchemeManager: ColorSchemeManager;
  orbitManager: OrbitManager;
  catalogManager: CatalogManager;
  timeManager: TimeManager;
  renderer: WebGLRenderer;
  sensorManager: SensorManager;
  uiManager: UiManager;
  inputManager: InputManager;
  mainCameraInstance: Camera;
  cameraControlWidget: CameraControlWidget;

  constructor(
    settingsOverride: SettingsManagerOverride = {
      isPreventDefaultHtml: false,
      isShowSplashScreen: true,
    },
  ) {
    if (this.isReady) {
      throw new Error('程序已经运行');
    }

    this.settingsOverride_ = settingsOverride;
  }

  init() {
    console.log('🔍 DEBUG: KeepTrack.init() 开始执行...');

    try {
      console.log('🔍 DEBUG: 初始化 settingsManager...');
      settingsManager.init(this.settingsOverride_);
      console.log('✅ DEBUG: settingsManager 初始化完成');

      console.log('🔍 DEBUG: 设置容器元素...');
      KeepTrack.setContainerElement();
      console.log('✅ DEBUG: 容器元素设置完成');

      if (!this.settingsOverride_.isPreventDefaultHtml) {
        console.log('🔍 DEBUG: 加载默认HTML和CSS...');
        import(/* webpackMode: "eager" */ '@css/loading-screen.css');
        KeepTrack.getDefaultBodyHtml();
        BottomMenu.init();
        console.log('✅ DEBUG: 默认HTML和CSS加载完成');

        if (!isThisNode() && settingsManager.isShowSplashScreen) {
          console.log('🔍 DEBUG: 加载启动画面...');
          KeepTrack.loadSplashScreen_();
          console.log('✅ DEBUG: 启动画面加载完成');
        }
      }

      console.log('✅ DEBUG: KeepTrack.init() 执行完成');
    } catch (error) {
      console.error('❌ DEBUG: KeepTrack.init() 执行失败:', error);
      throw error;
    }

    try {
      console.log('🔍 DEBUG: 开始创建核心管理器实例...');

      console.log('🔍 DEBUG: 创建 OrbitManager...');
      const orbitManagerInstance = new OrbitManager();
      keepTrackContainer.registerSingleton(Singletons.OrbitManager, orbitManagerInstance);
      console.log('✅ DEBUG: OrbitManager 创建完成');

      console.log('🔍 DEBUG: 创建 CatalogManager...');
      const catalogManagerInstance = new CatalogManager();
      keepTrackContainer.registerSingleton(Singletons.CatalogManager, catalogManagerInstance);
      console.log('✅ DEBUG: CatalogManager 创建完成');

      console.log('🔍 DEBUG: 创建 GroupsManager...');
      const groupManagerInstance = new GroupsManager();
      keepTrackContainer.registerSingleton(Singletons.GroupsManager, groupManagerInstance);
      console.log('✅ DEBUG: GroupsManager 创建完成');

      console.log('🔍 DEBUG: 创建 TimeManager...');
      const timeManagerInstance = new TimeManager();
      keepTrackContainer.registerSingleton(Singletons.TimeManager, timeManagerInstance);
      console.log('✅ DEBUG: TimeManager 创建完成');

      console.log('🔍 DEBUG: 创建 WebGLRenderer...');
      const rendererInstance = new WebGLRenderer();
      console.log('✅ DEBUG: WebGLRenderer 创建完成');

      keepTrackContainer.registerSingleton(Singletons.WebGLRenderer, rendererInstance);
      keepTrackContainer.registerSingleton(Singletons.MeshManager, rendererInstance.meshManager);

      console.log('🔍 DEBUG: 创建 Scene...');
      const sceneInstance = new Scene({
        gl: keepTrackApi.getRenderer().gl,
      });
      keepTrackContainer.registerSingleton(Singletons.Scene, sceneInstance);
      console.log('✅ DEBUG: Scene 创建完成');

      console.log('🔍 DEBUG: 创建其他管理器...');
      const sensorManagerInstance = new SensorManager();
      keepTrackContainer.registerSingleton(Singletons.SensorManager, sensorManagerInstance);

      const dotsManagerInstance = new DotsManager();
      keepTrackContainer.registerSingleton(Singletons.DotsManager, dotsManagerInstance);

      const uiManagerInstance = new UiManager();
      keepTrackContainer.registerSingleton(Singletons.UiManager, uiManagerInstance);

      const colorSchemeManagerInstance = new ColorSchemeManager();
      keepTrackContainer.registerSingleton(Singletons.ColorSchemeManager, colorSchemeManagerInstance);

      const inputManagerInstance = new InputManager();
      keepTrackContainer.registerSingleton(Singletons.InputManager, inputManagerInstance);

      const sensorMathInstance = new SensorMath();
      keepTrackContainer.registerSingleton(Singletons.SensorMath, sensorMathInstance);

      const mainCameraInstance = new Camera();
      const cameraControlWidget = new CameraControlWidget();
      this.cameraControlWidget = cameraControlWidget;
      keepTrackContainer.registerSingleton(Singletons.MainCamera, mainCameraInstance);

      const hoverManagerInstance = new HoverManager();
      keepTrackContainer.registerSingleton(Singletons.HoverManager, hoverManagerInstance);

      console.log('🔍 DEBUG: 设置实例属性...');
      this.mainCameraInstance = mainCameraInstance;
      this.errorManager = errorManagerInstance;
      this.dotsManager = dotsManagerInstance;
      this.lineManager = lineManagerInstance;
      this.colorSchemeManager = colorSchemeManagerInstance;
      this.orbitManager = orbitManagerInstance;
      this.catalogManager = catalogManagerInstance;
      this.timeManager = timeManagerInstance;
      this.renderer = rendererInstance;
      this.sensorManager = sensorManagerInstance;
      this.uiManager = uiManagerInstance;
      this.inputManager = inputManagerInstance;
      this.demoManager = new DemoManager();

      console.log('✅ DEBUG: 所有核心管理器创建完成');
    } catch (error) {
      console.error('❌ DEBUG: 核心管理器创建失败:', error);
      throw error;
    }
  }

  /** Check if the FPS is above a certain threshold */
  static isFpsAboveLimit(dt: Milliseconds, minimumFps: number): boolean {
    return KeepTrack.getFps_(dt) > minimumFps;
  }

  gameLoop(timestamp = <Milliseconds>0): void {
    requestAnimationFrame(this.gameLoop.bind(this));
    const dt = <Milliseconds>(timestamp - this.lastGameLoopTimestamp_);

    this.lastGameLoopTimestamp_ = timestamp;

    if (settingsManager.cruncherReady) {
      this.update_(dt); // Do any per frame calculations
      this.draw_(dt);

      if ((keepTrackApi.getPlugin(SelectSatManager)?.selectedSat ?? -1) > -1) {
        const selectedSatellite = keepTrackApi.getPlugin(SelectSatManager)?.primarySatObj;

        if (selectedSatellite) {
          keepTrackApi.getUiManager().
            updateSelectBox(this.timeManager.realTime, this.timeManager.lastBoxUpdateTime, selectedSatellite);
        }
      }
    }
  }

  static getDefaultBodyHtml(): void {
    if (!keepTrackApi.containerRoot) {
      throw new Error('Container root is not set');
    }

    SplashScreen.initLoadingScreen(keepTrackApi.containerRoot);

    keepTrackApi.containerRoot.id = 'keeptrack-root';
    keepTrackApi.containerRoot.innerHTML += keepTrackApi.html`
      <header>
        <div id="keeptrack-header" class="start-hidden"></div>
      </header>
      <main>
        <div id="rmb-wrapper"></div>
        <div id="canvas-holder">
        <div id="logo-primary" class="start-hidden">
            <a href="http://spacedefense.cn" target="_blank">
              <img src="${settingsManager.installDirectory}img/logo-primary.png" alt="KeepTrack">
            </a>
          </div>
          <div id="logo-secondary" class="start-hidden">
            <a href="https://celestrak.org" target="_blank">
              <img src="${settingsManager.installDirectory}img/logo-secondary.png" alt="Celestrak">
            </a>
          </div>
          <canvas id="keeptrack-canvas"></canvas>
          <div id="ui-wrapper">
            <div id="sat-hoverbox">
              <span id="sat-hoverbox1"></span>
              <span id="sat-hoverbox2"></span>
              <span id="sat-hoverbox3"></span>
            </div>
            <div id="sat-minibox"></div>

            <div id="legend-hover-menu" class="start-hidden"></div>
            <aside id="left-menus"></aside>
          </div>
        </div>
        <figcaption id="info-overlays">
          <div id="camera-status-box" class="start-hidden status-box">Earth Centered Camera Mode</div>
          <div id="propRate-status-box" class="start-hidden status-box">Propagation Rate: 1.00x</div>
        </figcaption>
      </main>
      <footer id="nav-footer" class="page-footer resizable">
        <div id="footer-handle" class="ui-resizable-handle ui-resizable-n"></div>
        <div id="footer-toggle-wrapper">
          <div id="nav-footer-toggle">&#x25BC;</div>
        </div>
      </footer>`;

    if (!settingsManager.isShowSplashScreen) {
      /*
       * hideEl('loading-screen');
       * hideEl('nav-footer');
       */
    }
  }

  private static setContainerElement() {
    // User provides the container using the settingsManager
    const containerDom = settingsManager.containerRoot ?? document.getElementById('keeptrack-root') as HTMLDivElement;

    if (!containerDom) {
      throw new Error('Failed to find container');
    }

    // If no current shadow DOM, create one - this is mainly for testing
    if (!keepTrackApi.containerRoot) {
      keepTrackApi.containerRoot = containerDom;
    }
  }

  private static getFps_(dt: Milliseconds): number {
    return 1000 / dt;
  }

  /* istanbul ignore next */
  static async initCss(): Promise<void> {
    try {
      if (!isThisNode()) {
        KeepTrack.printLogoToConsole_();
      }

      // Load the CSS - 优化后的加载顺序
      if (!settingsManager.isDisableCss) {
        // 1. 基础样式
        import('@css/fonts.css');
        import(/* webpackMode: "eager" */ '@css/materialize.css').catch(() => {
          // This is intentional
        });
        import(/* webpackMode: "eager" */ '@css/astroux/css/astro.css').catch(() => {
          // This is intentional
        });
        import(/* webpackMode: "eager" */ '@css/materialize-local.css').catch(() => {
          // This is intentional
        });

        // 2. 主样式文件
        import(/* webpackMode: "eager" */ '@css/style.css')
          .then(
            // 3. 响应式样式
            await import(/* webpackMode: "eager" */ '@css/responsive-sm.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            await import(/* webpackMode: "eager" */ '@css/responsive-md.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            await import(/* webpackMode: "eager" */ '@css/responsive-lg.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            await import(/* webpackMode: "eager" */ '@css/responsive-xl.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            await import(/* webpackMode: "eager" */ '@css/responsive-2xl.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            // 4. 🔥🔥🔥 统一透明效果（替代transparent-menus.css和force-transparent.css）
            await import(/* webpackMode: "eager" */ '@css/transparency-unified.css').catch(() => {
              // This is intentional
            }),
          )
          .then(
            // 5. 🔥🔥🔥 样式变体（替代style.embed.css和style.celestrak.css）
            await import(/* webpackMode: "eager" */ '@css/style-variants.css').catch(() => {
              // This is intentional
            }),
          )
          .then(() => {
            // 6. 🔥🔥🔥 在所有基础CSS加载后，立即设置缩放变量
            console.log('🎯 基础CSS加载完成，设置缩放变量...');
            initSystemScaleDetection();
          })
          .then(
            // 7. 🔥🔥🔥 最后加载优化的菜单样式，确保最高优先级
            await import(/* webpackMode: "eager" */ '@css/menu-styles-optimized.css').catch(() => {
              // This is intentional
            }),
          )
          .then(() => {
            // 8. 🔥🔥🔥 所有CSS加载完成后，强制应用缩放并重新渲染
            console.log('🎯 所有CSS加载完成，强制应用缩放...');
            setTimeout(() => {
              initSystemScaleDetection();
              // 强制重新渲染所有元素
              document.body.style.display = 'none';
              document.body.offsetHeight;
              document.body.style.display = '';
            }, 50);
          })
          .catch(() => {
            // This is intentional
          });
      } else if (settingsManager.enableLimitedUI) {
        import(/* webpackMode: "eager" */ '@css/limitedUI.css').catch(() => {
          // This is intentional
        });
      }
    } catch (e) {
      // intentionally left blank
    }
  }

  /* istanbul ignore next */
  private static loadSplashScreen_(): void {
    // Randomly load a splash screen - not a vulnerability
    const image = KeepTrack.splashScreenImgList_[Math.floor(Math.random() * KeepTrack.splashScreenImgList_.length)];
    const loadingDom = getEl('loading-screen');

    if (loadingDom) {
      loadingDom.style.backgroundImage = `url(${image})`;
      loadingDom.style.backgroundSize = 'cover';
      loadingDom.style.backgroundPosition = 'center';
      loadingDom.style.backgroundRepeat = 'no-repeat';
    } else {
      errorManagerInstance.debug('Failed to load splash screen');
    }

    // Preload the rest of the images after 30 seconds
    setTimeout(() => {
      KeepTrack.splashScreenImgList_.forEach((img) => {
        const preloadImg = new Image();

        preloadImg.src = img;
      });
    }, 30000);
  }

  private static printLogoToConsole_() {
    // eslint-disable-next-line no-console
    console.log([
      '',
      ' #####                                   ######                                                  ',
      '#     #  #####     ##     ####   ######  #     #  ######  ######  ######  #    #   ####   ######',
      '#        #    #   #  #   #    #  #       #     #  #       #       #       ##   #  #       #     ',
      ' #####   #    #  #    #  #       #####   #     #  #####   #####   #####   # #  #   ####   ##### ',
      '      #  #####   ######  #       #       #     #  #       #       #       #  # #       #  #     ',
      '#     #  #       #    #  #    #  #       #     #  #       #       #       #   ##  #    #  #     ',
      ' #####   #       #    #   ####   ######  ######   ######  #       ######  #    #   ####   ######',
                                                                                                                                                                                                   
      '================================================================================================',
      '------北京星地探索科技有限公司   官网：www.spacedefense.cn   电子邮箱：<EMAIL>',
      ''
    ].join('\\n'));
  }

  /**
   * 检查用户认证状态
   */
  private async checkAuthentication(): Promise<boolean> {
    console.log('🔍 DEBUG: checkAuthentication() 开始执行...');
    console.log('🔍 DEBUG: localStorage 可用:', typeof localStorage !== 'undefined');

    try {
      const token = localStorage.getItem('authToken');
      console.log('🔍 DEBUG: 从 localStorage 获取 token:', token ? '存在' : '不存在');

      if (!token) {
        console.log('❌ DEBUG: 未找到认证令牌，返回 false');
        return false;
      }

      console.log('✅ DEBUG: 找到认证令牌，返回 true');
      return true;

      return false;
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }



  private static showErrorCode(error: Error & { lineNumber: number }): void {
    // TODO: Replace console calls with ErrorManagerInstance

    let errorHtml = '';

    errorHtml += error?.message ? `${error.message}<br>` : '';
    errorHtml += error?.lineNumber ? `Line: ${error.lineNumber}<br>` : '';
    errorHtml += error?.stack ? `${error.stack}<br>` : '';
    const LoaderText = getEl('loader-text');

    if (LoaderText) {
      LoaderText.innerHTML = errorHtml;
      // eslint-disable-next-line no-console
      console.error(error);
    } else {
      // eslint-disable-next-line no-console
      console.error(error);
    }
    // istanbul ignore next
    if (!isThisNode()) {
      // eslint-disable-next-line no-console
      console.warn(error);
    }
  }

  private draw_(dt = <Milliseconds>0) {
    const renderer = keepTrackApi.getRenderer();
    const camera = keepTrackApi.getMainCamera();

    camera.draw(keepTrackApi.getPlugin(SelectSatManager)?.primarySatObj, renderer.sensorPos);
    renderer.render(keepTrackApi.getScene(), keepTrackApi.getMainCamera());

    // 在近距离模式下提高更新频率以减少抖动
    const isNearMode = camera.cameraType === CameraType.SATELLITE || camera.cameraType === CameraType.FIXED_TO_SAT || camera.camDistBuffer < settingsManager.nearZoomLevel;
    const fpsLimit = isNearMode ? 2 : 5; // 近距离模式下降低FPS限制，提高更新频率

    if (KeepTrack.isFpsAboveLimit(dt, fpsLimit) && !settingsManager.lowPerf && !settingsManager.isDragging && !settingsManager.isDemoModeOn) {
      keepTrackApi.getOrbitManager().updateAllVisibleOrbits();
      this.inputManager.update(dt);

      // Only update hover if we are not on mobile
      if (!settingsManager.isMobileModeEnabled) {
        keepTrackApi.getHoverManager().setHoverId(this.inputManager.mouse.mouseSat, keepTrackApi.getMainCamera().mouseX, keepTrackApi.getMainCamera().mouseY);
      }
    }

    // If Demo Mode do stuff
    if (settingsManager.isDemoModeOn && keepTrackApi.getSensorManager()?.currentSensors[0]?.lat !== null) {
      this.demoManager.update();
    }

    keepTrackApi.emit(KeepTrackApiEvents.endOfDraw, dt);
  }

  async run(): Promise<void> {
    try {
      // 可选的认证检查（不强制）
      const isAuthenticated = await this.checkAuthentication();
      if (isAuthenticated) {
        console.log('用户已认证');
      } else {
        console.log('用户未认证，但允许访问基础功能');
      }

      const catalogManagerInstance = keepTrackApi.getCatalogManager();
      const orbitManagerInstance = keepTrackApi.getOrbitManager();
      const timeManagerInstance = keepTrackApi.getTimeManager();
      const renderer = keepTrackApi.getRenderer();
      const sceneInstance = keepTrackApi.getScene();
      const dotsManagerInstance = keepTrackApi.getDotsManager();
      const uiManagerInstance = keepTrackApi.getUiManager();
      const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();
      const inputManagerInstance = keepTrackApi.getInputManager();

      // Upodate the version number and date
      settingsManager.versionNumber = VERSION;

      // Error Trapping
      window.addEventListener('error', (e: ErrorEvent) => {
        if (!settingsManager.isGlobalErrorTrapOn) {
          return;
        }
        if (isThisNode()) {
          throw e.error;
        }
        errorManagerInstance.error(e.error, 'Global Error Trapper', e.message);
      });

      keepTrackApi.getMainCamera().init(settingsManager);

      SplashScreen.loadStr(SplashScreen.msg.science);

      // Load all the plugins now that we have the API initialized
      await import('./plugins/plugins')
        .then((mod) => mod.loadPlugins(keepTrackApi, settingsManager.plugins))
        .catch(() => {
          // intentionally left blank
        });

      SplashScreen.loadStr(SplashScreen.msg.science2);
      // Start initializing the rest of the website
      timeManagerInstance.init();
      uiManagerInstance.onReady();

      SplashScreen.loadStr(SplashScreen.msg.dots);
      /*
       * MobileManager.checkMobileMode();
       * We need to know if we are on a small screen before starting webgl
       */
      await renderer.glInit();

      sceneInstance.init(renderer.gl);
      sceneInstance.loadScene();

      dotsManagerInstance.init(settingsManager);

      catalogManagerInstance.initObjects();

      catalogManagerInstance.init();
      colorSchemeManagerInstance.init();

      await CatalogLoader.load(); // Needs Object Manager and gl first

      lineManagerInstance.init();

      orbitManagerInstance.init(lineManagerInstance, renderer.gl);

      uiManagerInstance.init();

      dotsManagerInstance.initBuffers(colorSchemeManagerInstance.colorBuffer!);

      inputManagerInstance.init();

      await renderer.init(settingsManager);
      renderer.meshManager.init(renderer.gl);

      // Now that everything is loaded, start rendering to thg canvas
      this.gameLoop();

      this.postStart_();
      this.isReady = true;
    } catch (error) {
      KeepTrack.showErrorCode(<Error & { lineNumber: number }>error);
    }
  }

  private postStart_() {
    // UI Changes after everything starts -- DO NOT RUN THIS EARLY IT HIDES THE CANVAS
    UiManager.postStart();

    if (settingsManager.cruncherReady) {
      /*
       * Create Container Div
       * NOTE: This needs to be done before uiManagerFinal
       */
      if (settingsManager.plugins.DebugMenuPlugin) {
        const uiWrapperDom = getEl('ui-wrapper');

        if (uiWrapperDom) {
          uiWrapperDom.innerHTML += '<div id="eruda"></div>';
        }
      }

      // Update any CSS now that we know what is loaded
      keepTrackApi.emit(KeepTrackApiEvents.uiManagerFinal);



      keepTrackApi.getUiManager().initMenuController();

      // Update MaterialUI with new menu options
      try {
        // Jest workaround
        // eslint-disable-next-line new-cap
        window.M.AutoInit();
      } catch {
        // intentionally left blank
      }

      window.addEventListener('resize', () => {
        keepTrackApi.emit(KeepTrackApiEvents.resize);
      });
      keepTrackApi.emit(KeepTrackApiEvents.resize);

      keepTrackApi.isInitialized = true;
      keepTrackApi.emit(KeepTrackApiEvents.onKeepTrackReady);
      if (settingsManager.onLoadCb) {
        settingsManager.onLoadCb();
      }
    } else {
      // 🔧 优化：使用requestAnimationFrame而不是setTimeout
      requestAnimationFrame(() => {
        this.postStart_();
      });
    }
  }

  private update_(dt = <Milliseconds>0) {
    const timeManagerInstance = keepTrackApi.getTimeManager();
    const renderer = keepTrackApi.getRenderer();
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    renderer.dt = dt;
    renderer.dtAdjusted = <Milliseconds>(Math.min(renderer.dt / 1000.0, 1.0 / Math.max(timeManagerInstance.propRate, 0.001)) * timeManagerInstance.propRate);

    this.timeManager.update();

    keepTrackApi.emit(KeepTrackApiEvents.update, dt);

    // Update official time for everyone else
    timeManagerInstance.setNow(<Milliseconds>Date.now());
    // 🔧 优化：使用requestAnimationFrame而不是setTimeout来节流
    if (!this.isUpdateTimeThrottle_) {
      this.isUpdateTimeThrottle_ = true;
      timeManagerInstance.setSelectedDate(timeManagerInstance.simulationTimeObj);
      requestAnimationFrame(() => {
        this.isUpdateTimeThrottle_ = false;
      });
    }

    // Update Draw Positions
    keepTrackApi.getDotsManager().updatePositionBuffer();

    renderer.update();

    /*
     * Update Colors
     * NOTE: We used to skip this when isDragging was true, but its so efficient that doesn't seem necessary anymore
     */
    colorSchemeManagerInstance.calculateColorBuffers(false); // avoid recalculating ALL colors
  }

  // Make the api available
  api = keepTrackApi;
}

/**
 * 系统缩放检测和应用 - 恢复正确的缩放逻辑
 */
function initSystemScaleDetection(): void {
  try {
    const devicePixelRatio = window.devicePixelRatio || 1;
    const screenWidth = screen.width;
    const windowWidth = window.innerWidth;
    const screenToWindowRatio = screenWidth / windowWidth;

    let scale = 1;

    console.log('🔍 系统缩放检测:');
    console.log('- devicePixelRatio:', devicePixelRatio);
    console.log('- screen.width:', screenWidth);
    console.log('- window.innerWidth:', windowWidth);

    // 🔥 增强缩放检测 - 特别针对大分辨率桌面
    if (devicePixelRatio > 1.1) {
      scale = devicePixelRatio;
      console.log('✅ 使用devicePixelRatio:', scale);
    } else if (screenToWindowRatio > 1.2) {
      scale = screenToWindowRatio;
      console.log('✅ 使用屏幕比例:', scale);
    } else if (screenWidth >= 2560) {
      // 4K或更高分辨率，强制放大
      scale = 1.5;
      console.log('✅ 4K+屏幕强制缩放:', scale);
    } else if (screenWidth >= 1920) {
      // 1080p高分辨率，适度放大
      scale = 1.2;
      console.log('✅ 1080p+屏幕适度缩放:', scale);
    }

    // 限制范围，但允许更大的缩放
    scale = Math.max(1, Math.min(4, scale));

    // 设置CSS变量
    const root = document.documentElement;
    root.style.setProperty('--system-scale-factor', scale.toString(), 'important');
    document.body.style.setProperty('--system-scale-factor', scale.toString(), 'important');

    console.log('🎯 应用缩放因子:', scale);

    // 🔥🔥🔥 强化缩放应用 - 修复所有具体问题
    let fixStyle = document.getElementById('comprehensive-scaling-fix');
    if (!fixStyle) {
      fixStyle = document.createElement('style');
      fixStyle.id = 'comprehensive-scaling-fix';
      document.head.appendChild(fixStyle);
    }

    // 计算实际字体大小（基础字体增大）
    const baseFont = Math.round(18 / scale);
    const largeFont = Math.round(22 / scale);
    const iconSize = Math.round(28 / scale);
    const titleFont = Math.round(20 / scale);

    fixStyle.textContent = `
      /* 🔥 1. 修复顶部菜单日期时间字体一致性 */
      #jday,
      #datetime-text,
      #datetime-title {
        font-size: ${largeFont}px !important;
        line-height: ${largeFont + 4}px !important;
        color: white !important;
        font-family: 'Roboto Mono', monospace !important;
      }

      /* 🔥 2. 修复顶部菜单图标 */
      .top-menu .material-icons,
      #top-menu .material-icons,
      .navbar .material-icons {
        font-size: ${iconSize}px !important;
        width: ${iconSize}px !important;
        height: ${iconSize}px !important;
      }

      /* 🔥 3. 修复搜索框和清除图标（不改变宽度） */
      #search,
      .search-input {
        font-size: ${baseFont}px !important;
        padding: ${Math.round(8 / scale)}px ${Math.round(12 / scale)}px !important;
        height: ${Math.round(36 / scale)}px !important;
        border: none !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 0 !important;
        background: transparent !important;
        outline: none !important;
        box-shadow: none !important;
      }

      #search-clear,
      .search-clear {
        font-size: ${iconSize}px !important;
        width: ${iconSize}px !important;
        height: ${iconSize}px !important;
      }

      /* 🔥 4. 修复sat-info-box卫星名字 */
      #sat-info-title-name {
        font-size: ${titleFont}px !important;
        line-height: ${titleFont + 4}px !important;
        font-weight: bold !important;
      }

      /* 🔥 5. 修复sat-info-box参数和值 */
      #sat-infobox .sat-info-key,
      #sat-infobox .sat-info-value {
        font-size: ${baseFont}px !important;
        line-height: ${baseFont + 4}px !important;
      }

      /* 🔥 6. 修复时间管理器 */
      #datetime,
      #datetime-input,
      #ui-datepicker-div,
      .ui-timepicker-div {
        font-size: ${baseFont}px !important;
      }

      #ui-datepicker-div button,
      .ui-timepicker-div button {
        font-size: ${Math.round(14 / scale)}px !important;
        padding: ${Math.round(6 / scale)}px ${Math.round(10 / scale)}px !important;
      }

      /* 🔥 7. 修复所有Material Icons */
      .material-icons {
        font-size: ${iconSize}px !important;
        width: ${iconSize}px !important;
        height: ${iconSize}px !important;
      }

      /* 🔥 8. 修复侧菜单按钮和文字 */
      .side-menu button,
      #settings-menu button {
        font-size: ${baseFont}px !important;
        padding: ${Math.round(10 / scale)}px ${Math.round(14 / scale)}px !important;
        min-height: ${Math.round(40 / scale)}px !important;
      }

      .side-menu,
      #settings-menu {
        font-size: ${baseFont}px !important;
        line-height: ${baseFont + 4}px !important;
      }

      .side-menu label,
      #settings-menu label {
        font-size: ${baseFont}px !important;
      }

      /* 🔥 9. 修复sat-info-box宽度（适合笔记本屏幕） */
      #sat-infobox {
        width: ${Math.round(50 / scale)}% !important;
        max-width: ${Math.round(450 / scale)}px !important;
      }

      @media (max-width: 1366px) {
        #sat-infobox {
          width: ${Math.round(60 / scale)}% !important;
          max-width: ${Math.round(400 / scale)}px !important;
        }
      }

      /* 🔥 10. 修复tooltip */
      [data-tooltip]:before {
        font-size: ${Math.round(14 / scale)}px !important;
        padding: ${Math.round(8 / scale)}px !important;
        width: ${Math.round(150 / scale)}px !important;
        margin-left: ${Math.round(-75 / scale)}px !important;
      }
    `;







  } catch (error) {
    console.warn('❌ 缩放检测失败:', error);
  }
}