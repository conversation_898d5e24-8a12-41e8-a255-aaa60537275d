/* eslint-disable max-lines */
/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * @Copyright 北京星地探索科技有限公司
 *
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode, SensorGeolocation } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import type { FilterPluginSettings } from '@app/plugins/filter-menu/filter-menu';
import { ColorSchemeColorMap } from '@app/singletons/color-schemes/color-scheme';
import { ObjectTypeColorSchemeColorMap } from '@app/singletons/color-schemes/object-type-color-scheme';
import { AtmosphereSettings, EarthBumpTextureQuality, EarthCloudTextureQuality, EarthDayTextureQuality, EarthNightTextureQuality, EarthPoliticalTextureQuality, EarthSpecTextureQuality, EarthTextureStyle } from '@app/singletons/draw-manager/earth';
import { SunTextureQuality } from '@app/singletons/draw-manager/sun';
import { MobileManager } from '@app/singletons/mobileManager';
import { UrlManager } from '@app/static/url-manager';
import { Degrees, Kilometers, Milliseconds } from 'ootk';
import { RADIUS_OF_EARTH } from '../lib/constants';
import { PersistenceManager, StorageKey } from '../singletons/persistence-manager';
import { ClassificationString } from '../static/classification';
import { isThisNode } from '../static/isThisNode';
import { defaultColorSettings } from './default-color-settings';
import { defaultPlugins } from './default-plugins';
import { parseGetVariables } from './parse-get-variables';
import { SettingsPresets } from './presets/presets';

export class SettingsManager {
  constructor() {
    this.installDirectory = './dist/'; // 默认资源路径
    // 设置地球纹理默认值 - 使用低质量纹理以提高性能
    this.earthTextureStyle = EarthTextureStyle.BLUE_MARBLE; // 使用蓝色大理石纹理
    this.earthDayTextureQuality = EarthDayTextureQuality.MEDIUM; // 中等质量白天纹理
    this.earthNightTextureQuality = EarthNightTextureQuality.MEDIUM; // 中等质量夜间纹理
    this.earthBumpTextureQuality = EarthBumpTextureQuality.OFF; // 关闭凹凸贴图
    this.earthSpecTextureQuality = EarthSpecTextureQuality.OFF; // 关闭镜面反射
    this.earthPoliticalTextureQuality = EarthPoliticalTextureQuality.OFF; // 关闭政治边界
    this.earthCloudTextureQuality = EarthCloudTextureQuality.OFF; // 关闭云层
  }
  installDirectory: string;
  earthTextureStyle: EarthTextureStyle;
  earthDayTextureQuality = EarthDayTextureQuality.MEDIUM; // 默认中等质量
  earthNightTextureQuality = EarthNightTextureQuality.MEDIUM; // 默认中等质量
  earthBumpTextureQuality = EarthBumpTextureQuality.OFF; // 关闭凹凸贴图
  earthSpecTextureQuality = EarthSpecTextureQuality.OFF; // 关闭镜面反射
  earthCloudTextureQuality = EarthCloudTextureQuality.OFF; // 关闭云层
  earthPoliticalTextureQuality = EarthPoliticalTextureQuality.OFF; // 关闭政治边界

  /**
   * 用于保存分类字符串的变量，未使用时设置为`null`
   */
  classificationStr = null as ClassificationString | null;
  activeMenuMode: MenuMode = MenuMode.BASIC;
  // 控制加载哪些内置插件
  plugins = defaultPlugins;
  changeTimeWithKeyboardAmountBig = 1000 * 60 * 60 as Milliseconds; // 1小时
  changeTimeWithKeyboardAmountSmall = 1000 * 60 as Milliseconds; // 1分钟

  filter: FilterPluginSettings = {};
  /**
   * 启用/禁用卫星信息框的任务数据部分。如果数据集不包含任务数据，则此设置无效。
   */
  isMissionDataEnabled = true;
  /**
   * 如果绘制ECF轨道，这是要绘制的轨道数量。
   */
  numberOfEcfOrbitsToDraw = 1;
  /**
   * 绘制协方差椭球时使用的置信水平。
   * 1 = 68.27% 置信度
   * 2 = 95.45% 置信度
   * 3 = 99.73% 置信度
   */
  covarianceConfidenceLevel: number = 2;
  /**
   * 确定是否应绘制协方差椭球的标志。
   */
  isDrawCovarianceEllipsoid = false;
  isDrawPoliticalMap = false; // 关闭政治边界以提高性能
  isDrawCloudsMap = false; // 关闭云层以提高性能
  sunTextureQuality: SunTextureQuality;
  isEarthGrayScale = false;
  isEarthAmbientLighting = true;
  isBlockPersistence = false;
  /** 选择卫星时将其居中显示。 */
  isFocusOnSatelliteWhenSelected = true;
  isUseJdayOnTopMenu = true;
  /** 确定是否在启动画面显示加载提示的标志 */
  isShowLoadingHints = true;
  /**
   * 确定是否禁用底部菜单的标志。除非您已禁用使用底部菜单的插件，否则不要启用此选项。
   */
  isDisableBottomMenu = false;
  /** 初始时间偏移 */
  staticOffset: number = 0; // 以秒为单位
  isDrawNightAsDay = false;
  isEmbedMode = false;


  static preserveSettings() {
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_CAMERA_WIDGET, settingsManager.drawCameraWidget.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_ORBITS, settingsManager.isDrawOrbits.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_TRAILING_ORBITS, settingsManager.isDrawTrailingOrbits.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_ECF, settingsManager.isOrbitCruncherInEcf.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_IN_COVERAGE_LINES, settingsManager.isDrawInCoverageLines.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_SUN, settingsManager.isDrawSun.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_COVARIANCE_ELLIPSOID, settingsManager.isDrawCovarianceEllipsoid.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_BLACK_EARTH, settingsManager.isBlackEarth.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_ATMOSPHERE, settingsManager.isDrawAtmosphere.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_AURORA, settingsManager.isDrawAurora.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DRAW_MILKY_WAY, settingsManager.isDrawMilkyWay.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_GRAY_SKYBOX, settingsManager.isGraySkybox.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_ECI_ON_HOVER, settingsManager.isEciOnHover.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_HOS, settingsManager.colors.transparent[3] === 0 ? 'true' : 'false');
    if (settingsManager.isShowConfidenceLevels) {
      PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_CONFIDENCE_LEVELS, settingsManager.isShowConfidenceLevels.toString());
    } else {
      PersistenceManager.getInstance().removeItem(StorageKey.SETTINGS_CONFIDENCE_LEVELS);
    }
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DEMO_MODE, settingsManager.isDemoModeOn.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_SAT_LABEL_MODE, settingsManager.isSatLabelModeOn.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_FREEZE_PROP_RATE_ON_DRAG, settingsManager.isFreezePropRateOnDrag.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DISABLE_TIME_MACHINE_TOASTS, settingsManager.isDisableTimeMachineToasts.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_SEARCH_LIMIT, settingsManager.searchLimit.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_SAMPLES, settingsManager.godraysSamples.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_DECAY, settingsManager.godraysDecay.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_EXPOSURE, settingsManager.godraysExposure.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_DENSITY, settingsManager.godraysDensity.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_WEIGHT, settingsManager.godraysWeight.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_GODRAYS_ILLUMINATION_DECAY, settingsManager.godraysIlluminationDecay.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_EARTH_DAY_RESOLUTION, settingsManager.earthDayTextureQuality?.toString());
    PersistenceManager.getInstance().saveItem(StorageKey.GRAPHICS_SETTINGS_EARTH_NIGHT_RESOLUTION, settingsManager.earthNightTextureQuality?.toString());

    keepTrackApi.emit(KeepTrackApiEvents.saveSettings);
  }

  colors: ColorSchemeColorMap & ObjectTypeColorSchemeColorMap;

  /**
   * 应用程序加载时使用的默认配色方案。这必须是与可用配色方案之一的类名匹配的字符串。
   * 例如：DefaultColorScheme、CelestrakColorScheme等。
   */
  defaultColorScheme = 'CelestrakColorScheme';

  /** 确保没有html被注入到页面中 */
  isPreventDefaultHtml = false;
  /**
   * 时间机器模式中前进前的延迟
   */
  timeMachineDelay = <Milliseconds>5000;
  /**
   * 时间机器模式在当前日期的延迟
   */
  timeMachineDelayAtPresentDay = <Milliseconds>20000;
  /**
   * 地图宽度的初始分辨率以提高性能
   */
  mapWidth = 800;
  /**
   * 地图高度的初始分辨率以提高性能
   */
  mapHeight = 600;
  /**
   * 加载用户最后使用的传感器的标志
   */
  isLoadLastSensor = true;
  /**
   * 禁用主用户界面。目前是全有或全无的包。
   */
  disableUI = false;
  isMobileModeEnabled = false;
  /**
   * 立体地图最后一次更新的时间。
   *
   * TODO: 这不应该属于设置管理器。
   */
  lastMapUpdateTime = 0;
  hiResWidth: number | null = null;
  hiResHeight: number | null = null;
  screenshotMode = null;
  lastBoxUpdateTime = null;
  /**
   * FPS模式、天象馆模式、天文模式和卫星视图的初始视野设置
   */
  fieldOfView = 0.6;
  db = null;
  /**
   * 捕获错误并通过github报告
   */
  isGlobalErrorTrapOn = true;
  /**
   * 确定是否应显示启动画面图像。
   * 文本和版本号仍会显示。
   */
  isShowSplashScreen = true;
  /**
   * 确定是否在应用程序中显示概念碎片。这是为演示目的而设计的。
   */
  isNotionalDebris = false;
  isFreezePropRateOnDrag = false;
  /**
   * 禁用可选的ASCII目录（仅适用于离线模式）
   *
   * /tle/TLE.txt
   */
  isDisableAsciiCatalog = true;
  settingsManager = null;
  /**
   * 指示是否应在地球上显示有效载荷所有者/制造商。
   *
   * TODO: 这需要改进。大多数机构都没有链接到任何卫星！
   */
  isShowAgencies = false;
  /**
   * 确定是否在应用程序中显示地球同步轨道卫星。
   */
  isShowGeoSats = true;
  /**
   * 确定是否在应用程序中显示高椭圆轨道卫星。
   */
  isShowHeoSats = true;
  /**
   * 确定是否在应用程序中显示中地球轨道卫星。
   */
  isShowMeoSats = true;
  /**
   * 确定是否在应用程序中显示低地球轨道卫星。
   */
  isShowLeoSats = true;
  /**
   * 确定是否在应用程序中显示概念卫星。
   * 概念卫星是尚未发射的卫星。
   */
  isShowNotionalSats = true;
  /**
   * 确定是否在应用程序中显示星链卫星。
   */
  isShowStarlinkSats = true;
  /**
   * 确定是否应显示有效载荷。
   */
  isShowPayloads = true;
  /**
   * 确定是否显示火箭体。
   */
  isShowRocketBodies = true;
  /**
   * 确定是否显示碎片。
   */
  isShowDebris = true;
  /**
   * @deprecated
   * 选择"所有"卫星时显示的最大轨道数
   */
  maxOribtsDisplayedDesktopAll = 10000;
  /**
   * 选择一组卫星时的透明度
   */
  orbitGroupAlpha = 0.5;
  loopTimeMachine = false;
  isDisableSelectSat: boolean | null = null;
  timeMachineLongToast = false;
  lastInteractionTime = 0;
  /**
   * 禁用JSON目录（仅适用于离线模式）
   *
   * /tle/extra.json
   */
  isDisableExtraCatalog = true;
  /**
   * 绘制轨道时要绘制的线条数
   *
   * 更大的数字会使轨道更平滑，但会更消耗资源
   * 调整为256以平衡轨道渲染精度和性能
   */
  orbitSegments = 256;
  /**
   * 最后一次手柄移动的时间戳。
   */
  lastGamepadMovement = 0;
  /**
   * 指示手柄控制是否受限。
   */
  isLimitedGamepadControls = false;
  /**
   * 切换多个预设以与EPFL（洛桑联邦理工学院）一起使用。
   *
   * 注意：这对其他机构或演示也可能有用。
   */
  isEPFL = false;
  isDisableUrlBar = null;
  /**
   * 添加自定义网格列表以强制加载特定网格
   *
   * 然后可以在网格管理器中使用这些来强制使用特定网格
   */
  meshListOverride: string[] = [];
  isDebrisOnly = false;
  isDisableCss = null;
  /**
   * 允许右键菜单
   */
  isAllowRightClick = true;
  /**
   * 设置加载时调用的回调函数。
   */
  // eslint-disable-next-line no-empty-function
  onLoadCb = () => { };
  /**
   * 在时间机器期间禁用提示
   */
  isDisableTimeMachineToasts = false;
  isDrawConstellationBoundaries = null;
  isDrawNasaConstellations = null;
  /**
   * 确定是否在应用程序中绘制太阳。
   */
  isDrawSun = true;
  /**
   * 确定用于太阳照明的绘制命令数量
   * 这应该是一个GodraySamples值（16、32、64、128）
   * @default 16
   */
  godraysSamples = 8; // 降低采样数以提高性能
  /**
   * 神光效果的衰减因子。
   *
   * 此值控制神光强度随距离减少的速度。
   * 较低的值会产生较短/不太明显的神光，而较高的值
   * 会创建更长/更突出的神光。
   *
   * @default 0.983
   */
  godraysDecay = 0.983;
  /**
   * 神光效果的曝光级别。
   * 控制神光渲染的亮度/强度。
   * 较高的值使神光更加明显。
   * @default 0.75.
   */
  godraysExposure = 0.75;
  /**
   * 神光效果的密度。
   * 控制光散射效果的强度和厚度。
   * 较高的值会产生更明显的神光。
   * @default 1.8
   */
  godraysDensity = 1.8;
  /**
   * 神光效果的权重因子。
   * 控制神光/光散射效果的强度。
   * 较高的值增加神光的可见性。
   * @default 0.085
   */
  godraysWeight = 0.085;
  /**
   * 表示神光（体积光散射）强度随距离光源的距离而减弱的速率。
   *
   * 较高的值使神光在远离光源时更快地消失。
   * 较低的值允许神光延伸得更远，强度减少较少。
   *
   * @default 2.7
   */
  godraysIlluminationDecay = 2.7;
  /**
   * 模拟中太阳的大小，表示为比例因子。
   * 值为0.9表示太阳以其默认大小的90%显示。
   * 值为1.1表示太阳以其默认大小的110%显示。
   */
  sizeOfSun = 1.1;
  /**
   * 确定是否使用太阳纹理。
   * 设置为true时，应用程序将使用自定义纹理渲染太阳。
   * 设置为false时，应用程序将使用默认的太阳表示。
   * @default false
   */
  isUseSunTexture = false;
  /**
   * 当传感器在视野范围内时，绘制从传感器到卫星的线条
   */
  isDrawInCoverageLines = true;
  /**
   * 确定是否绘制轨道。
   */
  isDrawOrbits = true;
  /**
   * 在对象悬停时显示ECI坐标
   */
  isEciOnHover = false;
  /**
   * 确定是否应在屏幕上绘制银河系。
   */
  isDrawMilkyWay = true;
  /**
   * 确定画布的背景应该是灰色还是黑色。
   *
   * 注意：这仅在不绘制银河系时使用。
   */
  isGraySkybox = false;
  /**
   * 确定用户是否正在拖拽地球的全局标志
   */
  isDragging = false;
  /**
   * 在ECF与ECI中显示地球同步轨道
   * 默认设置为false以避免轨道显示异常
   */
  isOrbitCruncherInEcf = false;
  lastSearch: string | string[] = '';
  isGroupOverlayDisabled: boolean | null = null;
  /**
   * 切换到近距离摄像模式时与卫星的距离。
   * 这用于在放大卫星时减缓推拉效果。
   * 调整为2km，让用户需要缩放到很近的距离才进入放大卫星模式。
   */
  nearZoomLevel = 2 as Kilometers;
  isPreventColorboxClose = false;
  isDayNightToggle = false;
  isUseHigherFOVonMobile = null;
  lostSatStr = '';
  maxOribtsDisplayed = 10000;
  isOrbitOverlayVisible = false;
  isShowSatNameNotOrbit = null;
  /**
   * 确定悬停在对象上时是否显示下次过境时间。
   *
   * 这是处理密集型的，应在低端设备上禁用
   */
  isShowNextPass = false;
  dotsOnScreen = 0;
  versionDate = '';
  versionNumber = '';
  /**
   * 用户的地理位置数据。
   */
  geolocation: SensorGeolocation = {
    lat: null,
    lon: null,
    alt: null,
    minaz: null,
    maxaz: null,
    minel: null,
    maxel: null,
    minrange: null,
    maxrange: null,
  };

  altMsgNum = null;
  altLoadMsgs = false;
  /**
   * 调整以改变围绕地球自动平移的相机速度
   */
  autoPanSpeed = 1;

  /**
   * 调整以改变围绕地球自动旋转的相机速度
   */
  autoRotateSpeed = 0.000075;
  /**
   * 相机衰减的速度。
   *
   * 减少此值可为相机变化提供动量
   */
  cameraDecayFactor = 5;
  /**
   * 相机移动的速度。
   *
   * TODO: 这需要设为只读，并且应该使用单独的内部相机变量来处理
   * 按下shift键时的逻辑
   */
  cameraMovementSpeed = 0.003;
  /**
   * 相机移动的最小速度。
   *
   * TODO: 这需要设为只读，并且应该使用单独的内部相机变量来处理
   * 按下shift键时的逻辑
   */
  cameraMovementSpeedMin = 0.005;
  /**
   * 卫星视锥与地球的距离。
*
* 此值用于防止视锥嵌入地球。
*
* 您可以调整此值，使视锥看起来距离地球更近或更远。
*
* 负值将导致视锥嵌入地球，但在某些情况下这可能是理想的。
   */
  coneDistanceFromEarth = 0 as Kilometers;
  /**
   * 用于在截图和地图上禁用版权文本。
   */
  copyrightOveride = false;
  /**
   * 确定处理器加载是否完成的全局标志
   */
  cruncherReady = false;
  /**
   * 要显示的当前图例。
   */
  currentLegend = 'default';
  /**
   * TLE被认为丢失前的天数。
   */
  daysUntilObjectLost = 60;
  /**
   * 演示模式中每个卫星之间的毫秒数。
   */
  demoModeInterval = <Milliseconds>1000;
  /**
   * 在桌面设备上显示的最大卫星标签数。
   */
  desktopMaxLabels = 1000;
  /**
   * 桌面视图的最小宽度（像素）。
   */
  desktopMinimumWidth = 1300;
  /**
   * 目前仅禁用平移。
   *
   * TODO: 禁用所有相机移动
   */
  disableCameraControls = false;
  /**
   * 禁用正常的浏览器右键菜单
   */
  disableDefaultContextMenu = true;
  /**
   * 禁用来自键盘/鼠标的正常浏览器事件
   */
  disableNormalEvents = false;
  /**
   * 禁用窗口对象滚动
   */
  disableWindowScroll = true;
  /**
   * 禁用触摸移动
   *
   * 注意：在桌面上会导致拖拽错误
   */
  disableWindowTouchMove = true;
  /**
   * 禁用缩放键盘按键
   */
  disableZoomControls = true;
  /**
   * 用于渲染地球对象的纬度段数。
   */
  earthNumLatSegs = 128;
  /**
   * 用于渲染地球的经度段数。
   */
  earthNumLonSegs = 128;
  /**
   * 在每次绘制时更新选定卫星的轨道。
   *
   * 性能损失，但使卫星的运行方向更清晰
   */
  enableConstantSelectedSatRedraw = true;
  /**
   * 高亮显示时显示对象的轨道
   */
  enableHoverOrbits = true;
  /**
   * 显示带有对象信息的覆盖层
   */
  enableHoverOverlay = true;
  /**
   * 指示是否启用后备css。这仅在isDisableCss为true时加载。
   */
  enableLimitedUI = true;
  /**
   * @deprecated
   * 视野设置的最大值。
   *
   * TODO: 为FPS、天象馆、天文和卫星视图实现此功能
   */
  fieldOfViewMax = 1.2;
  /**
   * @deprecated
   * 视野设置的最小值。
   *
   * TODO: 为FPS、天象馆、天文和卫星视图实现此功能
   */
  fieldOfViewMin = 0.04;
  /**
   * 在初始轨道插件中拟合TLE的步数
   */
  fitTleSteps = 3; // 增加此值会严重影响性能
  /**
   * FPS模式下相机在Z方向移动的速度。
   */
  fpsForwardSpeed = 3;
  /**
   * FPS模式下相机上下俯仰的速度。
   */
  fpsPitchRate = 0.02;
  /**
   * FPS模式下相机旋转的速度。
   */
  fpsRotateRate = 0.02;
  /**
   * FPS模式下相机在X方向移动的速度。
   */
  fpsSideSpeed = 3;
  /**
   * 最小fps或跳过太阳/月亮
   */
  fpsThrottle1 = 0;
  /**
   * 最小fps或忽略卫星速度
   */
  fpsThrottle2 = 10;
  /**
   * FPS模式下相机在Y方向移动的速度。
   */
  fpsVertSpeed = 3;
  /**
   * FPS模式下相机扭转（偏航）的速度。
   */
  fpsYawRate = 0.02;
  /**
   * 确定是否正在使用地理位置的全局标志
   */
  geolocationUsed = false;
  /**
   * Minimum elevation to for calculating DOPs in dop plugin
   */
  gpsElevationMask = <Degrees>15;
  /**
   * Color of the dot when hovering over an object.
   */
  hoverColor = <[number, number, number, number]>[1.0, 1.0, 0.0, 1.0]; // Yellow
  /**
   * The relative path to the installation directory. This is necessary if the application is
   * a folder inside the main folder of the webserver.
   */
  dataSources = {
    /**
     * This is where the TLEs are loaded from
     *
     * It was previously: ${settingsManager.installDirectory}tle/TLE2.json`
     *
     * It can be loaded from a local file or a remote source
     */
    tle: 'http://www.space-data.cn/data/tle.json',
    /** url for an external TLE source */
    externalTLEs: '',
    /**
     * A boolean flag indicating whether only external TLEs (Two-Line Elements) should be used.
     * When set to `true`, the system will exclusively utilize external TLE data.
     * When set to `false`, the system may use internal or other sources of TLE data.
     */
    externalTLEsOnly: false,
    tleDebris: 'https://spacedefense.cn/tle/TLEdebris.json',
    vimpel: 'https://spacedefense.cn/v3/r2/vimpel.json',
    /** This determines if tle source is loaded to supplement externalTLEs  */
    isSupplementExternal: false,
  };
  telemetryServer = 'https://spacedefense.cn';
  /**
   * Determines whether or not to hide the propogation rate text on the GUI.
   */
  isAlwaysHidePropRate = false;
  /**
   * Determines whether the canvas should automatically resize when the window is resized.
   */
  isAutoResizeCanvas = true;
  /**
   * If true, hide the earth textures and make the globe black
   */
  isBlackEarth = false;
  /**
   * Determines whether or not to load the specularity map for the Earth.
   */
  isDrawSpecMap = false; // 关闭镜面反射贴图以提高性能
  /**
   * Determines whether or not to load the bump map for the Earth.
   */
  isDrawBumpMap = false; // 关闭凹凸贴图以提高性能
  /**
   * Determines whether the atmosphere should be drawn or not.
   * 0 = No atmosphere
   * 1 = Thin white atmosphere
   * 2 = Colored atmosphere
   */
  isDrawAtmosphere: AtmosphereSettings = AtmosphereSettings.COLORFUL;
  /**
   * Determines whether or not to draw the Aurora effect.
   */
  isDrawAurora = true;
  /**
   * Determines whether or not to run the demo mode.
   */
  isDemoModeOn = false;
  /**
   * Disables the loading of control site data
   */
  isDisableControlSites = true;
  /**
   * Disables the loading of launch site data
   */
  isDisableLaunchSites = false;
  /**
   * Disables the loading of sensor data
   */
  isDisableSensors = false;
  /**
   * Determines whether the application should use a reduced-draw mode.
   * If true, the application will use a less resource-intensive method of rendering.
   * If false, the application will use the default rendering method.
   */
  isDrawLess = false;
  /**
   * Determines whether the last map that was loaded should be loaded again on the next session.
   */
  isLoadLastMap = true;
  /**
   * Global flag for determining if the application is resizing
   */
  isResizing = false;
  /**
   * Determines whether or not to show the satellite labels.
   */
  isSatLabelModeOn = true;
  /**
   * Flag for showing the primary logo
   */
  isShowPrimaryLogo = true;
  /**
   * Flag for showing the secondary logo for partnerships
   */
  isShowSecondaryLogo = false;
  /**
   * Flag for using the debris catalog instead of the full catalog
   *
   * /tle/TLEdebris.json
   */
  isUseDebrisCatalog = false;
  /**
   * Determines whether zooming stops auto rotation in the application.
   */
  isZoomStopsRotation = true;
  /**
   * Changing the zoom with the mouse wheel will stop the camera from following the satellite.
   */
  isZoomStopsSnappedOnSat = false;
  /**
   * List of the last search results
   */
  lastSearchResults: number[] = [];
  /**
   * String to limit which satellites are loaded from the catalog
   */
  limitSats = '';
  /**
   * Minimum elevation to draw a line scan
   */
  lineScanMinEl = 5;
  /**
   * The speed at which the scan lines for radars move across the screen
   *
   * About 30 seconds to scan earth (arbitrary)
   *
   * (each draw will be +speed lat/lon)
   */
  lineScanSpeedRadar = 0.25;
  /**
   * The speed at which the scan lines for radars move across the screen
   *
   * About 6 seconds to scan earth (no source, just a guess)
   *
   * (each draw will be +speed lat/lon)
   */
  lineScanSpeedSat = 6;
  lkVerify = 0;
  lowPerf = false;
  /**
   * Preallocate the maximum number of analyst satellites that can be manipulated
   *
   * NOTE: This mainly applies to breakup scenarios
   */
  maxAnalystSats = 10000;
  /**
   * Preallocate the maximum number of field of view marker dots that can be displayed
   */
  maxFieldOfViewMarkers = 1;
  /**
   * Preallocate the maximum number of labels that can be displayed
   *
   * Set mobileMaxLabels and desktopMaxLabels instead of this directly
   */
  maxLabels = 0; // 20000;
  /**
   * Preallocate the maximum number of missiles that can be displayed
   *
   * NOTE: New attack scenarios are limited to this number
   */
  maxMissiles = 500;
  /**
   * The maximum number of orbits to display on mobile devices.
   */
  maxOrbitsDisplayedMobile = 1500;
  /**
   * The maximum number of orbits to be displayed on desktop.
   */
  maxOribtsDisplayedDesktop = 100000;
  /**
   * 距离地球表面的最大缩放距离（公里）
   *
   * 用于默认和偏移相机模式的缩放控制
   */
  maxZoomDistance = <Kilometers>300000;
  /**
   * Which mesh to use if meshOverride is set
   */
  meshOverride = null;
  /**
   * The rotation of the mesh if meshOverride is set
   */
  meshRotation = {
    x: 0,
    y: 0,
    z: 0,
  };

  /**
   * Minimum time between draw calls in milliseconds
   *
   * 20 FPS = 50ms
   * 30 FPS = 33.33ms
   * 60 FPS = 16.67ms
   */
  minimumDrawDt = <Milliseconds>0.0;
  /**
   * 搜索前需输入的最少字符数
   */
  minimumSearchCharacters = 2; // Searches after 3 characters typed
  /**
   * 距离原点(0,0,0)的最小缩放距离（公里）
   *
   * 用于默认和偏移相机模式的缩放控制
   */
  minZoomDistance = <Kilometers>(RADIUS_OF_EARTH + 20);
  /**
   * Maximum number of satellite labels to display on mobile devices
   */
  mobileMaxLabels = 100;
  /**
   * Override the default models on satellite view
   */
  modelsOnSatelliteViewOverride = false;
  /**
   * Name of satellite category for objects not in the official catalog.
   */
  nameOfSpecialSats = 'SpecialSats';
  /**
   * 确定视角时要考虑的遍数
   */
  nextNPassesCount = 5;
  noMeshManager = false;
  /**
   * TODO: Reimplement stars
   */
  isDisableStars = true;
  /** Flag to determine if external data is available */
  offline = true;
  /**
   * The offset in the x direction for the offset camera mode.
   */
  offsetCameraModeX = 100000;
  /**
   * The offset in the z direction for the offset camera mode.
   */
  offsetCameraModeZ = -6000;
  /**
   * How much an orbit fades over time
   *
   * 0.0 = Not Visible
   *
   * 1.0 = No Fade
   */
  orbitFadeFactor = 0.6;
  /**
   * Color of orbits when a group of satellites is selected.
   */
  orbitGroupColor = <[number, number, number, number]>[1.0, 1.0, 0.0, 0.7];
  /**
   * Color of orbit when hovering over an object.
   */
  orbitHoverColor = <[number, number, number, number]>[1.0, 1.0, 0.0, 0.9];
  /**
   * Color of orbit when in view.
   */
  orbitInViewColor = <[number, number, number, number]>[1.0, 1.0, 1.0, 0.7]; // WHITE
  /**
   * Color of orbit when in Planetarium View.
   */
  orbitPlanetariumColor = <[number, number, number, number]>[1.0, 1.0, 1.0, 0.2]; // Transparent White
  /**
   * Color of orbit when selected.
   */
  orbitSelectColor = <[number, number, number, number]>[1.0, 0.0, 0.0, 0.9];
  /**
   * Color of secondary object orbit.
   */
  orbitSelectColor2 = <[number, number, number, number]>[0.0, 0.4, 1.0, 0.9];
  pTime = [];
  /**
   * Global flag for determining if a screenshot is queued
   */
  queuedScreenshot = false;
  retro = false;
  /**
   * Minimum time between new satellite labels in milliseconds
   */
  minTimeBetweenSatLabels = <Milliseconds>100;
  /**
   * The settings for the satellite shader.
   */
  satShader = {
    /**
     * The minimum zoom level at which large objects are displayed.
     */
    largeObjectMinZoom: 0.37,
    /**
     * The maximum zoom level at which large objects are displayed.
     */
    largeObjectMaxZoom: 0.58,
    /**
     * The minimum size of objects in the shader.
     */
    minSize: 5.5,
    /**
     * The minimum size of objects in the shader when in planetarium mode.
     */
    minSizePlanetarium: 20.0,
    /**
     * The maximum size of objects in the shader when in planetarium mode.
     */
    maxSizePlanetarium: 20.0,
    /**
     * The maximum allowed size of objects in the shader.
     * This value is dynamically changed based on zoom level.
     */
    maxAllowedSize: 35.0,
    /**
     * Whether or not to use dynamic sizing for objects in the shader.
     */
    isUseDynamicSizing: false,
    /**
     * The scalar value used for dynamic sizing of objects in the shader.
     */
    dynamicSizeScalar: 1.0,
    /**
     * The size of stars and searched objects in the shader.
     */
    starSize: '20.0',
    /**
     * The distance at which objects start to grow in kilometers.
     * Must be a float as a string for the GPU to read.
     * This makes stars bigger than satellites.
     */
    distanceBeforeGrow: '14000.0',
    /**
     * The blur radius factor used for satellites.
     */
    blurFactor1: '0.53',
    /**
     * The blur alpha factor used for satellites.
     */
    blurFactor2: '0.5',
    /**
     * The blur radius factor used for stars.
     */
    blurFactor3: '0.43',
    /**
     * The blur alpha factor used for stars.
     */
    blurFactor4: '0.25',
    /**
     * The maximum size of objects in the shader.
     */
    maxSize: 70.0,
  };

  /**
   * The maximum number of satellites to display when searching.
   */
  searchLimit = 600;
  /**
   * Color of the dot when selected.
   */
  selectedColor = <[number, number, number, number]>[1.0, 0.0, 0.0, 1.0]; // Red
  /**
   * Determines whether the orbit should be shown through the Earth or not.
   */
  showOrbitThroughEarth = false;
  /**
   * Determines whether small images should be used.
   *
   * Use these to default smallest resolution maps
   * Really useful on small screens and for faster loading times
   *
   * 强制设为false以确保使用高清纹理
   */
  smallImages = false;
  /**
   * Allows canvas will steal focus on load
   */
  startWithFocus = false;
  /**
   * Automatically display all of the orbits
   */
  startWithOrbitsDisplayed = false;
  /**
   * How many draw calls to wait before updating orbit overlay if last draw time was greater than 50ms
   */
  updateHoverDelayLimitBig = 5;
  /**
   * How many draw calls to wait before updating orbit overlay if last draw time was greater than 20ms
   */
  updateHoverDelayLimitSmall = 3;
  /**
   * Size of the dot vertex shader
   */
  vertShadersSize = 12;
  /**
   * The desired video bitrate in bits per second for video recording.
   *
   * This value is set to 30,000,000 bits per second (or 10.0 Mbps) by default.
   */
  videoBitsPerSecond = 30000000;
  /**
   * The maximum z-depth for the WebGL renderer.
   *
   * Increasing this causes z-fighting
   * Decreasing this causes clipping of stars and satellites
   */
  zFar = 450000.0;
  /**
   * The minimum z-depth for the WebGL renderer.
   */
  zNear = 1.0;
  /**
   * 用户放大或缩小时缩放级别变化的速度。
   */
  zoomSpeed = 0.0050;
  /**
   * Draw Trailing Orbits
   */
  isDrawTrailingOrbits = false;
  /**
   * Enables the old extended catalog including JSC Vimpel data
   * @deprecated Use isEnableJscCatalog instead
   */
  isEnableExtendedCatalog = false;
  selectedColorFallback = <[number, number, number, number]>[0, 0, 0, 0];
  /**
   * Flag if the keyboard should be disabled
   */
  isDisableKeyboard = false;
  /**
   * Flag for if the user is running inside an iframe
   */
  isInIframe = false;
  isAutoRotateL = true;
  isAutoRotateR = false;
  isAutoRotateU = false;
  isAutoRotateD = false;
  isAutoPanL = false;
  isAutoPanR = false;
  isAutoPanU = false;
  isAutoPanD = false;
  isAutoZoomIn = false;
  isAutoZoomOut = false;
  autoZoomSpeed = 0.00002;
  maxNotionalDebris = 100000;
  /**
   * This is an override for how many dot colors are calculated per draw loop.
   * Higher numbers will make the dots more accurate, but will slow down the simulation.
   */
  dotsPerColor: number;
  /**
   * Minimum distance from satellite when we switch to close camera mode
   * The camera will not be able to get closer than this distance
   */
  minDistanceFromSatellite = 1.25 as Kilometers;

  /**
   * Disable toast messages
   */
  isDisableToasts = false;
  /*
   * Enables the new JSC Vimpel catalog
   */
  isEnableJscCatalog = true;
  /**
   * Size of the dot for picking purposes
   */
  pickingDotSize: string = '18.0';

  /**
   * Disable drawing godrays (huge performance hit on mobile)
   */
  isDisableGodrays = false;
  isDisableSkybox = false;
  isDisableMoon = false;
  isDisableSearchBox = false;
  isDisableAsyncReadPixels = false;
  /**
   * Use 16K textures for the Milky Way
   */
  hiresMilkWay = false;
  /**
   * When set to true, only load satellites with the name "Starlink"
   */
  isStarlinkOnly = false;
  /**
   * Indicates whether to show confidence levels when hovering over an object.
   */
  isShowConfidenceLevels: boolean = false;
  /**
   * The container root element for the application
   * NOTE: This is for initializing it, but keepTrackApi.containerRoot will be used throughout
   * the application when looking for the container root element
   */
  containerRoot: HTMLDivElement;
  /**
   * The initial zoom level for the camera.
   * 0 = earth and 1 = max distance from earth
   */
  initZoomLevel: number;
  positionCruncher: Worker | null = null;
  orbitCruncher: Worker | null = null;
  /** Enables the camera widget */
  drawCameraWidget = false;

  loadPersistedSettings() {
    this.isDrawOrbits = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_ORBITS, this.isDrawOrbits) as boolean;
    this.drawCameraWidget = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_CAMERA_WIDGET, this.drawCameraWidget) as boolean;
    this.isDrawTrailingOrbits = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_TRAILING_ORBITS, this.isDrawTrailingOrbits) as boolean;
    this.isOrbitCruncherInEcf = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_ECF, this.isOrbitCruncherInEcf) as boolean;
    this.isDrawInCoverageLines = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_IN_COVERAGE_LINES, this.isDrawInCoverageLines) as boolean;
    this.isDrawSun = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_SUN, this.isDrawSun) as boolean;
    this.isDrawCovarianceEllipsoid = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_COVARIANCE_ELLIPSOID, this.isDrawCovarianceEllipsoid) as boolean;
    this.isBlackEarth = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_BLACK_EARTH, this.isBlackEarth) as boolean;
    this.isDrawAtmosphere = parseInt(PersistenceManager.getInstance().getItem(StorageKey.SETTINGS_DRAW_ATMOSPHERE) ?? '0') as AtmosphereSettings;
    this.isDrawAurora = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_AURORA, this.isDrawAurora) as boolean;
    this.isDrawMilkyWay = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DRAW_MILKY_WAY, this.isDrawMilkyWay) as boolean;
    this.isGraySkybox = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_GRAY_SKYBOX, this.isGraySkybox) as boolean;
    this.isEciOnHover = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_ECI_ON_HOVER, this.isEciOnHover) as boolean;
    if (settingsManager.isShowConfidenceLevels) {
      this.isShowConfidenceLevels = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_CONFIDENCE_LEVELS, this.isShowConfidenceLevels) as boolean;
    } else {
      this.isShowConfidenceLevels = false;
    }
    this.isDemoModeOn = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DEMO_MODE, this.isDemoModeOn) as boolean;
    this.isSatLabelModeOn = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_SAT_LABEL_MODE, this.isSatLabelModeOn) as boolean;
    this.isFreezePropRateOnDrag = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_FREEZE_PROP_RATE_ON_DRAG, this.isFreezePropRateOnDrag) as boolean;
    this.isDisableTimeMachineToasts = PersistenceManager.getInstance().checkIfEnabled(StorageKey.SETTINGS_DISABLE_TIME_MACHINE_TOASTS, this.isDisableTimeMachineToasts) as boolean;

    const earthDayTextureQaulityString = PersistenceManager.getInstance().getItem(StorageKey.GRAPHICS_SETTINGS_EARTH_DAY_RESOLUTION);

    if (earthDayTextureQaulityString !== null) {
      this.earthDayTextureQuality = earthDayTextureQaulityString as EarthDayTextureQuality;
    }

    const earthNightTextureQaulityString = PersistenceManager.getInstance().getItem(StorageKey.GRAPHICS_SETTINGS_EARTH_NIGHT_RESOLUTION);

    if (earthNightTextureQaulityString !== null) {
      this.earthNightTextureQuality = earthNightTextureQaulityString as EarthNightTextureQuality;
    }

    const searchLimitString = PersistenceManager.getInstance().getItem(StorageKey.SETTINGS_SEARCH_LIMIT);

    if (searchLimitString !== null) {
      this.searchLimit = parseInt(searchLimitString);
    }
  }

  init(settingsOverride?: SettingsManagerOverride) {
    /*
     * Export settingsManager to everyone else
     * window.settingsManager = this;
     * Expose these to node if running in node
     */
    if (global) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (<any>global).settingsManager = this;
    }

    this.pTime = [];

    this.checkIfIframe_();
    this.setInstallDirectory_();
    MobileManager.checkMobileMode();
    this.setEmbedOverrides_();
    this.setColorSettings_();

    const params = this.loadOverridesFromUrl_();

    if (settingsOverride) {
      this.loadOverrides_(settingsOverride);
    }

    if (!this.disableUI) {
      parseGetVariables(params, this);
    }
    settingsManager.isBlockPersistence = UrlManager.parseGetVariables(this) || settingsManager.isBlockPersistence;

    if (!settingsManager.isBlockPersistence) {
      /**
       * Load Order:
       * URL Params > Local Storage > Default
       */
      this.loadPersistedSettings();

    }

    // If No UI Reduce Overhead
    if (this.disableUI) {
      // LEAVE AT LEAST ONE TO PREVENT ERRORS
      this.maxFieldOfViewMarkers = 1;
      this.maxMissiles = 1;
      this.maxAnalystSats = 1;
    }

    // Disable resource intense plugins if lowPerf is enabled
    if (this.lowPerf) {
      if (this.plugins.SensorFov) {
        this.plugins.SensorFov.enabled = false;
      }
      if (this.plugins.SensorSurvFence) {
        this.plugins.SensorSurvFence.enabled = false;
      }
      if (this.plugins.SatelliteFov) {
        this.plugins.SatelliteFov.enabled = false;
      }
      this.maxFieldOfViewMarkers = 1;
    }

    this.loadLastMapTexture_();
  }

  private checkIfIframe_() {
    if (window.self !== window.top) {
      this.isInIframe = true;
      this.isShowPrimaryLogo = true;
    }
  }

  /**
   * Sets the color settings for the application. If the colors are not found in local storage or the version is outdated,
   * default colors are used and saved to local storage.
   *
   * @private
   */
  private setColorSettings_() {
    this.selectedColorFallback = this.selectedColor;

    this.colors = {} as ColorSchemeColorMap & ObjectTypeColorSchemeColorMap;
    try {
      const jsonString = PersistenceManager.getInstance().getItem(StorageKey.SETTINGS_DOT_COLORS);

      if (jsonString) {
        this.colors = JSON.parse(jsonString);
      }
    } catch {
      // eslint-disable-next-line no-console
      console.warn('Settings Manager: Unable to get color settings - localStorage issue!');
    }
    if (!this.colors || Object.keys(this.colors).length === 0 || this.colors.version !== '1.4.6') {
      this.colors = defaultColorSettings;

      PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_DOT_COLORS, JSON.stringify(this.colors));
    }
  }

  /**
   * Loads overrides from the URL query string and applies them to the plugin settings.
   * @returns An array of query string parameters.
   */
  private loadOverridesFromUrl_() {
    const params = UrlManager.getParams();

    const plugins = this.plugins;

    for (const param of params) {
      const key = param.split('=')[0];
      const val = param.split('=')[1];

      if (key === 'settingsManagerOverride') {
        const overrides = JSON.parse(decodeURIComponent(val));

        Object.keys(overrides.plugins)
          .filter((_key) => _key in plugins)
          .forEach((_key) => {
            if (typeof overrides.plugins[_key] === 'undefined') {
              return;
            }
            this.plugins[_key] = overrides.plugins[_key];
          });
      }
    }

    return params;
  }

  disableAllPlugins() {
    Object.keys(this.plugins).forEach((key) => {
      this.plugins[key] = false;
    });
  }

  /**
   * Load the previously saved map texture.
   */
  private loadLastMapTexture_() {
    if (this.disableUI) {
      this.isLoadLastMap = false;
    }

    if (this.isLoadLastMap && !this.isDrawLess) {
      const lastMap = PersistenceManager.getInstance().getItem(StorageKey.LAST_MAP);

      if (lastMap) {
        settingsManager.earthTextureStyle = lastMap as EarthTextureStyle;
      }
    }
  }

  /**
   * Sets the embed overrides for the settings.
   * If the current page is an embed.html page, it sets various settings to specific values.
   *
   * FOR TESTING ONLY
   */
  private setEmbedOverrides_() {
    let pageName = location.href.split('/').slice(-1);

    pageName = pageName[0].split('?').slice(0);

    if (pageName[0] === 'embed.html') {
      this.disableUI = true;
      this.startWithOrbitsDisplayed = true;
      this.isAutoResizeCanvas = true;
      this.enableHoverOverlay = true;
      this.enableHoverOrbits = true;
      this.isDrawLess = true;
      this.smallImages = true;
      this.updateHoverDelayLimitSmall = 25;
      this.updateHoverDelayLimitBig = 45;
    }
  }

  exportSettingsToJSON() {
    const settings = {};

    for (const key of Object.keys(this)) {
      settings[key] = this[key];
    }

    // Save the settings to a file
    const settingsBlob = new Blob([JSON.stringify(settings)], { type: 'application/json' });
    const url = URL.createObjectURL(settingsBlob);
    const a = document.createElement('a');

    a.href = url;
    a.download = 'settings.json';

    a.click();
  }

  private loadOverrides_(settingsOverride: SettingsManagerOverride) {
    // combine settingsOverride with window.settingsOverride
    const overrides = { ...settingsOverride, ...window.settingsOverride };
    // override values in this with overrides

    for (const key of Object.keys(overrides)) {
      if (typeof overrides[key] === 'object' && overrides[key] !== null) {
        this[key] = { ...this[key], ...overrides[key] };
      } else {
        this[key] = overrides[key];
      }
    }
  }

  private setInstallDirectory_() {
    switch (window.location.host) {
      case 'dev.keeptrack.space':
      case 'www.dev.keeptrack.space':
      case 'keeptrack.space':
      case 'www.keeptrack.space':
        this.installDirectory = '/app/';
        break;
      case 'localhost':
      case '127.0.0.1':
        // Is node running? This must be some kind of test
        if (isThisNode()) {
          this.installDirectory = 'http://localhost:8080/';
        } else {
          /*
           * Comment Out the Next Two Lines if you are testing on a local server
           * and have the keeptrack files installed in a subdirectory
           */
          this.installDirectory = '/';
          // this.offline = true;
        }
        break;
      case 'darts.staging.dso.mil':
        this.installDirectory = '/keeptrack/';
        break;
      case 't':
      case 'www':
        this.installDirectory = '/keeptrack.space/';
        break;
      case '':
        this.offline = true;
        this.isDisableAsciiCatalog = false;
        this.installDirectory = './';
        break;
      case 'poderespacial.fac.mil.co':
        SettingsPresets.loadPresetFacSat2(this);
        break;
      default:
        this.installDirectory = '/';
        break;
    }
    if (typeof this.installDirectory === 'undefined') {
      // Put Your Custom Install Directory Here
      this.installDirectory = '/';
    }
  }

  /**
   * Placeholder for overrides
   */
  // eslint-disable-next-line no-empty-function
  timeMachineString(_yearStr: string): string | boolean {
    return false;
  }
}

// Create a type based on the parameters of SettingsManager (ignore methods)
export type SettingsManagerOverride = Partial<Omit<SettingsManager,
  'exportSettingsToJSON' | 'loadOverridesFromUrl_' | 'loadLastMapTexture_' | 'setEmbedOverrides_' | 'setMobileSettings_' | 'setInstallDirectory_' | 'setColorSettings_' |
  'checkIfIframe_' | 'initParseFromGETVariables_' | 'loadOverrides_' | 'setMobileSettings_' | 'setEmbedOverrides_' | 'loadLastMapTexture_' | 'setColorSettings_'>>;

// Export the settings manager instance

export const settingsManager = new SettingsManager();