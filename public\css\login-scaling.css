/* 🔥🔥🔥 登录界面缩放样式 */
/* 为登录界面提供响应式缩放支持 */

/* 检测系统缩放并设置CSS变量 */
:root {
  /* 默认缩放因子，将由JavaScript动态设置 */
  --system-scale-factor: 1;
  
  /* 🔥 登录界面专用字体大小变量 */
  --login-base-font: calc(22px / var(--system-scale-factor));
  --login-large-font: calc(28px / var(--system-scale-factor));
  --login-xlarge-font: calc(36px / var(--system-scale-factor));
  --login-button-height: calc(52px / var(--system-scale-factor));
  --login-input-height: calc(48px / var(--system-scale-factor));
  --login-spacing: calc(20px / var(--system-scale-factor));
}

/* 🔥 登录页面整体容器 */
body {
  font-size: var(--login-base-font) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
}

/* 🔥 登录容器 */
.container {
  font-size: var(--login-base-font) !important;
  padding: var(--login-spacing) !important;
  max-width: calc(600px / var(--system-scale-factor)) !important;
  margin: calc(50px / var(--system-scale-factor)) auto !important;
}

/* 🔥 标题样式 */
h1, h2, h3 {
  font-size: var(--login-xlarge-font) !important;
  line-height: calc(40px / var(--system-scale-factor)) !important;
  margin-bottom: var(--login-spacing) !important;
}

/* 🔥 表单组 */
.form-group {
  margin-bottom: var(--login-spacing) !important;
}

.form-group label {
  font-size: var(--login-base-font) !important;
  margin-bottom: calc(8px / var(--system-scale-factor)) !important;
  display: block;
}

/* 🔥 输入框样式 */
input[type="text"],
input[type="password"],
input[type="email"] {
  font-size: var(--login-base-font) !important;
  padding: calc(12px / var(--system-scale-factor)) !important;
  height: var(--login-input-height) !important;
  border-radius: calc(4px / var(--system-scale-factor)) !important;
  border-width: calc(1px / var(--system-scale-factor)) !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 🔥 按钮样式 */
.btn,
button {
  font-size: var(--login-base-font) !important;
  padding: calc(12px / var(--system-scale-factor)) calc(24px / var(--system-scale-factor)) !important;
  height: var(--login-button-height) !important;
  border-radius: calc(4px / var(--system-scale-factor)) !important;
  border: none !important;
  cursor: pointer !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 🔥 选项卡样式 */
.tabs {
  margin-bottom: var(--login-spacing) !important;
}

.tab {
  font-size: var(--login-base-font) !important;
  padding: calc(12px / var(--system-scale-factor)) calc(20px / var(--system-scale-factor)) !important;
  border-radius: calc(4px / var(--system-scale-factor)) calc(4px / var(--system-scale-factor)) 0 0 !important;
}

/* 🔥 Toast消息样式 */
.toast {
  font-size: var(--login-base-font) !important;
  padding: calc(12px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
  border-radius: calc(4px / var(--system-scale-factor)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor)) !important;
}

/* 🔥 密码强度指示器 */
.password-strength {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  margin-top: calc(8px / var(--system-scale-factor)) !important;
}

/* 🔥 锁定计时器 */
#lockoutTimer {
  font-size: var(--login-base-font) !important;
  padding: calc(12px / var(--system-scale-factor)) !important;
  margin-top: var(--login-spacing) !important;
  border-radius: calc(4px / var(--system-scale-factor)) !important;
}

/* 🔥 响应式调整 */
@media (min-width: 768px) {
  .container {
    max-width: calc(500px / var(--system-scale-factor)) !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: calc(600px / var(--system-scale-factor)) !important;
  }
}

/* 🔥 确保所有文本元素都应用缩放 */
* {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

p, span, div, a {
  font-size: var(--login-base-font) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
}

/* 🔥 链接样式 */
a {
  font-size: var(--login-base-font) !important;
  text-decoration: none !important;
}

a:hover {
  text-decoration: underline !important;
}
