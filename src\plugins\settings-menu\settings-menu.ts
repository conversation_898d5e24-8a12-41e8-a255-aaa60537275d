import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { ColorPick } from '@app/lib/color-pick';
import { getEl, hideEl } from '@app/lib/get-el';
import { parseRgba } from '@app/lib/rgba';
import { rgbCss } from '@app/lib/rgbCss';
import { SettingsManager } from '@app/settings/settings';
import { PersistenceManager, StorageKey } from '@app/singletons/persistence-manager';
import { LegendManager } from '@app/static/legend-manager';
import { OrbitCruncherType, OrbitDrawTypes } from '@app/webworker/orbitCruncher';
import settingsPng from '@public/img/icons/settings.png';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SoundNames } from '../sounds/SoundNames';
import { TimeMachine } from '../time-machine/time-machine';

/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * http://www.spacesecure.cn
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

declare module '@app/interfaces' {
  interface UserSettings {
    isBlackEarth: boolean;
    isDrawMilkyWay: boolean;
  }
}

export class SettingsMenuPlugin extends KeepTrackPlugin {
  readonly id = 'SettingsMenuPlugin';
  dependencies_ = [];

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.SETTINGS, MenuMode.ALL];

  bottomIconElementName: string = 'settings-menu-icon';
  bottomIconImg = settingsPng;
  sideMenuElementName: string = 'settings-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="settings-menu" class="side-menu-parent start-hidden text-select">
    <div id="settings-content" class="side-menu">
      <div class="row">
        <form id="settings-form">
          <div id="settings-general">
            <div class="row center"></div>
            </br>
            <div class="row center">
              <button id="settings-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">更新设置 &#9658;</button>
            </div>
            <div class="row center">
              <button id="settings-reset" class="btn btn-ui waves-effect waves-light" type="button" name="action">恢复默认 &#9658;</button>
            </div>
            <h5 class="center-align">通用设置</h5>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="悬停时，显示卫星信息">
                <input id="settings-enableHoverOverlay" type="checkbox" checked/>
                <span class="lever"></span>
                悬停时显示信息
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="当选择卫星后，放大到卫星目标上">
                <input id="settings-focusOnSatelliteWhenSelected" type="checkbox" checked/>
                <span class="lever"></span>
                选择后聚焦卫星
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="禁用此选项可隐藏轨道线">
                <input id="settings-drawOrbits" type="checkbox" checked/>
                <span class="lever"></span>
                绘制轨道
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="启用此功能可显示卫星的当前位置而不是其当前去向">
                <input id="settings-drawTrailingOrbits" type="checkbox"/>
                <span class="lever"></span>
                绘制尾随轨道
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="轨道将使用ECF与ECI绘制（主要用于GEO轨道）。注意：启用此选项可能导致某些轨道显示异常">
                <input id="settings-drawEcf" type="checkbox" />
                <span class="lever"></span>
                在ECF中绘制轨道（实验性）
              </label>
            </div>
            <div class="input-field col s12">
              <input id="settings-numberOfEcfOrbitsToDraw" type="number" min="1" max="10" value="1" />
              <label for="settings-numberOfEcfOrbitsToDraw" class="active">要绘制的ECF轨道数 (1-10)</label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="在视场内绘制从传感器到卫星的线">
                <input id="settings-isDrawCovarianceEllipsoid" type="checkbox" checked/>
                <span class="lever"></span>
                绘制协方差椭圆球
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="在视场内绘制从传感器到卫星的线">
                <input id="settings-isDrawInCoverageLines" type="checkbox" checked/>
                <span class="lever"></span>
                绘制视角内线
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="悬停时显示ECI坐标">
                <input id="settings-eciOnHover" type="checkbox"/>
                <span class="lever"></span>
                悬停时显示ECI
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="不可选择的卫星将被隐藏而不是变灰">
                <input id="settings-hos" type="checkbox" />
                <span class="lever"></span>
                隐藏其它卫星
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="禁用此功能可隐藏相机小部件">
                <input id="settings-drawCameraWidget" type="checkbox" checked/>
                <span class="lever"></span>
                显示相机插件
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="显示卫星元素集的可信度">
                <input id="settings-confidence-levels" type="checkbox" />
                <span class="lever"></span>
                显示可信级别
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="每3秒将从视角范围内中选择一颗新卫星">
                <input id="settings-demo-mode" type="checkbox" />
                <span class="lever"></span>
                启用演示模式
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="所有卫星旁边都会出现小文本标签.">
                <input id="settings-sat-label-mode" type="checkbox" checked />
                <span class="lever"></span>
                开启卫星标签模式
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="当你旋转相机时，时间将会冻结">
                <input id="settings-freeze-drag" type="checkbox" />
                <span class="lever"></span>
                启用点击冻结时间
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="时光穿梭停止显示提示消息">
                <input id="settings-time-machine-toasts" type="checkbox" />
                <span class="lever"></span>
                禁用时光穿梭
              </label>
            </div>
          </div>
          <div class="row light-blue darken-3" style="height:4px; display:block;"></div>
          <div id="settings-colors" class="row">
            <h5 class="center-align">颜色设置</h5>
            <div class="row">
              <div class="input-field col s6">
                <center>
                  <p>载荷</p>
                  <button id="settings-color-payload" class="btn waves-effect waves-light"></button>
                </center>
              </div>
              <div class="input-field col s6">
                <center>
                  <p>火箭体</p>
                  <button id="settings-color-rocketBody" class="btn waves-effect waves-light"></button>
                </center>
              </div>
            </div>
            <div class="row">
              <div class="input-field col s6">
                <center>
                  <p>碎片</p>
                  <button id="settings-color-debris" class="btn waves-effect waves-light"></button>
                </center>
              </div>
              <div class="input-field col s6">
                <center>
                  <p>视野内卫星</p>
                  <button id="settings-color-inview" class="btn waves-effect waves-light"></button>
                </center>
              </div>
            </div>
            <div class="row">
              <div class="input-field col s6">
                <center>
                  <p>导弹</p>
                  <button id="settings-color-missile" class="btn waves-effect waves-light"></button>
                </center>
              </div>
              <div class="input-field col s6">
                <center>
                  <p>视野内导弹</p>
                  <button id="settings-color-missileInview" class="btn waves-effect waves-light"></button>
                </center>
              </div>
            </div>
            <div class="row">
              <div class="input-field col s6">
                <center>
                  <p>特殊卫星</p>
                  <button id="settings-color-special" class="btn waves-effect waves-light"></button>
                </center>
              </div>
            </div>
          </div>
          <div id="settings-opt" class="row">
            <div class="row">
              <h5 class="center-align">设置覆盖</h5>
            </div>
            <div class="row">
              <div class="input-field col s12">
                <input value="150" id="maxSearchSats" type="text" data-position="top" data-delay="50" data-tooltip="显示的最大搜索结果" />
                <label for="maxSearchSats" class="active">搜索最大结果</label>
              </div>
            </div>
            <div class="row">
              <div class="input-field col s12">
                <input value="30" id="satFieldOfView" type="text" data-position="top" data-delay="50" data-tooltip="卫星的视场角是多少度" />
                <label for="satFieldOfView" class="active">卫星视场角</label>
              </div>
            </div>
          </div>
          <div id="fastCompSettings" class="row">
            <h5 class="center-align">需要快速CPU</h5>
            <div class="switch row">
              <label>
                <input id="settings-snp" type="checkbox" />
                <span class="lever"></span>
                悬停时显示下一个通行证
              </label>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>`;

  isNotColorPickerInitialSetup = false;

  addHtml(): void {
    super.addHtml();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {

        getEl('settings-form')?.addEventListener('change', SettingsMenuPlugin.onFormChange_);
        getEl('settings-form')?.addEventListener('submit', SettingsMenuPlugin.onSubmit_);
        getEl('settings-reset')?.addEventListener('click', SettingsMenuPlugin.resetToDefaults);


        if (!settingsManager.isShowConfidenceLevels) {
          hideEl(getEl('settings-confidence-levels')!.parentElement!.parentElement!);
        }

        if (!settingsManager.plugins.TimeMachine) {
          hideEl(getEl('settings-time-machine-toasts')!.parentElement!.parentElement!);
        }

        const colorPalette = [
          // Reds
          rgbCss([1.0, 0.0, 0.0, 1.0]), // Red
          rgbCss([1.0, 0.4, 0.4, 1.0]), // Light Red
          rgbCss([1.0, 0.0, 0.6, 1.0]), // Pink
          rgbCss([1.0, 0.75, 0.8, 1.0]), // Light Pink
          rgbCss([1.0, 0.0, 1.0, 1.0]), // Magenta

          // Oranges
          rgbCss([1.0, 0.65, 0.0, 1.0]), // Orange
          rgbCss([0.85, 0.5, 0.0, 1.0]), // Dark Orange
          rgbCss([1.0, 0.8, 0.6, 1.0]), // Peach

          // Yellows
          rgbCss([1.0, 1.0, 0.0, 1.0]), // Yellow
          rgbCss([0.8, 0.4, 0.0, 1.0]), // Dark Yellow

          // Greens
          rgbCss([0.4, 0.8, 0.0, 1.0]), // Chartreuse
          rgbCss([0.0, 1.0, 0.0, 1.0]), // Lime Green
          rgbCss([0.2, 1.0, 0.0, 0.5]), // Dark Green (with transparency)
          rgbCss([0.5, 1.0, 0.5, 1.0]), // Mint Green
          rgbCss([0.6, 0.8, 0.2, 1.0]), // Olive Green

          // Cyans
          rgbCss([0.0, 1.0, 1.0, 1.0]), // Cyan
          rgbCss([0.0, 0.8, 0.8, 1.0]), // Light Blue
          rgbCss([0.0, 0.5, 0.5, 1.0]), // Teal
          rgbCss([0.0, 0.2, 0.4, 1.0]), // Dark Teal

          // Blues
          rgbCss([0.2, 0.4, 1.0, 1.0]), // Dark Blue
          rgbCss([0.0, 0.0, 0.5, 1.0]), // Navy Blue

          // Purples
          rgbCss([0.5, 0.0, 1.0, 1.0]), // Purple
          rgbCss([0.5, 0.0, 0.5, 1.0]), // Dark Purple
          rgbCss([0.8, 0.2, 0.8, 1.0]), // Violet

          // Browns
          rgbCss([0.5, 0.25, 0.0, 1.0]), // Brown
          rgbCss([0.6, 0.4, 0.2, 1.0]), // Tan
          rgbCss([0.9, 0.9, 0.5, 1.0]), // Beige

          // Grays
          rgbCss([0.9, 0.9, 0.9, 1.0]), // Light Gray
          rgbCss([0.5, 0.5, 0.5, 1.0]), // Gray
          rgbCss([0.1, 0.1, 0.1, 1.0]), // Dark Gray
        ];

        ColorPick.initColorPick('#settings-color-payload', {
          initialColor: rgbCss(settingsManager.colors?.payload || [0.2, 1.0, 0.0, 0.5]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'payload'),
        });
        ColorPick.initColorPick('#settings-color-rocketBody', {
          initialColor: rgbCss(settingsManager.colors?.rocketBody || [0.2, 0.4, 1.0, 1]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'rocketBody'),
        });
        ColorPick.initColorPick('#settings-color-debris', {
          initialColor: rgbCss(settingsManager.colors?.debris || [0.5, 0.5, 0.5, 1]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'debris'),
        });
        ColorPick.initColorPick('#settings-color-inview', {
          initialColor: rgbCss(settingsManager.colors?.inFOV || [0.85, 0.5, 0.0, 1.0]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'inview'),
        });
        ColorPick.initColorPick('#settings-color-missile', {
          initialColor: rgbCss(settingsManager.colors?.missile || [1.0, 1.0, 0.0, 1.0]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'missile'),
        });
        ColorPick.initColorPick('#settings-color-missileInview', {
          initialColor: rgbCss(settingsManager.colors?.missileInview || [1.0, 0.0, 0.0, 1.0]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'missileInview'),
        });
        ColorPick.initColorPick('#settings-color-special', {
          initialColor: rgbCss(settingsManager.colors?.pink || [1.0, 0.0, 0.6, 1.0]),
          palette: colorPalette,
          onColorSelected: (colorpick: ColorPick) => this.onColorSelected_(colorpick, 'pink'),
        });
        this.isNotColorPickerInitialSetup = true;
      },
    );
  }

  addJs(): void {
    super.addJs();
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, SettingsMenuPlugin.syncOnLoad);

    // 为ECF轨道数输入框添加验证
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => {
      setTimeout(() => {
        this.setupEcfInputValidation();
      }, 200);
    });
  }

  private setupEcfInputValidation(): void {
    const ecfInput = document.getElementById('settings-numberOfEcfOrbitsToDraw') as HTMLInputElement;
    if (!ecfInput) return;

    // 只在失焦时验证，不在输入时干扰
    ecfInput.addEventListener('blur', () => {
      let value = parseInt(ecfInput.value);

      // 只有在失焦时才修正无效值
      if (isNaN(value) || value < 1) {
        ecfInput.value = '1';
      } else if (value > 10) {
        ecfInput.value = '10';
      }
    });

    // 防止输入非数字字符（但允许删除和编辑）
    ecfInput.addEventListener('keypress', (e) => {
      // 允许数字、退格、删除、方向键等
      if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
        e.preventDefault();
      }
    });
  }

  static syncOnLoad() {
    const drawCameraWidgetEl = <HTMLInputElement>getEl('settings-drawCameraWidget');

    if (drawCameraWidgetEl) {
      drawCameraWidgetEl.checked = settingsManager.drawCameraWidget;
      const cameraControlWidgetEl = getEl('camera-control-widget');

      if (cameraControlWidgetEl) {
        cameraControlWidgetEl.style.display = settingsManager.drawCameraWidget ? 'block' : 'none';
      }
    }

    const settingsElements = [
      { id: 'settings-drawOrbits', setting: 'isDrawOrbits' },
      { id: 'settings-drawTrailingOrbits', setting: 'isDrawTrailingOrbits' },
      { id: 'settings-drawEcf', setting: 'isOrbitCruncherInEcf' },
      { id: 'settings-numberOfEcfOrbitsToDraw', setting: 'numberOfEcfOrbitsToDraw' },
      { id: 'settings-isDrawInCoverageLines', setting: 'isDrawInCoverageLines' },
      { id: 'settings-enableHoverOverlay', setting: 'enableHoverOverlay' },
      { id: 'settings-focusOnSatelliteWhenSelected', setting: 'isFocusOnSatelliteWhenSelected' },
      { id: 'settings-isDrawCovarianceEllipsoid', setting: 'isDrawCovarianceEllipsoid' },
      { id: 'settings-eciOnHover', setting: 'isEciOnHover' },
      { id: 'settings-hos', setting: 'colors.transparent[3] === 0' },
      { id: 'settings-confidence-levels', setting: 'isShowConfidenceLevels' },
      { id: 'settings-demo-mode', setting: 'isDemoModeOn' },
      { id: 'settings-sat-label-mode', setting: 'isSatLabelModeOn' },
      { id: 'settings-snp', setting: 'isShowNextPassOnHover' },
      { id: 'settings-freeze-drag', setting: 'isFreezePropRateOnDrag' },
      { id: 'settings-time-machine-toasts', setting: 'isDisableTimeMachineToasts' },
    ];

    settingsElements.forEach(({ id, setting }) => {
      const element = <HTMLInputElement>getEl(id);

      if (element) {
        if (setting.includes('colors.transparent')) {
          element.checked = settingsManager.colors.transparent[3] === 0;
        } else {
          element.checked = settingsManager[setting];
        }
      }
    });

    const maxSearchSatsEl = <HTMLInputElement>getEl('maxSearchSats');

    if (maxSearchSatsEl) {
      maxSearchSatsEl.value = settingsManager.searchLimit.toString();
    }
  }

  private onColorSelected_(context: ColorPick, colorStr: string) {
    if (typeof context === 'undefined' || context === null) {
      throw new Error('context is undefined');
    }
    if (typeof colorStr === 'undefined' || colorStr === null) {
      throw new Error('colorStr is undefined');
    }

    context.element.style.cssText = `background-color: ${context.color} !important; color: ${context.color};`;
    if (this.isNotColorPickerInitialSetup) {
      settingsManager.colors[colorStr] = parseRgba(context.color);
      LegendManager.legendColorsChange();
      const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

      colorSchemeManagerInstance.calculateColorBuffers(true);
      PersistenceManager.getInstance().saveItem(StorageKey.SETTINGS_MANAGER_COLORS, JSON.stringify(settingsManager.colors));
    }
  }

  // eslint-disable-next-line complexity
  private static onFormChange_(e: Event, isDMChecked?: boolean, isSLMChecked?: boolean) {
    if (typeof e === 'undefined' || e === null) {
      throw new Error('e is undefined');
    }

    switch ((<HTMLElement>e.target)?.id) {
      case 'settings-drawOrbits':
      case 'settings-drawCameraWidget':
      case 'settings-drawTrailingOrbits':
      case 'settings-drawEcf':
      case 'settings-numberOfEcfOrbitsToDraw':
      case 'settings-isDrawInCoverageLines':
      case 'settings-enableHoverOverlay':
      case 'settings-focusOnSatelliteWhenSelected':
      case 'settings-isDrawCovarianceEllipsoid':
      case 'settings-drawSun':
      case 'settings-drawBlackEarth':
      case 'settings-drawAtmosphere':
      case 'settings-drawAurora':
      case 'settings-drawMilkyWay':
      case 'settings-graySkybox':
      case 'settings-eciOnHover':
      case 'settings-hos':
      case 'settings-confidence-levels':
      case 'settings-demo-mode':
      case 'settings-sat-label-mode':
      case 'settings-freeze-drag':
      case 'settings-time-machine-toasts':
      case 'settings-snp':
        if ((<HTMLInputElement>getEl((<HTMLInputElement>e.target)?.id ?? ''))?.checked) {
          // Play sound for enabling option
          keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_ON);
        } else {
          // Play sound for disabling option
          keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_OFF);
        }
        break;
      default:
        break;
    }

    isDMChecked ??= (<HTMLInputElement>getEl('settings-demo-mode')).checked;
    isSLMChecked ??= (<HTMLInputElement>getEl('settings-sat-label-mode')).checked;

    if (isSLMChecked && (<HTMLElement>e.target).id === 'settings-demo-mode') {
      (<HTMLInputElement>getEl('settings-sat-label-mode')).checked = false;
      getEl('settings-demo-mode')?.classList.remove('lever:after');
    }

    if (isDMChecked && (<HTMLElement>e.target).id === 'settings-sat-label-mode') {
      (<HTMLInputElement>getEl('settings-demo-mode')).checked = false;
      getEl('settings-sat-label-mode')?.classList.remove('lever:after');
    }
  }

  static resetToDefaults() {
    keepTrackApi.getSoundManager().play(SoundNames.BUTTON_CLICK);
    settingsManager.isDrawOrbits = true;
    settingsManager.drawCameraWidget = false;
    settingsManager.isDrawTrailingOrbits = false;
    settingsManager.isOrbitCruncherInEcf = false;
    settingsManager.isDrawInCoverageLines = true;
    settingsManager.enableHoverOverlay = true;
    settingsManager.isFocusOnSatelliteWhenSelected = true;
    settingsManager.isEciOnHover = false;
    settingsManager.isDemoModeOn = false;
    settingsManager.isSatLabelModeOn = true;
    settingsManager.isFreezePropRateOnDrag = false;
    settingsManager.isDisableTimeMachineToasts = false;
    settingsManager.searchLimit = 100000;
    PersistenceManager.getInstance().removeItem(StorageKey.SETTINGS_DOT_COLORS);
    SettingsManager.preserveSettings();
    SettingsMenuPlugin.syncOnLoad();
  }

  private static onSubmit_(e: SubmitEvent) {
    if (typeof e === 'undefined' || e === null) {
      throw new Error('e is undefined');
    }
    e.preventDefault();

    const uiManagerInstance = keepTrackApi.getUiManager();
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    keepTrackApi.getSoundManager()?.play(SoundNames.BUTTON_CLICK);

    settingsManager.isOrbitCruncherInEcf = (<HTMLInputElement>getEl('settings-drawEcf')).checked;
    let numberOfEcfOrbitsToDraw = parseInt((<HTMLInputElement>getEl('settings-numberOfEcfOrbitsToDraw')).value);

    // 验证ECF轨道数范围
    if (isNaN(numberOfEcfOrbitsToDraw) || numberOfEcfOrbitsToDraw < 1 || numberOfEcfOrbitsToDraw > 10) {
      numberOfEcfOrbitsToDraw = 1;
      (<HTMLInputElement>getEl('settings-numberOfEcfOrbitsToDraw')).value = '1';
    }

    if (numberOfEcfOrbitsToDraw !== settingsManager.numberOfEcfOrbitsToDraw) {
      keepTrackApi.getOrbitManager().orbitWorker.postMessage({
        typ: OrbitCruncherType.SETTINGS_UPDATE,
        numberOfOrbitsToDraw: numberOfEcfOrbitsToDraw,
      });
    }
    settingsManager.numberOfEcfOrbitsToDraw = numberOfEcfOrbitsToDraw;
    settingsManager.isDrawInCoverageLines = (<HTMLInputElement>getEl('settings-isDrawInCoverageLines')).checked;
    settingsManager.enableHoverOverlay = (<HTMLInputElement>getEl('settings-enableHoverOverlay')).checked;
    settingsManager.isFocusOnSatelliteWhenSelected = (<HTMLInputElement>getEl('settings-focusOnSatelliteWhenSelected')).checked;
    settingsManager.isDrawCovarianceEllipsoid = (<HTMLInputElement>getEl('settings-isDrawCovarianceEllipsoid')).checked;
    settingsManager.drawCameraWidget = (<HTMLInputElement>getEl('settings-drawCameraWidget')).checked;
    const ccWidgetCanvas = getEl('camera-control-widget');

    if (ccWidgetCanvas) {
      if (settingsManager.drawCameraWidget) {
        ccWidgetCanvas.style.display = 'block';
      } else {
        ccWidgetCanvas.style.display = 'none';
      }
    }

    const isDrawOrbitsChanged = settingsManager.isDrawOrbits !== (<HTMLInputElement>getEl('settings-drawOrbits')).checked;

    settingsManager.isDrawOrbits = (<HTMLInputElement>getEl('settings-drawOrbits')).checked;
    if (isDrawOrbitsChanged) {
      keepTrackApi.getOrbitManager().drawOrbitsSettingChanged();
    }
    settingsManager.isDrawTrailingOrbits = (<HTMLInputElement>getEl('settings-drawTrailingOrbits')).checked;

    if (keepTrackApi.getOrbitManager().orbitWorker) {
      if (settingsManager.isDrawTrailingOrbits) {
        keepTrackApi.getOrbitManager().orbitWorker.postMessage({
          typ: OrbitCruncherType.CHANGE_ORBIT_TYPE,
          orbitType: OrbitDrawTypes.TRAIL,
        });
      } else {
        keepTrackApi.getOrbitManager().orbitWorker.postMessage({
          typ: OrbitCruncherType.CHANGE_ORBIT_TYPE,
          orbitType: OrbitDrawTypes.ORBIT,
        });
      }
    }
    // Must come after the above checks
    settingsManager.isEciOnHover = (<HTMLInputElement>getEl('settings-eciOnHover')).checked;
    const isHOSChecked = (<HTMLInputElement>getEl('settings-hos')).checked;

    settingsManager.colors.transparent = isHOSChecked ? [1.0, 1.0, 1.0, 0] : [1.0, 1.0, 1.0, 0.1];
    settingsManager.isShowConfidenceLevels = (<HTMLInputElement>getEl('settings-confidence-levels')).checked;
    settingsManager.isDemoModeOn = (<HTMLInputElement>getEl('settings-demo-mode')).checked;
    settingsManager.isSatLabelModeOn = (<HTMLInputElement>getEl('settings-sat-label-mode')).checked;
    settingsManager.isShowNextPass = (<HTMLInputElement>getEl('settings-snp')).checked;
    settingsManager.isFreezePropRateOnDrag = (<HTMLInputElement>getEl('settings-freeze-drag')).checked;

    settingsManager.isDisableTimeMachineToasts = (<HTMLInputElement>getEl('settings-time-machine-toasts')).checked;
    const timeMachinePlugin = keepTrackApi.getPlugin(TimeMachine);

    /*
     * TODO: These settings buttons should be inside the plugins themselves
     * Stop Time Machine
     */
    if (timeMachinePlugin) {
      timeMachinePlugin.isMenuButtonActive = false;
    }

    /*
     * if (orbitManagerInstance.isTimeMachineRunning) {
     *   settingsManager.colors.transparent = orbitManagerInstance.tempTransColor;
     * }
     */
    keepTrackApi.getGroupsManager().clearSelect();
    colorSchemeManagerInstance.calculateColorBuffers(true); // force color recalc

    keepTrackApi.getPlugin(TimeMachine)?.setBottomIconToUnselected();

    colorSchemeManagerInstance.reloadColors();

    const newFieldOfView = parseInt((<HTMLInputElement>getEl('satFieldOfView')).value);

    if (isNaN(newFieldOfView)) {
      (<HTMLInputElement>getEl('satFieldOfView')).value = '30';
      uiManagerInstance.toast('视野值无效！', ToastMsgType.critical);
    }

    const maxSearchSats = parseInt((<HTMLInputElement>getEl('maxSearchSats')).value);

    if (isNaN(maxSearchSats)) {
      (<HTMLInputElement>getEl('maxSearchSats')).value = settingsManager.searchLimit.toString();
      uiManagerInstance.toast('最大搜索结果值无效', ToastMsgType.critical);
    } else {
      settingsManager.searchLimit = maxSearchSats;
      uiManagerInstance.searchManager.doSearch(keepTrackApi.getUiManager().searchManager.getCurrentSearch());
    }

    colorSchemeManagerInstance.calculateColorBuffers(true);

    SettingsManager.preserveSettings();
  }
}

