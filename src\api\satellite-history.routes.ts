import { Router } from 'express';
import { Client } from '@elastic/elasticsearch';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

const router = Router();

// 读取字段映射配置
let fieldMapping: any = null;
function loadFieldMapping() {
  if (!fieldMapping) {
    try {
      const configPath = path.resolve('es-field-mapping.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        fieldMapping = JSON.parse(configData);
        console.log('字段映射配置加载成功');
      } else {
        // 使用默认配置
        fieldMapping = {
          elasticsearch: {
            index: "orbital_tle",
            fields: {
              norad_id: "norad_id",
              time: "time",
              object_type: "orbital_elements.object_type.keyword",
              object_name: "satellite_name",
              satellite_name: "satellite_name",
              subsat_long: "orbital_elements.subsat_long",
              subsat_lat: "orbital_elements.subsat_lat",
              orbital_elements: "orbital_elements"
            },
            geo_satellite: {
              object_type_value: "GEO",
              longitude_field: "orbital_elements.subsat_long",
              latitude_field: "orbital_elements.subsat_lat"
            },
            query_limits: {
              max_records: 50000,
              batch_size: 1000,
              timeout_seconds: 30
            }
          }
        };
        console.log('使用默认字段映射配置');
      }
    } catch (error) {
      console.error('加载字段映射配置失败:', error);
      throw error;
    }
  }
  return fieldMapping;
}

// AES-256-GCM加密密钥
const ENCRYPTION_KEY = process.env.ES_ENCRYPTION_KEY || 'your-secret-key-32-chars-long!!';
const ALGORITHM = 'aes-256-gcm';

// 读取ES配置（使用AES-256-GCM加密）
const esConfigPath = 'es-config.enc';
function loadESConfig() {
  if (!fs.existsSync(esConfigPath)) throw new Error('ES配置文件不存在');

  try {
    const encryptedData = fs.readFileSync(esConfigPath, 'utf8');
    const data = JSON.parse(encryptedData);

    // 解密数据
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = Buffer.from(data.iv, 'hex');
    const authTag = Buffer.from(data.authTag, 'hex');
    const encrypted = Buffer.from(data.encrypted, 'hex');

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from(''));

    let decrypted = decipher.update(encrypted);
    const final = decipher.final();
    decrypted = Buffer.concat([decrypted, final]);

    const config = JSON.parse(decrypted.toString('utf8'));
    return config.elasticsearch;
  } catch (error) {
    console.error('解密ES配置失败:', error);
    throw new Error('ES配置文件解密失败');
  }
}

const esConfig = loadESConfig();
const esClient = new Client({
  node: esConfig.url,
  auth: {
    username: esConfig.username,
    password: esConfig.password,
  },
  maxRetries: 3,
  requestTimeout: 30000,
});

/**
 * 标准化的卫星历史数据查询API
 *
 * 统一端点支持多种查询类型:
 * 1. 历史轨道数据查询:
 *    GET /api/satellite-history?norad_id=25544&start=2025-01-01&end=2025-07-01
 *
 * 2. 历史经度数据查询:
 *    GET /api/satellite-history?start=2025-01-01&end=2025-07-01&geo_only=true&lon_min=85&lon_max=100
 */

// 统一的卫星历史数据查询端点
router.get('/', async (req, res) => {
  try {
    const config = loadFieldMapping();
    const fields = config.elasticsearch.fields;
    const geoConfig = config.elasticsearch.geo_satellite;
    const limits = config.elasticsearch.query_limits;

    const noradId = req.query.norad_id;
    const startDate = req.query.start;
    const endDate = req.query.end;
    const geoOnly = req.query.geo_only === 'true';
    const lonMin = req.query.lon_min ? parseFloat(req.query.lon_min as string) : null;
    const lonMax = req.query.lon_max ? parseFloat(req.query.lon_max as string) : null;

    // 参数验证
    if (!startDate || !endDate) {
      return res.status(400).json({
        error: '缺少必需参数',
        message: '请提供start和end日期参数',
        example: '/api/satellite-history?norad_id=25544&start=2025-01-01&end=2025-07-01'
      });
    }

    const startDatetime = `${startDate}T00:00:00Z`;
    const endDatetime = `${endDate}T23:59:59Z`;

    // 构建基础查询
    const query: any = {
      bool: {
        must: [
          { range: { [fields.time]: { gte: startDatetime, lte: endDatetime } } }
        ]
      }
    };

    let queryType = 'orbital';
    let noradIds: number[] = [];

    if (geoOnly) {
      // GEO卫星经度查询模式
      queryType = 'longitude';
      query.bool.must.push({ term: { [fields.object_type]: geoConfig.object_type_value } });

      // 确保经度字段存在（重要！）
      query.bool.must.push({ exists: { field: geoConfig.longitude_field } });

      // 如果同时指定了NORAD ID，也添加到查询条件
      if (noradId) {
        if (Array.isArray(noradId)) {
          noradIds = noradId.flatMap(id =>
            String(id).split(/[\s,]+/).map(n => parseInt(n.trim())).filter(n => !isNaN(n))
          );
        } else {
          noradIds = String(noradId).split(/[\s,]+/).map(n => parseInt(n.trim())).filter(n => !isNaN(n));
        }

        if (noradIds.length > 0) {
          query.bool.must.push({ terms: { [fields.norad_id]: noradIds } });
        }
      }

      // 添加经度范围过滤
      if (lonMin !== null && lonMax !== null && !isNaN(lonMin) && !isNaN(lonMax)) {
        query.bool.must.push({
          range: {
            [geoConfig.longitude_field]: {
              gte: Math.min(lonMin, lonMax),
              lte: Math.max(lonMin, lonMax)
            }
          }
        });
      }

      console.log('查询GEO经度数据参数:', { startDatetime, endDatetime, lonMin, lonMax, noradIds });
    } else {
      // 轨道数据查询模式
      if (!noradId) {
        return res.status(400).json({
          error: '缺少NORAD编号参数',
          message: '轨道数据查询需要指定NORAD编号',
          example: '/api/satellite-history?norad_id=25544&start=2025-01-01&end=2025-07-01'
        });
      }

      // 解析NORAD ID列表
      if (Array.isArray(noradId)) {
        noradIds = noradId.flatMap(id =>
          String(id).split(/[\s,]+/).map(n => parseInt(n.trim())).filter(n => !isNaN(n))
        );
      } else {
        noradIds = String(noradId).split(/[\s,]+/).map(n => parseInt(n.trim())).filter(n => !isNaN(n));
      }

      if (noradIds.length === 0) {
        return res.status(400).json({ error: '无效的NORAD编号' });
      }

      query.bool.must.push({ terms: { [fields.norad_id]: noradIds } });
      console.log('查询轨道数据参数:', { noradIds, startDatetime, endDatetime });
    }

    // 执行查询
    const sourceFields = [
      fields.norad_id,
      fields.time,
      fields.satellite_name,
      'orbital_elements'
    ];

    const allRecords = await executeScrollQuery(query, sourceFields, limits);

    // 根据查询类型转换数据格式
    let transformedRecords: any[];

    if (geoOnly) {
      // GEO经度数据格式 - 保持与原有代码兼容
      transformedRecords = allRecords.map((record: any, index: number) => {
        const transformed = {
          norad_id: record.norad_id,
          time: record.time,
          satellite_name: record.satellite_name,
          object_name: record.satellite_name,
          object_type: record.orbital_elements?.object_type,
          subsat_long: record.orbital_elements?.subsat_long,
          subsat_lat: record.orbital_elements?.subsat_lat,
          // 同时提供新字段名（向前兼容）
          longitude: record.orbital_elements?.subsat_long,
          latitude: record.orbital_elements?.subsat_lat
        };

        // 调试：打印前几条记录的详细信息
        if (index < 3) {
          console.log(`GEO记录 ${index + 1}:`, {
            norad_id: transformed.norad_id,
            satellite_name: transformed.satellite_name,
            object_type: transformed.object_type,
            subsat_long: transformed.subsat_long,
            subsat_lat: transformed.subsat_lat,
            orbital_elements_exists: !!record.orbital_elements,
            orbital_elements_keys: record.orbital_elements ? Object.keys(record.orbital_elements) : []
          });
        }

        return transformed;
      });
    } else {
      // 轨道数据格式 - 保留完整的orbital_elements
      transformedRecords = allRecords.map((record: any) => ({
        norad_id: record.norad_id,
        time: record.time,
        satellite_name: record.satellite_name,
        orbital_elements: record.orbital_elements || {}
      }));
    }

    console.log(`${queryType}数据查询完成: 返回 ${transformedRecords.length} 条记录`);

    // 构建参数信息
    const parameters: any = { startDate, endDate };
    if (geoOnly) {
      parameters.geoOnly = true;
      if (lonMin !== null) parameters.lonMin = lonMin;
      if (lonMax !== null) parameters.lonMax = lonMax;
      if (noradIds.length > 0) parameters.noradIds = noradIds;
    } else {
      parameters.noradIds = noradIds;
    }

    console.log(`查询参数:`, parameters);

    return res.json({
      count: transformedRecords.length,
      data: transformedRecords,
      query_type: queryType,
      parameters: parameters
    });

  } catch (error) {
    console.error('数据查询失败:', error);
    return res.status(500).json({
      error: '查询失败',
      message: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 通用的滚动查询执行函数
async function executeScrollQuery(query: any, sourceFields: string[], limits: any): Promise<any[]> {
  const batchSize = limits.batch_size;
  let allRecords: any[] = [];
  let searchAfter: any[] | undefined = undefined;
  let hasMore = true;

  while (hasMore && allRecords.length < limits.max_records) {
    const searchParams: any = {
      index: fieldMapping.elasticsearch.index,
      size: batchSize,
      query: query,
      _source: sourceFields,
      sort: [{ time: 'asc' }, { norad_id: 'asc' }]
    };

    if (searchAfter) {
      searchParams.search_after = searchAfter;
    }

    const result = await esClient.search(searchParams);
    const hits = result.hits?.hits || [];

    if (hits.length === 0) {
      hasMore = false;
      break;
    }

    const records = hits.map((h: any) => h._source);
    allRecords.push(...records);
    searchAfter = hits[hits.length - 1]?.sort;

    if (!searchAfter || allRecords.length >= limits.max_records) {
      hasMore = false;
    }
  }

  return allRecords;
}

export default router;
