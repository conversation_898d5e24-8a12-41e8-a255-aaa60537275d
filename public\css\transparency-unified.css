/*
 * 统一透明效果文件
 * 替代: force-transparent.css, transparent-menus.css
 * 整合所有透明相关的样式，避免重复和冲突
 * 创建时间: 2025-01-25
 */

/* ========================================
 * 核心透明变量
 * ======================================== */
:root {
  /* 透明度级别 */
  --transparency-full: transparent;
  --transparency-light: rgba(255, 255, 255, 0.05);
  --transparency-medium: rgba(255, 255, 255, 0.1);
  --transparency-heavy: rgba(255, 255, 255, 0.15);
  
  /* 模糊效果 */
  --blur-light: blur(5px);
  --blur-medium: blur(10px);
  --blur-heavy: blur(15px);
}

/* ========================================
 * 底部菜单透明效果
 * ======================================== */

/* 底部菜单容器 */
#nav-footer,
#bottom-icons-container {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
  backdrop-filter: var(--blur-medium) !important;
  -webkit-backdrop-filter: var(--blur-medium) !important;
}

/* 底部菜单图标区域 */
#bottom-icons {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
}

/* 底部菜单过滤器 */
#bottom-icons-filter {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
}

/* 底部菜单项 */
.bmenu-item,
.bmenu-filter-item {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 底部菜单项 - 移除所有悬停效果和边框 */
.bmenu-item,
.bmenu-filter-item,
.bmenu-item-inner,
.bmenu-filter-item-inner {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  transition: none !important;
}

.bmenu-item:hover,
.bmenu-filter-item:hover,
.bmenu-item-inner:hover,
.bmenu-filter-item-inner:hover {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* ========================================
 * 侧边菜单透明效果
 * ======================================== */

/* 所有侧边菜单容器 */
[id$="-menu"],
.side-menu,
.menu-container {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
  backdrop-filter: var(--blur-medium) !important;
  -webkit-backdrop-filter: var(--blur-medium) !important;
  border: none !important;
  box-shadow: none !important;
}

/* 菜单项 */
[id$="-menu"] .menu-item,
.side-menu .menu-item {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
}

/* 菜单项悬停 */
[id$="-menu"] .menu-item:hover,
.side-menu .menu-item:hover {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
}

/* ========================================
 * 弹窗和对话框透明效果
 * ======================================== */

/* 模态框背景 */
.modal,
.dialog,
.popup {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  backdrop-filter: var(--blur-heavy) !important;
  -webkit-backdrop-filter: var(--blur-heavy) !important;
}

/* 弹窗内容区域 */
.modal-content,
.dialog-content,
.popup-content {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
  backdrop-filter: var(--blur-medium) !important;
  -webkit-backdrop-filter: var(--blur-medium) !important;
  border: none !important;
  border-radius: 8px !important;
}

/* sat-info-box特殊处理 - 完全无边框 */
#sat-infobox,
#sat-infobox .modal-content,
#sat-infobox .dialog-content,
#sat-infobox .popup-content {
  border: none !important;
  box-shadow: none !important;
}

/* 时间管理器窗口透明模糊 - 与sat-info-box一致 */
#ui-datepicker-div,
.ui-timepicker-div,
.ui-datepicker,
.ui-timepicker-wrapper {
  background: transparent !important;
  background-color: transparent !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 8px !important;
}

/* 时间管理器内部元素透明 */
#ui-datepicker-div *,
.ui-timepicker-div *,
.ui-datepicker *,
.ui-timepicker-wrapper * {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 时间管理器头部和内容区域 */
.ui-datepicker-header,
.ui-datepicker-calendar,
.ui-timepicker-div table,
.ui-timepicker-div .ui-slider {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
}

/* 搜索结果窗口透明模糊 - 与sat-info-box一致 */
#search-results,
.search-results,
.search-container,
.search-dropdown {
  background: transparent !important;
  background-color: transparent !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 8px !important;
}

/* 搜索结果项目透明 */
.search-result,
.search-results li,
.search-item {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 搜索结果悬停效果 */
.search-result:hover,
.search-results li:hover,
.search-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* ========================================
 * 搜索框和输入框透明效果
 * ======================================== */

/* 搜索框容器 - 完全透明 */
#search-holder,
.search-container {
  background: transparent !important;
  background-color: transparent !important;
  backdrop-filter: none !important; /* 🔥 移除模糊效果 */
  -webkit-backdrop-filter: none !important; /* 🔥 移除webkit模糊效果 */
}

/* 🔥🔥🔥 搜索框专用样式 - 最高优先级 */
#search {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}

#search:focus,
#search:active {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}

/* 🔥🔥🔥 图例颜色方块保护 - 从透明化规则中排除 */

/* 🔥🔥🔥 侧边菜单强制透明 - 终极优先级 */
[id$="-menu"] li:not(.Square-Box):not([class*="legend-"]),
[id$="-menu"] .row:not(.Square-Box):not([class*="legend-"]),
[id$="-menu"] .input-field:not(.Square-Box):not([class*="legend-"]),
[id$="-menu"] .switch:not(.Square-Box):not([class*="legend-"]),
[id$="-menu"] .btn:not(.Square-Box):not([class*="legend-"]),
[id$="-menu"] button:not(.Square-Box):not([class*="legend-"]),
.side-menu li:not(.Square-Box):not([class*="legend-"]),
.side-menu .row:not(.Square-Box):not([class*="legend-"]),
.side-menu .input-field:not(.Square-Box):not([class*="legend-"]),
.side-menu .switch:not(.Square-Box):not([class*="legend-"]),
.side-menu .btn:not(.Square-Box):not([class*="legend-"]),
.side-menu button:not(.Square-Box):not([class*="legend-"]) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 🔥 设置菜单开关按钮特殊处理 - 移除背景条 */
[id$="-menu"] .switch,
[id$="-menu"] .switch label,
[id$="-menu"] .switch .lever,
.side-menu .switch,
.side-menu .switch label,
.side-menu .switch .lever {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 但是保留开关按钮的滑块背景色 */
[id$="-menu"] .switch label .lever,
.side-menu .switch label .lever {
  background-color: #2196f3 !important;
}

[id$="-menu"] .switch label input[type=checkbox]:checked + .lever,
.side-menu .switch label input[type=checkbox]:checked + .lever {
  background-color: #1976d2 !important;
}

/* 输入框 - 暂时禁用以修复登录界面问题 */
/*
input[type="text"]:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#search):not(#loginUsername):not(#registerUsername),
input[type="search"]:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#search),
input[type="password"]:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#loginPassword):not(#registerPassword):not(#confirmPassword),
input[type="email"]:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#registerEmail),
select:not(#sat-infobox select):not(#sat-infobox *):not([id$="-menu"] select):not([id$="-menu"] *),
textarea:not(#sat-infobox textarea):not(#sat-infobox *):not([id$="-menu"] textarea):not([id$="-menu"] *) {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
  border: 1px solid var(--transparency-medium) !important;
  backdrop-filter: var(--blur-light) !important;
  -webkit-backdrop-filter: var(--blur-light) !important;
}
*/

/* 输入框聚焦 - 暂时禁用以修复登录界面问题 */
/*
input[type="text"]:focus:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#search):not(#loginUsername):not(#registerUsername),
input[type="search"]:focus:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#search),
input[type="password"]:focus:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#loginPassword):not(#registerPassword):not(#confirmPassword),
input[type="email"]:focus:not(#sat-infobox input):not(#sat-infobox *):not([id$="-menu"] input):not([id$="-menu"] *):not(#registerEmail),
select:focus:not(#sat-infobox select):not(#sat-infobox *):not([id$="-menu"] select):not([id$="-menu"] *),
textarea:focus:not(#sat-infobox textarea):not(#sat-infobox *):not([id$="-menu"] textarea):not([id$="-menu"] *) {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
  border: 1px solid var(--transparency-heavy) !important;
}
*/

/* sat-info-box内部元素完全无边框无背景，但排除拖动手柄 */
#sat-infobox input:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox select:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox textarea:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-section-header:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-key:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-value:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-data:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .sat-info-item:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .collection-item:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .row:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox .col:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox div:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox span:not(#sat-info-title):not(#sat-info-title *),
#sat-infobox p:not(#sat-info-title):not(#sat-info-title *) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* 🔥 确保sat-info-title拖动手柄不被透明化 */
#sat-info-title,
#sat-info-title * {
  pointer-events: auto !important;
  cursor: move !important;
}

/* 侧边菜单内部元素完全无边框无底色 - 全面覆盖，但排除图例 */
[id$="-menu"]:not(#legend-hover-menu),
[id$="-menu"]:not(#legend-hover-menu) *:not(.Square-Box):not([class*="legend-"]),
.side-menu:not(#legend-hover-menu),
.side-menu:not(#legend-hover-menu) *:not(.Square-Box):not([class*="legend-"]),
#side-menu:not(#legend-hover-menu),
#side-menu:not(#legend-hover-menu) *:not(.Square-Box):not([class*="legend-"]),
#side-menu-parent:not(#legend-hover-menu),
#side-menu-parent:not(#legend-hover-menu) *:not(.Square-Box):not([class*="legend-"]),
.menu-container,
.menu-container *,
.menu-content,
.menu-content *,
.menu-item,
.menu-option,
.menu-row,
.menu-section,
.menu-header,
.menu-body,
.menu-footer,
input[type="text"],
input[type="number"],
input[type="range"],
input[type="checkbox"],
input[type="radio"],
select,
textarea,
button,
.btn,
.btn-flat,
.btn-small,
.btn-large,
.input-field,
.row,
.col,
.card,
.card-panel,
.collection,
.collection-item,
.collapsible,
.collapsible-header,
.collapsible-body {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* 🔥🔥🔥 拖动区域特殊处理 - 确保可见和可交互 - 超高优先级 */
html body #bottom-icons-container > div[style*="cursor: n-resize"],
html body div[style*="cursor: n-resize"],
html body .drag-handle,
html body .resize-handle,
html body .drag-resize-handle,
#bottom-icons-container > div[style*="cursor: n-resize"],
div[style*="cursor: n-resize"],
.drag-handle,
.resize-handle,
.drag-resize-handle {
  background: rgba(255, 255, 255, 0.3) !important;
  background-color: rgba(255, 255, 255, 0.3) !important;
  background-image: none !important;
  pointer-events: auto !important;
  z-index: 9999 !important;
  opacity: 1 !important;
  visibility: visible !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  height: 4px !important;
  cursor: n-resize !important;
  border-top: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-bottom: none !important;
  border-left: none !important;
  border-right: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 拖动区域悬停效果 */
#bottom-icons-container > div[style*="cursor: n-resize"]:hover,
div[style*="cursor: n-resize"]:hover,
.drag-handle:hover,
.resize-handle:hover {
  background: rgba(255, 255, 255, 0.5) !important;
  background-color: rgba(255, 255, 255, 0.5) !important;
}

/* ========================================
 * 工具提示透明效果
 * ======================================== */

.tooltip,
.hint,
[data-tooltip] {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
  backdrop-filter: var(--blur-medium) !important;
  -webkit-backdrop-filter: var(--blur-medium) !important;
  border: 1px solid var(--transparency-heavy) !important;
  border-radius: 4px !important;
}

/* ========================================
 * 强制透明类
 * ======================================== */

/* 完全透明 */
.force-transparent {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 轻微透明 */
.force-transparent-light {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
  backdrop-filter: var(--blur-light) !important;
  -webkit-backdrop-filter: var(--blur-light) !important;
}

/* 中等透明 */
.force-transparent-medium {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
  backdrop-filter: var(--blur-medium) !important;
  -webkit-backdrop-filter: var(--blur-medium) !important;
}

/* ========================================
 * 禁用透明类（用于特殊情况）
 * ======================================== */

.disable-transparency {
  background: #1a1a1a !important;
  background-color: #1a1a1a !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* ========================================
 * 响应式透明效果
 * ======================================== */

/* 移动设备上减少模糊效果以提升性能 */
@media (max-width: 768px) {
  :root {
    --blur-light: blur(2px);
    --blur-medium: blur(3px);
    --blur-heavy: blur(5px);
  }
}

/* 高性能设备上增强效果 */
@media (min-width: 1920px) and (min-resolution: 2dppx) {
  :root {
    --blur-light: blur(8px);
    --blur-medium: blur(15px);
    --blur-heavy: blur(20px);
  }
}
