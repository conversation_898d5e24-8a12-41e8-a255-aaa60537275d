const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function checkGeoData() {
  try {
    console.log('正在连接 Elasticsearch...');
    console.log('ES 地址:', esConfig.url);
    console.log('索引:', esConfig.index);
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 30000,
    });

    // 1. 查找包含 subsat_long 字段的记录
    console.log('\n1. 查找包含 subsat_long 字段的记录...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          exists: { field: 'subsat_long' }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条包含 subsat_long 的记录`);
      
      if (result.hits?.hits?.length > 0) {
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`\n记录 ${index + 1}:`);
          console.log(`  norad_id: ${record.norad_id}`);
          console.log(`  satellite_name: ${record.satellite_name}`);
          console.log(`  subsat_long: ${record.subsat_long}`);
          console.log(`  object_type: ${record.object_type}`);
          console.log(`  time: ${record.time}`);
          console.log(`  所有字段: ${Object.keys(record).join(', ')}`);
        });
      }
    } catch (error) {
      console.log('查询 subsat_long 失败:', error.message);
    }

    // 2. 查找包含 object_type 字段的记录
    console.log('\n2. 查找包含 object_type 字段的记录...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          exists: { field: 'object_type' }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条包含 object_type 的记录`);
      
      if (result.hits?.hits?.length > 0) {
        // 统计不同的 object_type 值
        const typeStats = {};
        result.hits.hits.forEach(hit => {
          const type = hit._source.object_type;
          typeStats[type] = (typeStats[type] || 0) + 1;
        });
        
        console.log('object_type 值统计:');
        Object.entries(typeStats).forEach(([type, count]) => {
          console.log(`  - ${type}: ${count} 条`);
        });
      }
    } catch (error) {
      console.log('查询 object_type 失败:', error.message);
    }

    // 3. 查找 GEO 卫星
    console.log('\n3. 查找 GEO 卫星...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          term: { object_type: 'GEO' }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条 GEO 卫星记录`);
      
      if (result.hits?.hits?.length > 0) {
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`\nGEO卫星 ${index + 1}:`);
          console.log(`  norad_id: ${record.norad_id}`);
          console.log(`  satellite_name: ${record.satellite_name}`);
          console.log(`  subsat_long: ${record.subsat_long}`);
          console.log(`  time: ${record.time}`);
        });
      }
    } catch (error) {
      console.log('查询 GEO 卫星失败:', error.message);
    }

    // 4. 查找同时包含 subsat_long 和 object_type=GEO 的记录
    console.log('\n4. 查找同时包含 subsat_long 和 object_type=GEO 的记录...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 5,
        query: {
          bool: {
            must: [
              { term: { object_type: 'GEO' } },
              { exists: { field: 'subsat_long' } },
              { 
                bool: { 
                  must_not: { 
                    term: { subsat_long: '' } 
                  } 
                } 
              }
            ]
          }
        }
      });
      
      console.log(`找到 ${result.hits.total.value} 条符合条件的 GEO 卫星记录`);
      
      if (result.hits?.hits?.length > 0) {
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`\n符合条件的记录 ${index + 1}:`);
          console.log(`  norad_id: ${record.norad_id}`);
          console.log(`  satellite_name: ${record.satellite_name}`);
          console.log(`  object_type: ${record.object_type}`);
          console.log(`  subsat_long: ${record.subsat_long}`);
          console.log(`  time: ${record.time}`);
        });
      } else {
        console.log('未找到符合条件的记录，可能的原因:');
        console.log('1. object_type 的值不是 "GEO"');
        console.log('2. subsat_long 字段为空或不存在');
        console.log('3. 数据格式与预期不符');
      }
    } catch (error) {
      console.log('查询符合条件的记录失败:', error.message);
    }

    // 5. 查找最近的数据
    console.log('\n5. 查找最近的数据...');
    try {
      const result = await client.search({
        index: esConfig.index,
        size: 3,
        sort: [
          { time: { order: 'desc' } }
        ],
        query: {
          match_all: {}
        }
      });
      
      if (result.hits?.hits?.length > 0) {
        console.log('最近的记录:');
        result.hits.hits.forEach((hit, index) => {
          const record = hit._source;
          console.log(`\n最近记录 ${index + 1}:`);
          console.log(`  time: ${record.time}`);
          console.log(`  norad_id: ${record.norad_id}`);
          console.log(`  satellite_name: ${record.satellite_name}`);
          console.log(`  object_type: ${record.object_type || '无'}`);
          console.log(`  subsat_long: ${record.subsat_long || '无'}`);
        });
      }
    } catch (error) {
      console.log('查询最近数据失败:', error.message);
    }

  } catch (error) {
    console.error('检查GEO数据失败:', error.message);
    console.error('错误详情:', error);
  }
}

checkGeoData();
