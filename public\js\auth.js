class AuthManager {
  constructor(authServerUrl = 'http://127.0.0.1:3001') {
    this.authServerUrl = authServerUrl;
    this.isAuthenticated = false;
  }

  async login(username, password) {
    try {
      const response = await fetch(`${this.authServerUrl}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });
      
      if (!response.ok) throw new Error('认证失败');
      
      const data = await response.json();
      this.isAuthenticated = true;
      localStorage.setItem('authToken', data.token);
      return true;
    } catch (error) {
      console.error('登录错误:', error);
      return false;
    }
  }

  checkAuth() {
    return this.isAuthenticated || !!localStorage.getItem('authToken');
  }

  logout() {
    localStorage.removeItem('authToken');
    this.isAuthenticated = false;
  }
}

window.AuthManager = AuthManager;