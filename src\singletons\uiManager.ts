/* eslint-disable max-classes-per-file */
/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 * @Copyright (C) 2015-2016, <PERSON>
 *
 * Original source code released by <PERSON> at https://github.com/jeyoder/ThingsInSpace/
 * under the MIT License. Please reference https://spacedefense/license/thingsinspace.txt
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, ToastMsgType } from '@app/interfaces';
import { InputEventType, keepTrackApi } from '@app/keepTrackApi';
import { KeepTrackPlugin } from '@app/plugins/KeepTrackPlugin';
import { SoundNames } from '@app/plugins/sounds/SoundNames';
import { isThisNode } from '@app/static/isThisNode';
import '@materializecss/materialize';
import { BaseObject, DetailedSatellite, Milliseconds, MILLISECONDS_PER_SECOND } from 'ootk';
import { clickAndDragHeight, clickAndDragWidth } from '../lib/click-and-drag';
import { closeColorbox } from '../lib/colorbox';
import { getClass } from '../lib/get-class';
import { getEl, hideEl, setInnerHtml, showEl } from '../lib/get-el';
import { rgbCss } from '../lib/rgbCss';
import { LegendManager } from '../static/legend-manager';
import { UiValidation } from '../static/ui-validation';
import { ColorScheme } from './color-schemes/color-scheme';
import { errorManagerInstance } from './errorManager';
import { MobileManager } from './mobileManager';
import { SearchManager } from './search-manager';

export class UiManager {
  private static readonly LONG_TIMER_DELAY = MILLISECONDS_PER_SECOND * 100;

  private isFooterVisible_ = true; // 确保底部菜单默认可见
  private isInitialized_ = false;

  // materializecss/materialize goes to window.M, but we want a local reference
  M = window.M;
  bottomIconPress: (el: HTMLElement) => void;
  isAnalysisMenuOpen = false;
  isCurrentlyTyping = false;
  isUiVisible = true;
  lastBoxUpdateTime = 0;
  lastNextPassCalcSatId = 0;
  lastNextPassCalcSensorShortName: string;
  lastToast: string;
  searchManager: SearchManager;
  updateInterval = 1000;
  updateNextPassOverlay: (arg0: boolean) => void;
  searchHoverSatId = -1;
  isLegendMenuOpen = false;

  static fullscreenToggle() {
    if (!document.fullscreenElement) {
      try {
        document.documentElement?.requestFullscreen().catch((err) => {
          // Might fail on some browsers
          errorManagerInstance.debug(err);
        });
      } catch (e) {
        // Might fail on some browsers
        errorManagerInstance.debug(e);
      }
    } else {
      document.exitFullscreen();
    }

    setTimeout(() => {
      keepTrackApi.getRenderer().resizeCanvas(true);
    }, 100);
  }

  /** This runs after the drawManagerInstance starts */
  static postStart() {
    UiValidation.initUiValidation();

    setTimeout(() => {
      const imageElements = document.querySelectorAll('img') as unknown as {
        src: string;
        attributes: {
          delayedsrc: { value: string };
        };
      }[];

      imageElements.forEach((img) => {
        if (img.src && !img.src.includes('.svg') && !img.src.includes('.png') && !img.src.includes('.jpg')) {
          img.src = img.attributes.delayedsrc?.value;
        }
      });
    }, 0);

    // eslint-disable-next-line multiline-comment-style
    // // Enable Satbox Overlay
    // if (settingsManager.enableHoverOverlay) {
    //   try {
    //     const hoverboxDOM = document.createElement('div');
    //     hoverboxDOM.innerHTML = `
    //     <div id="sat-hoverbox">
    //       <span id="sat-hoverbox1"></span>
    //       <span id="sat-hoverbox2"></span>
    //       <span id="sat-hoverbox3"></span>
    //     </div>`;

    //     getEl('keeptrack-canvas')?.parentElement?.append(hoverboxDOM);
    //   } catch {
    //     /* istanbul ignore next */
    //     console.debug('document.createElement() failed!');
    //   }
    // }
  }

  dismissAllToasts() {
    this.activeToastList_.forEach((toast) => {
      toast.dismiss();
    });
    this.activeToastList_ = [];
  }

  private makeToast_(toastText: string, type: ToastMsgType, isLong = false) {
    if (settingsManager.isDisableToasts) {
      return null;
    }

    const toastMsg = window.M.toast({
      unsafeHTML: toastText,
    });

    // Add an on click event to dismiss the toast
    toastMsg.$el[0].addEventListener('click', () => {
      toastMsg.dismiss();
      this.activeToastList_ = this.activeToastList_.filter((t) => t !== toastMsg);
    });

    toastMsg.$el[0].addEventListener('contextmenu', () => {
      this.dismissAllToasts();
    });


    type = type || ToastMsgType.standby;
    if (isLong) {
      toastMsg.timeRemaining = UiManager.LONG_TIMER_DELAY;
    }

    setTimeout(() => {
      this.activeToastList_ = this.activeToastList_.filter((t) => t !== toastMsg);
    }, toastMsg.timeRemaining);

    switch (type) {
      case ToastMsgType.standby:
        toastMsg.$el[0].style.background = 'var(--statusDarkStandby)';
        keepTrackApi.getSoundManager()?.play(SoundNames.WARNING);
        break;
      case ToastMsgType.caution:
        toastMsg.$el[0].style.background = 'var(--statusDarkCaution)';
        keepTrackApi.getSoundManager()?.play(SoundNames.WARNING);
        break;
      case ToastMsgType.serious:
        toastMsg.$el[0].style.background = 'var(--statusDarkSerious)';
        keepTrackApi.getSoundManager()?.play(SoundNames.WARNING);
        break;
      case ToastMsgType.critical:
        toastMsg.$el[0].style.background = 'var(--statusDarkCritical)';
        keepTrackApi.getSoundManager()?.play(SoundNames.WARNING);
        break;
      case ToastMsgType.error:
        toastMsg.$el[0].style.background = 'var(--statusDarkCritical)';
        keepTrackApi.getSoundManager()?.play(SoundNames.ERROR);
        break;
      case ToastMsgType.normal:
      default:
        toastMsg.$el[0].style.background = 'var(--statusDarkNormal)';
        keepTrackApi.getSoundManager()?.play(SoundNames.WARNING);
        break;
    }

    return toastMsg;
  }

  colorSchemeChangeAlert(newScheme: ColorScheme) {
    /*
     * Don't make an alert unless something has really changed
     * Check if the name of the lastColorScheme function is the same as the name of the new color scheme
     */
    if (keepTrackApi.getColorSchemeManager().lastColorScheme?.id === newScheme.id) {
      return;
    }

    // Make an alert
    this.toast(`颜色模式切换到 ${newScheme.label}`, ToastMsgType.normal, false);
  }

  doSearch(searchString: string, isPreventDropDown?: boolean) {
    this.searchManager.doSearch(searchString, isPreventDropDown);
  }

  footerToggle() {
    if (this.isFooterVisible_) {
      this.isFooterVisible_ = false;
      getEl('nav-footer')?.classList.add('footer-slide-trans');
      getEl('nav-footer')?.classList.remove('footer-slide-up');
      getEl('nav-footer')?.classList.add('footer-slide-down');
      setInnerHtml('nav-footer-toggle', '&#x25B2;');
    } else {
      this.isFooterVisible_ = true;
      getEl('nav-footer')?.classList.add('footer-slide-trans');
      getEl('nav-footer')?.classList.remove('footer-slide-down');
      getEl('nav-footer')?.classList.add('footer-slide-up');
      setInnerHtml('nav-footer-toggle', '&#x25BC;');
    }
    // After 1 second the transition should be complete so lets stop moving slowly
    setTimeout(() => {
      getEl('nav-footer')?.classList.remove('footer-slide-trans');
    }, 1000);
  }

  hideUi() {
    if (this.isUiVisible) {
      hideEl('keeptrack-header');
      hideEl('ui-wrapper');
      hideEl('nav-footer');
      this.isUiVisible = false;
    } else {
      showEl('keeptrack-header');
      showEl('ui-wrapper');
      showEl('nav-footer');
      this.isUiVisible = true;
    }
  }

  hideSideMenus() {
    try {
      closeColorbox();
    } catch (e) {
      // closeColorbox 可能未定义，忽略错误
    }
    keepTrackApi.emit(KeepTrackApiEvents.hideSideMenus);
  }

  init() {
    if (this.isInitialized_) {
      throw new Error('UiManager already initialized');
    }

    this.searchManager = new SearchManager(this);

    if (settingsManager.isShowPrimaryLogo) {
      getEl('logo-primary')?.classList.remove('start-hidden');
    }
    if (settingsManager.isShowSecondaryLogo) {
      getEl('logo-secondary')?.classList.remove('start-hidden');
    }

    keepTrackApi.emit(KeepTrackApiEvents.uiManagerInit);

    // 备用机制：如果10秒后keeptrack-header仍然隐藏，强制显示它
    setTimeout(() => {
      const header = getEl('keeptrack-header', true);
      if (header && header.classList.contains('start-hidden')) {
        console.warn('Forcing keeptrack-header display after 10 seconds timeout');
        showEl('keeptrack-header');
      }
    }, 10000);

    this.sortBottomIcons();

    UiManager.initBottomMenuResizing_();

    // Initialize Navigation and Select Menus
    const elems = document.querySelectorAll('.dropdown-button');

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean, isShift: boolean) => {
      if (key === 'F2' && isShift && !isRepeat) {
        this.hideUi();
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === 'B' && !isRepeat) {
        this.toggleBottomMenu();
      }
    });

    window.M.Dropdown.init(elems);
    this.isInitialized_ = true;
  }

  private sortBottomIcons() {
    const bottomIcons = document.querySelectorAll('#bottom-icons > div');
    const sortedIcons = Array.from(bottomIcons).sort((a, b) => {
      const aOrder = parseInt(a.getAttribute('data-order') ?? KeepTrackPlugin.MAX_BOTTOM_ICON_ORDER.toString(), 10);
      const bOrder = parseInt(b.getAttribute('data-order') ?? KeepTrackPlugin.MAX_BOTTOM_ICON_ORDER.toString(), 10);

      return aOrder - bOrder;
    });
    const bottomIconsContainer = getEl('bottom-icons');

    if (bottomIconsContainer) {
      console.log('🔥🔥🔥 sortBottomIcons开始 - 调试信息:');
      const beforeStyle = window.getComputedStyle(bottomIconsContainer);
      console.log('排序前的样式:');
      console.log('  display:', beforeStyle.display);
      console.log('  gridTemplateColumns:', beforeStyle.gridTemplateColumns);
      console.log('  width:', beforeStyle.width);
      console.log('  float:', beforeStyle.float);

      // Clear the container before appending sorted icons
      bottomIconsContainer.innerHTML = '';
      sortedIcons.forEach((icon) => {
        bottomIconsContainer.appendChild(icon);
      });

      const afterInnerHTMLStyle = window.getComputedStyle(bottomIconsContainer);
      console.log('innerHTML清空后重新添加元素后的样式:');
      console.log('  display:', afterInnerHTMLStyle.display);
      console.log('  gridTemplateColumns:', afterInnerHTMLStyle.gridTemplateColumns);
      console.log('  width:', afterInnerHTMLStyle.width);
      console.log('  float:', afterInnerHTMLStyle.float);

      // 🔥 重要：重新设置innerHTML后，强制恢复grid布局 - 调整列宽和间距
      bottomIconsContainer.style.setProperty('display', 'grid', 'important');
      bottomIconsContainer.style.setProperty('grid-template-columns', 'repeat(auto-fill, 80px)', 'important');
      bottomIconsContainer.style.setProperty('justify-content', 'center', 'important');
      bottomIconsContainer.style.setProperty('width', 'calc(100% - 185px)', 'important');
      bottomIconsContainer.style.setProperty('float', 'right', 'important');
      bottomIconsContainer.style.setProperty('column-gap', '2px', 'important');

      const afterForceStyle = window.getComputedStyle(bottomIconsContainer);
      console.log('强制设置grid布局后的样式:');
      console.log('  display:', afterForceStyle.display);
      console.log('  gridTemplateColumns:', afterForceStyle.gridTemplateColumns);
      console.log('  width:', afterForceStyle.width);
      console.log('  float:', afterForceStyle.float);

      console.log('🔥 sortBottomIcons完成');
    }
  }

  initMenuController() {
    getEl('legend-hover-menu')?.addEventListener('click', (e: MouseEvent) => {
      const hoverMenuItemClass = (e.target as HTMLElement)?.classList[1];

      if (hoverMenuItemClass) {
        this.legendHoverMenuClick(hoverMenuItemClass);
      }
    });

    getEl('legend-menu')?.addEventListener('click', () => {
      if (this.isLegendMenuOpen) {
        // Closing Legend Menu
        hideEl('legend-hover-menu');
        getEl('legend-icon')?.classList.remove('bmenu-item-selected');
        this.isLegendMenuOpen = false;
      } else {
        // Opening Legend Menu

        if (getEl('legend-hover-menu')?.innerHTML.length === 0) {
          // TODO: Figure out why it is empty sometimes
          errorManagerInstance.debug('Legend Menu is Empty');
        }

        showEl('legend-hover-menu');
        getEl('legend-icon')?.classList.add('bmenu-item-selected');
        this.searchManager.hideResults();
        this.isLegendMenuOpen = true;
      }
    });

    // Resizing Listener
    window.addEventListener('resize', () => {
      MobileManager.checkMobileMode();
      settingsManager.isResizing = true;
    });

    this.addSearchEventListeners_();

    getEl('fullscreen-icon')?.addEventListener('click', () => {
      UiManager.fullscreenToggle();
    });

    getEl('nav-footer-toggle')?.addEventListener('click', () => {
      this.toggleBottomMenu();
    });

    clickAndDragWidth(getEl('settings-menu'));
    clickAndDragWidth(getEl('about-menu'));
  }

  toggleBottomMenu() {
    this.footerToggle();
    const navFooterDom = getEl('nav-footer');

    if (navFooterDom && parseInt(window.getComputedStyle(navFooterDom).bottom.replace('px', '')) < 0) {
      keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_ON);
      setTimeout(() => {
        const bottomHeight = getEl('bottom-icons-container')?.offsetHeight;
        // 确保菜单完全显示后再设置变量
        if (navFooterDom && parseInt(window.getComputedStyle(navFooterDom).bottom.replace('px', '')) >= 0) {
          document.documentElement.style.setProperty('--bottom-menu-top', `${bottomHeight}px`);
        }
      }, 1000); // Wait for the footer to be fully visible.
    } else {
      // If the footer is open, then it will be hidden shortly but we don't want to wait for it to be hidden
      keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_OFF);
      // 延迟设置，等待滑动动画完成
      setTimeout(() => {
        if (navFooterDom && parseInt(window.getComputedStyle(navFooterDom).bottom.replace('px', '')) < -120) {
          document.documentElement.style.setProperty('--bottom-menu-top', '0px');
        }
      }, 1100); // 稍微延迟，确保动画完成
    }
  }

  private addSearchEventListeners_() {
    getEl('search')?.addEventListener('focus', () => {
      this.isCurrentlyTyping = true;
    });
    getEl('ui-wrapper')?.addEventListener('focusin', () => {
      this.isCurrentlyTyping = true;
    });

    getEl('search')?.addEventListener('blur', () => {
      this.isCurrentlyTyping = false;
    });
    getEl('ui-wrapper')?.addEventListener('focusout', () => {
      this.isCurrentlyTyping = false;
    });
  }

  legendHoverMenuClick(legendType: string) {
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();
    const colorSchemeInstance = colorSchemeManagerInstance.currentColorScheme;
    const slug = legendType.split('-')[1];
    let isFlagOn = true;

    if (colorSchemeManagerInstance.objectTypeFlags[slug]) {
      isFlagOn = false;
    }

    if (!isFlagOn) {
      getClass(`legend-${slug}-box`).forEach((el) => {
        el.style.background = 'black';
      });
    } else {
      getClass(`legend-${slug}-box`).forEach((el) => {
        const color = colorSchemeInstance?.colorTheme[slug] ?? null;

        if (!color) {
          errorManagerInstance.log(`Color not found for ${slug}`);
        } else {
          el.style.background = rgbCss(color);
        }
      });
    }

    colorSchemeManagerInstance.objectTypeFlags[slug] = isFlagOn;
    if (colorSchemeInstance) {
      colorSchemeInstance.objectTypeFlags[slug] = colorSchemeManagerInstance.objectTypeFlags[slug];
    }

    colorSchemeManagerInstance.calculateColorBuffers(true);
  }

  onReady() {
    // Code Once index.htm is loaded
    if (settingsManager.offline) {
      this.updateInterval = 250;
    }

    // Setup Legend Colors
    LegendManager.legendColorsChange();

    // Run any plugins code
    keepTrackApi.emit(KeepTrackApiEvents.uiManagerOnReady);

    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        this.bottomIconPress = (el: HTMLElement) => keepTrackApi.emit(KeepTrackApiEvents.bottomMenuClick, el.id);
        const BottomIcons = getEl('bottom-icons');

        BottomIcons?.addEventListener('click', (evt: Event) => {
          const bottomIcons = getEl('bottom-icons');
          let targetElement = <HTMLElement | null>evt.target;

          while (targetElement && targetElement !== bottomIcons) {
            if (targetElement.parentElement === bottomIcons) {
              this.bottomIconPress(targetElement);

              return;
            }
            targetElement = targetElement.parentElement;
          }

          if (targetElement === bottomIcons) {
            return;
          }

          if (!targetElement) {
            errorManagerInstance.debug('targetElement is null');
          } else {
            this.bottomIconPress(targetElement);
          }
        });
        // 移除这个赋值，改为使用下面的方法
      },
    );
  }

  toast(toastText: string, type: ToastMsgType, isLong = false) {
    this.lastToast = toastText;

    if (isThisNode()) {
      // Testing environment only
      // eslint-disable-next-line no-console
      console.warn('Toast:', toastText);

      return;
    }

    try {
      // Stop toasts from crashing the app
      if (this.activeToastList_.length > 20) {
        return;
      }
      const toastMsg = this.makeToast_(toastText, type, isLong);

      if (toastMsg) {
        this.activeToastList_.push(toastMsg);
      }
    } catch (e) {
      errorManagerInstance.debug('toast failed');
    }
  }

  private activeToastList_ = [] as {
    $el: NodeListOf<HTMLElement>;
    timeRemaining: number;
    dismiss: () => void;
  }[];

  /**
   * Checks if enough time has elapsed and then calls all queued updateSelectBox callbacks
   */
  updateSelectBox(realTime: Milliseconds, lastBoxUpdateTime: Milliseconds, obj: BaseObject): void {
    if (!obj || obj.id === -1 || obj.isStatic()) {
      return;
    }

    const sat = obj as DetailedSatellite;

    if (realTime * 1 > lastBoxUpdateTime * 1 + this.updateInterval) {
      keepTrackApi.emit(KeepTrackApiEvents.updateSelectBox, sat);
      keepTrackApi.getTimeManager().lastBoxUpdateTime = realTime;
    }
  }

  private static initBottomMenuResizing_() {
    // Allow Resizing the bottom menu
    // 🔥 修复：设置合理的maxHeight，允许底部菜单增加高度
    const maxHeight = window.innerHeight * 0.5; // 最大高度为屏幕高度的50%
    // 🔥 修复：应该拖动nav-footer而不是bottom-icons-container
    const navFooterDom = getEl('nav-footer');

    console.log('🔍 [UI管理器] 底部菜单拖动初始化开始');
    console.log('🔍 [UI管理器] navFooterDom:', navFooterDom);
    console.log('🔍 [UI管理器] maxHeight:', maxHeight);

    if (!navFooterDom) {
      console.error('❌ [UI管理器] navFooterDom is null');
      errorManagerInstance.debug('navFooterDom is null');
    } else {
      console.log('✅ [UI管理器] 找到nav-footer，开始设置拖动功能');

      clickAndDragHeight(navFooterDom, maxHeight, () => {
        console.log('🎯 [UI管理器] 拖动回调函数被调用');
        let bottomHeight = navFooterDom.offsetHeight;

        console.log('🎯 [UI管理器] 更新底部菜单高度:', bottomHeight);
        document.documentElement.style.setProperty('--bottom-menu-height', `${bottomHeight}px`);

        // 🔥 同时更新内部容器的高度，确保没有空隙
        const bottomIconsContainerDom = getEl('bottom-icons-container');
        if (bottomIconsContainerDom) {
          bottomIconsContainerDom.style.setProperty('height', `${bottomHeight}px`, 'important');
        }

        const bottomIconsDom = getEl('bottom-icons');
        if (bottomIconsDom) {
          const iconsHeight = Math.max(bottomHeight - 20, 60); // 减去padding，但保证最小高度
          bottomIconsDom.style.setProperty('max-height', `${iconsHeight}px`, 'important');
        }

        // 🔥 更新CSS变量，确保其他地方使用的高度也同步
        document.documentElement.style.setProperty('--bottom-menu-height', `${bottomHeight}px`);

        // 🔥 更新CSS变量
        document.documentElement.style.setProperty('--bottom-menu-top', `${bottomHeight}px`);
      });
    }
  }
}
