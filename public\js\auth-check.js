/**
 * 认证检查脚本
 * 在主页面加载前检查用户登录状态
 */

class AuthChecker {
    constructor() {
        this.apiBaseUrl = '';
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    async init() {
        this.apiBaseUrl = await this.getApiBaseUrl();
    }

    async getApiBaseUrl() {
        // 首先尝试从localStorage获取（登录时保存的）
        const savedApiUrl = localStorage.getItem('apiBaseUrl');
        if (savedApiUrl) {
            console.log('从localStorage获取API地址:', savedApiUrl);
            return savedApiUrl;
        }

        // 使用统一配置加载器
        if (window.configLoader) {
            try {
                const apiUrl = await window.configLoader.getAuthUrl(); // 这已经包含了/api/auth
                console.log('从配置加载器获取API地址:', apiUrl);
                return apiUrl;
            } catch (error) {
                console.log('配置加载器获取失败:', error.message);
            }
        }

        try {
            // 尝试从配置文件获取API地址
            const response = await fetch('/config.json');
            if (response.ok) {
                const config = await response.json();
                if (config.apiServer && config.apiServer.url) {
                    const apiUrl = config.apiServer.url + '/api/auth';
                    console.log('从配置文件获取API地址:', apiUrl);
                    return apiUrl;
                }
            }
        } catch (error) {
            console.log('无法读取配置文件，使用默认地址');
        }

        // 最后回退到默认地址（从配置加载器获取默认配置）
        if (window.configLoader) {
            try {
                const defaultConfig = window.configLoader.getDefaultConfig();
                const apiUrl = defaultConfig.apiServer.url + '/api/auth';
                console.log('使用默认配置API地址:', apiUrl);
                return apiUrl;
            } catch (error) {
                console.log('获取默认配置失败:', error.message);
            }
        }

        // 最终回退
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const apiUrl = `${protocol}//${currentHost}:5001/api/auth`;
        console.log('使用最终回退API地址:', apiUrl);
        return apiUrl;
    }

    async checkAuth() {
        console.log('检查认证状态...');

        // 如果没有token，直接跳转到登录页
        if (!this.token) {
            console.log('未找到认证令牌，立即跳转到登录页面');
            this.redirectToLogin();
            return false;
        }

        try {
            console.log('发送认证验证请求...');
            // 确保URL格式正确，避免重复路径
            let verifyUrl = `${this.apiBaseUrl}/verify`;
            if (verifyUrl.includes('/api/auth/api/auth')) {
                verifyUrl = verifyUrl.replace('/api/auth/api/auth', '/api/auth');
            }
            console.log('验证URL:', verifyUrl);

            const response = await fetch(verifyUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token: this.token })
            });

            console.log('认证验证响应状态:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('认证验证响应数据:', data);

                if (data.valid && data.user) {
                    // 保存用户信息
                    localStorage.setItem('user', JSON.stringify(data.user));
                    console.log('用户认证成功:', data.user.username);

                    // 检查是否需要修改密码
                    if (data.user.needsPasswordChange) {
                        console.log('需要修改默认密码');
                        this.showPasswordChangeModal();
                        return false;
                    }

                    return true;
                }
            }

            // Token可能无效，但不立即清除，给用户更多机会
            console.log('⚠️ 认证令牌可能无效，但保持登录状态');
            return true; // 即使服务器认为token无效，也保持用户登录状态
        } catch (error) {
            console.error('认证检查失败:', error);
            // 网络错误时不清除token，可能是临时问题
            // 只有在明确的认证错误时才清除token
            console.log('⚠️ 认证检查网络错误，保持当前登录状态');
            return true; // 网络错误时假设用户仍然有效
        }
    }

    clearAuthData() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
    }

    redirectToLogin() {
        window.location.href = '/login.html';
    }

    showPasswordChangeModal() {
        // 创建密码修改模态框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                width: 90%;
                max-width: 400px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <h2 style="margin-bottom: 20px; color: #1a237e; text-align: center;">修改密码</h2>
                <p style="margin-bottom: 20px; color: #666; text-align: center;">
                    为了您的账户安全，请修改默认密码
                </p>
                <form id="changePasswordForm">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">当前密码</label>
                        <input type="password" id="currentPassword" required style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">新密码</label>
                        <input type="password" id="newPassword" required style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            box-sizing: border-box;
                        ">
                        <small style="color: #666;">至少8位，包含大小写字母、数字和特殊字符</small>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">确认新密码</label>
                        <input type="password" id="confirmPassword" required style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div id="passwordError" style="
                        color: #f44336;
                        margin-bottom: 15px;
                        display: none;
                        text-align: center;
                    "></div>
                    <button type="submit" style="
                        width: 100%;
                        padding: 12px;
                        background: #1a237e;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 16px;
                        cursor: pointer;
                    ">修改密码</button>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 处理密码修改
        document.getElementById('changePasswordForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handlePasswordChange();
        });
    }

    async handlePasswordChange() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const errorDiv = document.getElementById('passwordError');

        // 验证输入
        if (newPassword !== confirmPassword) {
            errorDiv.textContent = '两次输入的密码不一致';
            errorDiv.style.display = 'block';
            return;
        }

        if (!this.isPasswordStrong(newPassword)) {
            errorDiv.textContent = '密码强度不足：至少8位，包含大小写字母、数字和特殊字符';
            errorDiv.style.display = 'block';
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/change-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    oldPassword: currentPassword,
                    newPassword: newPassword
                })
            });

            if (response.ok) {
                alert('密码修改成功！');
                // 移除模态框并重新加载页面
                document.querySelector('[style*="position: fixed"]').remove();
                window.location.reload();
            } else {
                const data = await response.json();
                errorDiv.textContent = data.error || '密码修改失败';
                errorDiv.style.display = 'block';
            }
        } catch (error) {
            console.error('Password change failed:', error);
            errorDiv.textContent = '网络错误，请重试';
            errorDiv.style.display = 'block';
        }
    }

    isPasswordStrong(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
    }

    // 添加登出功能
    static logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        window.location.href = '/login.html';
    }

    // 获取当前用户信息
    static getCurrentUser() {
        const userStr = localStorage.getItem('user');
        return userStr ? JSON.parse(userStr) : null;
    }

    // 检查是否为管理员
    static isAdmin() {
        const user = AuthChecker.getCurrentUser();
        return user && user.role === 'admin';
    }
}

// 全局认证检查器实例
window.authChecker = new AuthChecker();

// 立即执行认证检查（不等待 DOMContentLoaded）
(function immediateAuthCheck() {
    // 检查当前页面是否需要认证
    const currentPath = window.location.pathname;
    const isLoginPage = currentPath.includes('login.html');
    const isAdminPage = currentPath.includes('admin.html');
    const isDebugPage = currentPath.includes('index-debug.html');
    const isTestPage = currentPath.includes('test-');

    // 强制认证检查：除了登录页面和特定管理页面，所有页面都需要认证
    // 特别包括主页（根路径 "/" 或 "/index.html"）
    const needsAuth = !isLoginPage && !isAdminPage && !isDebugPage && !isTestPage;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

    if (needsAuth || isMainPage) {
        console.log('🔒 强制执行认证检查...', '当前路径:', currentPath, '是主页:', isMainPage);

        // 检查是否有 token
        const token = localStorage.getItem('authToken');
        if (!token) {
            console.log('❌ 未找到认证令牌，立即跳转到登录页面');
            // 阻止页面继续加载
            document.write(`
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
                    color: white;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    z-index: 10000;
                ">
                    <div style="text-align: center;">
                        <h2>🔒 太空物体模拟平台</h2>
                        <p>需要登录才能访问，正在跳转...</p>
                        <div style="margin: 20px 0;">
                            <div style="
                                border: 3px solid rgba(255,255,255,0.3);
                                border-top: 3px solid white;
                                border-radius: 50%;
                                width: 30px;
                                height: 30px;
                                animation: spin 1s linear infinite;
                                margin: 0 auto;
                            "></div>
                        </div>
                        <p style="font-size: 14px; opacity: 0.8;">
                            如果没有自动跳转，请<a href="/login.html" style="color: #81c784;">点击这里</a>
                        </p>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                </div>
            `);
            // 立即跳转
            window.location.href = '/login.html';
            return;
        }

        console.log('✅ 找到认证令牌，继续验证...');
    }
})();

// 页面加载时自动检查认证
document.addEventListener('DOMContentLoaded', async () => {
    // 只在非登录页面进行认证检查
    const currentPath = window.location.pathname;
    const isLoginPage = currentPath.includes('login.html');
    const isAdminPage = currentPath.includes('admin.html');
    const isDebugPage = currentPath.includes('index-debug.html');

    // 强制认证检查：主页和所有需要认证的页面
    const needsAuth = !isLoginPage && !isAdminPage && !isDebugPage;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

    if (needsAuth || isMainPage) {
        console.log('🔒 DOM加载后认证检查...', '当前路径:', currentPath, '是主页:', isMainPage);

        // 🔧 简化认证检查，只验证token存在
        const token = localStorage.getItem('authToken');
        if (!token) {
            console.log('❌ 未找到认证令牌，立即跳转到登录页面');
            window.location.replace('/login.html');
            return;
        }

        console.log('✅ 认证令牌验证通过，允许访问页面');

        // 🔧 简化后台验证，不强制登出
        setTimeout(async () => {
            try {
                const isAuthenticated = await window.authChecker.checkAuth();
                if (!isAuthenticated) {
                    console.log('⚠️ 后台认证验证失败，但不强制登出');
                    // 不强制跳转，允许用户继续使用
                } else {
                    console.log('✅ 后台认证验证成功');
                }
            } catch (error) {
                console.log('⚠️ 后台认证验证网络错误，允许离线访问:', error);
                // 网络错误时不强制登出，允许离线使用
            }
        }, 1000); // 1秒后进行后台验证
    }
});

// 导出给其他脚本使用
window.AuthChecker = AuthChecker;

// 🔒 页面加载完成后的强制认证检查
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面DOM加载完成，进行强制认证检查');

    // 检查当前页面是否需要认证
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

    if (isMainPage) {
        console.log('当前是主页，进行强制认证检查');

        const token = localStorage.getItem('authToken');
        if (!token) {
            console.log('❌ 主页访问需要认证，但未找到令牌，立即跳转到登录页面');
            window.location.replace('/login.html');
            return;
        }

        console.log('✅ 主页强制认证检查通过');
    }
});

// 🔒 页面可见性变化时的认证检查（防止长时间离开后token过期）
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('页面重新可见，进行认证检查');

        const currentPath = window.location.pathname;
        const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

        if (isMainPage) {
            const token = localStorage.getItem('authToken');
            if (!token) {
                console.log('❌ 页面可见性检查：未找到令牌');
                window.location.replace('/login.html');
                return;
            }

            console.log('✅ 页面可见性认证检查通过');
        }
    }
});

// 🔒 优化：使用页面可见性变化和用户交互来触发认证检查，而不是定时器
function performTokenCheck() {
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath === '';

    if (isMainPage) {
        console.log('触发认证检查...');

        const token = localStorage.getItem('authToken');
        if (!token) {
            console.log('❌ 认证检查：未找到令牌');
            window.location.replace('/login.html');
            return;
        }

        // 检查token是否即将过期（提前5分钟提醒）
        try {
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                const currentTime = Math.floor(Date.now() / 1000);
                const fiveMinutesFromNow = currentTime + 300; // 5分钟后

                if (payload.exp && payload.exp < currentTime) {
                    console.log('❌ 认证检查：Token已过期');
                    localStorage.removeItem('authToken');
                    window.location.replace('/login.html');
                    return;
                } else if (payload.exp && payload.exp < fiveMinutesFromNow) {
                    console.log('⚠️ 认证检查：Token将在5分钟内过期');
                    // 可以在这里添加续期逻辑或提醒用户
                }
            }
        } catch (error) {
            console.log('❌ 认证检查：Token验证失败');
            localStorage.removeItem('authToken');
            window.location.replace('/login.html');
            return;
        }

        console.log('✅ 认证检查通过');
    }
}

// 🔧 优化：在用户交互时检查认证状态
let lastAuthCheck = Date.now();
const AUTH_CHECK_INTERVAL = 300000; // 5分钟

function throttledAuthCheck() {
    const now = Date.now();
    if (now - lastAuthCheck > AUTH_CHECK_INTERVAL) {
        lastAuthCheck = now;
        performTokenCheck();
    }
}

// 🔧 移除用户交互时的认证检查，避免干扰正常使用
// 只在页面加载和可见性变化时进行必要的认证检查
