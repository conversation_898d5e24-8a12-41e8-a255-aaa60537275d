const http = require('http');

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    console.log(`正在请求: ${url}`);
    
    const req = http.get(url, (res) => {
      console.log(`收到响应: ${res.statusCode} ${res.statusMessage}`);
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', (err) => {
      console.error(`请求错误: ${err.message}`);
      console.error(`错误代码: ${err.code}`);
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      console.error('请求超时');
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

async function testAPI() {
  try {
    console.log('测试 API 连接...');
    
    // 测试健康检查
    console.log('\n1. 测试健康检查...');
    const healthResult = await makeRequest('http://localhost:3001/api/health');
    console.log('健康检查状态:', healthResult.status);
    console.log('健康检查响应:', healthResult.data);
    
    // 测试 ES 历史数据 API
    console.log('\n2. 测试 ES 历史数据 API...');
    const params = new URLSearchParams({
      norad_id: '25544',
      start: '2025-01-01',
      end: '2025-07-01'
    });
    
    const esResult = await makeRequest(`http://localhost:3001/api/es-history?${params.toString()}`);
    console.log('ES API 状态:', esResult.status);
    console.log('ES API 响应:', JSON.stringify(esResult.data, null, 2));
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

testAPI(); 