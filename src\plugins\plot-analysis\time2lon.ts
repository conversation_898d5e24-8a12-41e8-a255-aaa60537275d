import { EChartsData, GetSatType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { SatMathApi } from '@app/singletons/sat-math-api';
import waterfallPng from '@public/img/icons/waterfall.png';
import * as echarts from 'echarts';
import 'echarts-gl';
import { Degrees, DetailedSatellite, SpaceObjectType } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

export class Time2LonPlots extends KeepTrackPlugin {
  readonly id = 'Time2LonPlots';
  dependencies_: string[] = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }

  bottomIconImg = waterfallPng;
  bottomIconElementName = 'menu-time2lon-plots';
  bottomIconCallback = () => {
    if (!this.isMenuButtonActive) {
      return;
    }
    const chartDom = getEl(this.plotCanvasId)!;

    this.createPlot(Time2LonPlots.getPlotData(), chartDom);
  };

  plotCanvasId = 'plot-analysis-chart-time2lon';
  chart: echarts.ECharts;

  sideMenuElementName = 'time2lon-plots-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="time2lon-plots-menu" class="side-menu-parent start-hidden text-select" style="width: 100vw; min-width: 100vw; max-width: 100vw;">
    <!-- 右上角关闭按钮 -->
    <div id="time2lon-close-btn" style="position: fixed; top: 20px; right: 20px; z-index: 99999; width: 40px; height: 40px; background: transparent; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 28px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="plot-analysis-content" class="side-menu" style="width: 100vw; height: 100vh; padding: 20px;">
      <h5 class="center-align" style="color: white; margin-bottom: 20px;">瀑布图</h5>
      <div id="${this.plotCanvasId}" style="width: 100%; height: calc(100vh - 100px);"></div>
    </div>
  </div>`;

  addHtml(): void {
    super.addHtml();
  }

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器 - 使用事件委托确保能工作
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'time2lon-close-btn') {
        console.log('瀑布图关闭按钮被点击了！');
        e.preventDefault();
        e.stopPropagation();

        // 隐藏菜单
        const menu = document.getElementById('time2lon-plots-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
          console.log('瀑布图菜单已隐藏');
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById(this.bottomIconElementName);
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
          console.log('瀑布图底部图标选中状态已取消');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
        console.log('瀑布图插件状态已重置');
      }
    });
  }

  createPlot(data: EChartsData, chartDom: HTMLElement) {
    // Dont Load Anything if the Chart is Closed
    if (!this.isMenuButtonActive) {
      return;
    }

    // Delete any old charts and start fresh
    if (!this.chart) {
      // Setup Configuration
      this.chart = echarts.init(chartDom);
      this.chart.on('click', (event) => {
        if ((event.data as unknown as { id: number })?.id > -1) {
          this.selectSatManager_.selectSat((event.data as unknown as { id: number })?.id);
        }
      });
    }

    // Setup Chart
    this.chart.setOption({
      title: {
        text: '时间与经度图',
        textStyle: {
          fontSize: 16,
          color: '#fff',
        },
      },
      legend: {
        show: true,
        textStyle: {
          color: '#fff',
        },
      },
      tooltip: {
        formatter: (params: { value: number[]; color: string; name: string; }) => {
          const data = params.value;
          const color = params.color;
          const name = params.name;

          return `
            <div style="display: flex; flex-direction: column; align-items: flex-start;">
              <div style="display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: space-between; align-items: flex-end;">
                <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-bottom: 5px;"></div>
                <div style="font-weight: bold;"> ${name}</div>
              </div>
              <div><bold>距离此时的时间：</bold> ${data[1].toFixed(2)} 分钟</div>
              <div><bold>经度:</bold> ${data[0].toFixed(3)}°</div>
            </div>
          `;
        },
      },
      xAxis: {
        name: '经度(°)',
        type: 'value',
        position: 'bottom',
      },
      yAxis: {
        name: '当前时间(分钟)',
        type: 'value',
        position: 'left',
      },
      zAxis: {
        name: '平均运动',
        type: 'value',
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'slider',
          show: true,
          yAxisIndex: [0],
          left: '93%',
          start: 0,
          end: 1440,
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'inside',
          yAxisIndex: [0],
          start: 0,
          end: 1440,
        },
      ],
      /*
       * visualMap: [
       *   {
       *     left: 'left',
       *     top: '10%',
       *     dimension: 2,
       *     min: 0,
       *     max: 18,
       *     itemWidth: 30,
       *     itemHeight: 500,
       *     calculable: true,
       *     precision: 0.05,
       *     text: ['Mean Motion'],
       *     textGap: 30,
       *     textStyle: {
       *       color: '#fff',
       *     },
       *     inRange: {
       *       // symbolSize: [10, 70],
       *     },
       *     outOfRange: {
       *       // symbolSize: [10, 70],
       *       opacity: 0,
       *       symbol: 'none',
       *     },
       *     controller: {
       *       inRange: {
       *         color: ['#41577c'],
       *       },
       *       outOfRange: {
       *         color: ['#999'],
       *       },
       *     },
       *   },
       * ],
       */
      series: data.map((item) => ({
        type: 'line',
        name: item.country,
        data: item.data?.map((dataPoint: {
          0: number;
          1: number;
        }) => ({
          name: item.name,
          id: item.satId,
          value: [dataPoint[1], dataPoint[0]],
        })),
        /*
         * symbolSize: 8,
         * itemStyle: {
         * borderWidth: 1,
         * borderColor: 'rgba(255,255,255,0.8)',
         * },
         */
        emphasis: {
          itemStyle: {
            color: '#fff',
          },
        },
      })),
    });
  }

  static getPlotData(): EChartsData {
    const objData = keepTrackApi.getCatalogManager().objectCache;
    const timeManagerInstance = keepTrackApi.getTimeManager();

    const now = timeManagerInstance.simulationTimeObj.getTime();

    const data = [] as EChartsData;

    objData.forEach((obj) => {
      if (obj.type !== SpaceObjectType.PAYLOAD) {
        return;
      }

      let sat = obj as DetailedSatellite;

      // Taking only GEO objects
      if (sat.eccentricity > 0.1) {
        return;
      }
      if (sat.period < 1240) {
        return;
      }
      if (sat.period > 1640) {
        return;
      }

      sat = keepTrackApi.getCatalogManager().getObject(sat.id, GetSatType.POSITION_ONLY) as DetailedSatellite;
      const plotPoints = SatMathApi.getLlaOfCurrentOrbit(sat, 24);
      const plotData: [number, Degrees][] = [];

      plotPoints.forEach((point) => {
        const pointTime = (point.time - now) / 1000 / 60;

        if (pointTime > 1440 || pointTime < 0) {
          return;
        }
        plotData.push([pointTime, point.lon]);
      });
      let country = '';

      switch (sat.country) {
        case 'United States of America':
        case 'United States':
        case 'US':
        case 'USA':
          country = '美国';
          break;

        case 'France':
        case 'FR':
          country = '法国';
          break;

        case 'Russian Federation':
        case 'CIS':
        case 'RU':
        case 'SU':
        case 'Russia':
          country = '俄罗斯';
          break;

        case 'China':
        case 'China, People\'s Republic of':
        case 'Hong Kong Special Administrative Region, China':
        case 'China (Republic)':
        case 'PRC':
        case 'CN':
          country = '中国';
          break;
        case 'Japan':
        case 'JPN':
          country = '日本';
          break;
        case 'India':
        case 'IND':
          country = '印度';
          break;
        default:
          country = '其它';
          break;
      }
      data.push({
        name: sat.name,
        satId: sat.id,
        country,
        data: plotData,
      });
    });

    return data;
  }
}
