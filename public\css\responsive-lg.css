@media (min-width: 1024px) and (max-width: 1279px) {
  :root {
    --nav-bar-height: 35px;
  }
}

@media (min-width: 1024px) {
  #sensor-selected-container {
    width: 100%;
    padding: 0px 10px;
  }

  #fullscreen-icon {
    display: none;
  }

  #menu-launches,
  #menu-record,
  #menu-color-scheme,
  #social,
  #fastCompSettings {
    display: block;
  }

  .top-menu-icons>a {
    padding: 0px 10px;
  }

  .search-icon-search-on {
    transition: 1s;
  }

  .top-menu-icons img {
    width: 25px;
    height: 25px;
  }

  #search-close {
    padding: 0px 6.25%;
    font-size: 24px;
  }

  /* TODO: Account for the bottom bar being minimized */
  #search-results {
    overflow-x: hidden;
    overflow-y: auto;
    display: none;
    position: absolute;
    right: 0px;
    width: 355px;
    background: var(--color-dark-background) !important;
    z-index: 1;
    top: var(--top-menu-height);
    bottom: var(--bottom-menu-top);
    max-height: calc(100% - var(--top-menu-height) - max(var(--bottom-menu-top), var(--search-box-bottom) + var(--bottom-menu-top)) + 3px);
    border-width: 0px 0px 5px 5px;
    border-style: solid;
    border-color: var(--color-dark-border);
    border-top: 5px solid var(--color-dark-border);
    padding-top: 0px;
  }

  .share-icons {
    position: absolute;
    left: 0px;
    z-index: 1;
    width: 50px;
    height: 50px;
    padding: 9px;
    background: var(--color-dark-background) !important;
  }

  .share-up {
    transition: 1s;
    top: 0px !important;
  }

  #time-machine-menu {
    padding-left: 35px;
    color: white;
    background: var(--color-dark-background);
    top: 50px;
    right: 0px;
    z-index: 100;
    position: absolute;
    overflow: auto;
    width: 100%;
    border-width: 0px 0px 5px 0px;
    border-style: solid;
    border-color: var(--color-dark-border);
  }

  #obfit-menu {
    width: 500px;
  }

  /* ::-webkit-scrollbar {
    display: none;
  } */

  .search-slide-down {
    width: 355px;
    /* This should match the #search-results */
  }

  .search-slide-up {
    width: 0px;
  }

  #legend-hover-menu {
    top: var(--top-menu-height);
    padding: 5px;
  }

  .Square-Box {
    width: calc(25px / var(--system-scale-factor, 1)) !important;
    height: calc(25px / var(--system-scale-factor, 1)) !important;
    /* 保留原有的颜色和样式属性 */
    border-width: calc(2px / var(--system-scale-factor, 1)) !important;
    border-style: solid !important;
    border-radius: calc(12px / var(--system-scale-factor, 1)) !important;
    margin-right: calc(15px / var(--system-scale-factor, 1)) !important;
    cursor: pointer !important;
    box-shadow: 0 0px calc(4px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
                0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  }

  #sat-infobox {
    /* 🔥 移除强制位置设置，让Draggabilly控制位置 */
    margin-top: 25px;
    width: 355px;
    max-height: 60%;
  }

  .satinfo-fixed:after {
    content: '';
    height: calc(var(--bottom-menu-height) + 15px);
    display: block;
  }

  .sat-info-value {
    float: right;
    width: 220px;
    padding: 0px 25px;
    text-align: center;
  }

  .truncate-search {
    width: 200px;
  }
}