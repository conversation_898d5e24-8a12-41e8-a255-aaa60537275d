/**
 *!
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://keeptrack.space
 *
 * @Copyright (C) 2025 Kruczek Labs LLC
 *
 * KeepTrack is free software: you can redistribute it and/or modify it under the
 * terms of the GNU Affero General Public License as published by the Free Software
 * Foundation, either version 3 of the License, or (at your option) any later version.
 *
 * KeepTrack is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY;
 * without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License along with
 * KeepTrack. If not, see <http://www.gnu.org/licenses/>.
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrack } from './keeptrack';





console.log('🔍 DEBUG: main.ts 开始执行...');

// Ensure settingsOverride exists with fallback
const settingsOverride = window.settingsOverride || {
  isPreventDefaultHtml: false,
  isShowSplashScreen: true,
};

console.log('🔍 DEBUG: settingsOverride:', settingsOverride);

try {
  console.log('🔍 DEBUG: 创建 KeepTrack 实例...');
  // Load the main website class
  const keepTrack = new KeepTrack(settingsOverride);
  console.log('✅ DEBUG: KeepTrack 实例创建成功');

  console.log('🔍 DEBUG: 调用 keepTrack.init()...');
  keepTrack.init();
  console.log('✅ DEBUG: keepTrack.init() 完成');

  // Expose to window for debugging
  window.keepTrack = keepTrack;

  // Initialize the website
  KeepTrack.initCss().then(() => {
    keepTrack.run().catch((error) => {
      console.error('Error in keepTrack.run():', error);
    });
  }).catch((error) => {
    console.error('Error in KeepTrack.initCss():', error);
  });
} catch (error) {
  console.error('Error in main.ts:', error);
}