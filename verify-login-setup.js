#!/usr/bin/env node

/**
 * 登录系统配置验证脚本
 * 检查所有必要的文件和配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证登录系统配置...\n');

// 检查的文件列表
const requiredFiles = [
    // 前端文件
    'public/index.html',
    'public/login.html',
    'public/js/auth-check.js',
    'public/js/auth.js',
    'public/js/login.js',
    'public/test-auth.html',
    
    // 后端文件
    'src/auth/auth.service.ts',
    'src/api/auth.routes.ts',
    'src/api/server.ts',
    
    // 配置文件
    'test-login-system.md'
];

let allFilesExist = true;
let issues = [];

console.log('📁 检查必要文件...');
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
        issues.push(`缺少文件: ${file}`);
    }
});

console.log('\n🔧 检查主页配置...');
try {
    const indexContent = fs.readFileSync('public/index.html', 'utf8');
    
    if (indexContent.includes('auth-check.js')) {
        console.log('✅ 主页已启用认证检查');
    } else {
        console.log('❌ 主页未启用认证检查');
        issues.push('主页需要引入 auth-check.js');
    }
    
    if (indexContent.includes('不进行任何认证检查')) {
        console.log('⚠️  主页仍包含禁用认证的注释');
        issues.push('建议移除禁用认证的注释');
    }
} catch (error) {
    console.log('❌ 无法读取主页文件');
    issues.push('无法读取 public/index.html');
}

console.log('\n🔐 检查认证脚本配置...');
try {
    const authCheckContent = fs.readFileSync('public/js/auth-check.js', 'utf8');
    
    if (authCheckContent.includes('当前路径:')) {
        console.log('✅ 认证脚本已更新路径检查逻辑');
    } else {
        console.log('❌ 认证脚本路径检查逻辑需要更新');
        issues.push('认证脚本需要更新路径检查逻辑');
    }
    
    if (authCheckContent.includes('/verify')) {
        console.log('✅ 认证脚本使用正确的验证端点');
    } else {
        console.log('❌ 认证脚本验证端点配置错误');
        issues.push('认证脚本验证端点需要修正');
    }
} catch (error) {
    console.log('❌ 无法读取认证检查脚本');
    issues.push('无法读取 public/js/auth-check.js');
}

console.log('\n🗂️  检查数据目录...');
const dataDir = 'data';
if (!fs.existsSync(dataDir)) {
    console.log('ℹ️  数据目录不存在，将在首次启动时自动创建');
} else {
    console.log('✅ 数据目录已存在');
    
    const dataFiles = ['users.json', 'registrations.json', 'login-attempts.json'];
    dataFiles.forEach(file => {
        const filePath = path.join(dataDir, file);
        if (fs.existsSync(filePath)) {
            console.log(`✅ ${filePath}`);
        } else {
            console.log(`ℹ️  ${filePath} - 将在首次启动时创建`);
        }
    });
}

console.log('\n📦 检查依赖包...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    const requiredDeps = ['express', 'uuid'];
    const requiredDevDeps = ['typescript', '@types/node'];
    
    requiredDeps.forEach(dep => {
        if (dependencies[dep] || devDependencies[dep]) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - 依赖包缺失`);
            issues.push(`缺少依赖包: ${dep}`);
        }
    });
} catch (error) {
    console.log('⚠️  无法检查 package.json');
}

console.log('\n📋 验证结果总结:');
if (issues.length === 0) {
    console.log('🎉 所有检查通过！登录系统配置正确。');
    console.log('\n🚀 下一步操作:');
    console.log('1. 启动认证服务器: npm run start:api');
    console.log('2. 启动前端服务器: npm start');
    console.log('3. 访问 http://localhost:8080 测试登录功能');
    console.log('4. 使用默认账户登录: admin / SpaceDefense2025!');
} else {
    console.log('⚠️  发现以下问题需要解决:');
    issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
    });
    console.log('\n请解决这些问题后重新运行验证。');
}

console.log('\n📖 更多信息请查看: test-login-system.md');
