import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { clickAndDragWidth } from '@app/lib/click-and-drag';
import { getEl } from '@app/lib/get-el';
import { hideLoading, showLoading } from '@app/lib/showLoading';
import rocketPng from '@public/img/icons/rocket.png';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';
import { missileManager } from './missile-manager';

export class MissilePlugin extends KeepTrackPlugin {
  readonly id = 'MissilePlugin';
  dependencies_ = [];

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = rocketPng;
  sideMenuElementName: string = `${this.id}-menu`;
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="${this.id}-menu" class="side-menu-parent start-hidden text-select">
    <div id="${this.id}-content" class="side-menu">
      <div class="row">
        <h5 class="center-align">创建导弹攻击</h5>
        <form id="${this.id}-form" class="col s12">
          <div class="input-field col s12">
            <select id="ms-type">
              <option value="0">定制导弹</option>
              <option value="1">俄罗斯到美国</option>
              <option value="2">俄罗斯向美国派遣潜艇</option>
              <option value="3">中国到美国</option>
              <option value="4">朝鲜到美国</option>
              <option value="5">美国到俄罗斯</option>
              <option value="6">美国到中国</option>
              <option value="7">美国到朝鲜</option>
            </select>
            <label>攻击类型</label>
          </div>
          <div id="ms-custom-opt">
            <div class="input-field col s12">
              <select id="ms-attacker">
                <optgroup label="俄罗斯">
                  <option value="200">阿列斯克</option>
                  <option value="201">多姆巴罗夫斯基</option>
                  <option value="202">乌日尔 (Uzhur)</option>
                  <option value="203">卡尔塔雷 (Kartaly)</option>
                  <option value="204">伊尔库茨克 (Irkutsk)</option>
                  <option value="205">坎斯克 (Kansk)</option>
                  <option value="206">克拉斯诺亚尔斯克 (Krasnoyarsk)</option>
                  <option value="207">下塔吉尔 (Nizhniy Tagil)</option>
                  <option value="208">新西伯利亚 (Novosibirsk)</option>
                  <option value="209">塔季舍沃 (SS-19)</option>
                  <option value="210">塔季舍沃 (SS-27)</option>
                  <option value="211">捷伊科沃 (Teykovo)</option>
                  <option value="212">约什卡尔奥拉 (Yoshkar Ola)</option>
                  <option value="213">鲍雷级潜艇 (布拉瓦导弹)</option>
                  <option value="214">德尔塔-IV级潜艇 (Sineva)</option>
                  <option value="215">德尔塔-IV级潜艇 (Layner)</option>
                </optgroup>
                <optgroup label="中国">
                  <option value="321">092型潜艇 (巨浪-2)</option>
                  <option value="300">南阳</option>
                  <option value="301">西宁</option>
                  <option value="302">德令哈</option>
                  <option value="303">海晏</option>
                  <option value="304">大同</option>
                  <option value="305">天水</option>
                  <option value="306">西峡</option>
                  <option value="307">邵阳</option>
                  <option value="308">玉溪</option>
                  <option value="309">洛阳</option>
                  <option value="310">五寨</option>
                  <option value="311">宣化</option>
                  <option value="312">通道</option>
                  <option value="313">卢氏</option>
                  <option value="314">泾县 A</option>
                  <option value="315">泾县 B</option>
                  <option value="316">湖南</option>
                  <option value="317">大庆市</option>
                  <option value="318">信阳市</option>
                  <option value="319">新疆</option>
                  <option value="320">西藏</option>
                </optgroup>
                <optgroup label="美国">
                  <option value="101">迈诺特 (Minot)</option>
                  <option value="102">马姆斯特伦 (Malmstrom)</option>
                  <option value="103">F.E. 华伦 (F.E. Warren)</option>
                  <option value="100">俄亥俄级潜艇 (三叉戟 II)</option>
                </optgroup>
                <optgroup label="英国">
                  <option value="600">前卫级潜艇 (三叉戟 II)</option>
                  <option value="601">克莱德海军基地 (三叉戟 II)</option>
                </optgroup>
                <optgroup label="法国">
                  <option value="500">凯旋级潜艇 (M51)</option>
                  <option value="501">比斯开湾</option>
                </optgroup>
                <optgroup label="朝鲜">
                  <option value="400">新浦潜艇 (北极星-1)</option>
                  <option value="401">新浦</option>
                  <option value="402">平安道</option>
                  <option value="403">平壤</option>
                </optgroup>
                </select>
                <label>发射地点</label>
                </div>
                <div id="ms-lau-holder-lat" class="input-field col s12">
                  <input placeholder="00.000" id="ms-lat-lau" type="text" maxlength="8" />
                  <label for="ms-lat-lau" class="active">自定义发射纬度</label>
                </div>
                <div id="ms-lau-holder-lon" class="input-field col s12">
                  <input placeholder="00.000" id="ms-lon-lau" type="text" maxlength="8" />
                  <label for="ms-lon-lau" class="active">自定义发射经度</label>
                </div>
                <div class="input-field col s12">
                  <select id="ms-target">
                    <optgroup label="美国">
                      <option value="0">华盛顿特区 (Washington DC)</option>
                      <option value="1">纽约市 (New York City)</option>
                      <option value="2">洛杉矶 (Los Angeles)</option>
                      <option value="3">芝加哥 (Chicago)</option>
                      <option value="4">波士顿 (Boston)</option>
                      <option value="5">西雅图 (Seattle)</option>
                      <option value="6">迈阿密 (Miami)</option>
                      <option value="7">达拉斯 (Dallas)</option>
                      <option value="8">科罗拉多斯普林斯 (Colorado Springs)</option>
                      <option value="9">奥马哈 (Omaha)</option>
                      <option value="10">夏威夷 (Hawaii)</option>
                      <option value="11">关岛 (Guam)</option>
                    </optgroup>
                    <option value="-1">自定义目标点 (Custom Impact)</option>
                    <optgroup label="北约国家">
                      <option value="12">伦敦 (London)</option>
                      <option value="13">巴黎 (Paris)</option>
                      <option value="14">法属加勒比地区 (French Caribbean)</option>
                      <option value="15">马德里 (Madrid)</option>
                      <option value="16">罗马 (Rome)</option>
                      <option value="17">柏林 (Berlin)</option>
                      <option value="18">多伦多 (Toronto)</option>
                    </optgroup>
                    <optgroup label="非北约国家">
                      <option value="19">莫斯科 (Moscow)</option>
                      <option value="20">圣彼得堡 (St. Petersburg)</option>
                      <option value="21">新西伯利亚 (Novosibirsk)</option>
                      <option value="22">北京 (Beijing)</option>
                      <option value="23">平壤 (Pyongyang)</option>
                    </optgroup>
              </select>
              <label>目标位置</label>
            </div>
            <div id="ms-tgt-holder-lat" class="input-field col s12">
              <input placeholder="00.000" id="ms-lat" type="text" maxlength="8" />
              <label for="ms-lat" class="active">自定义目标纬度</label>
            </div>
            <div id="ms-tgt-holder-lon" class="input-field col s12">
              <input placeholder="00.000" id="ms-lon" type="text" maxlength="8" />
              <label for="ms-lon" class="active">自定义目标经度</label>
            </div>
          </div>
          <div class="center-align">
            <button class="btn btn-ui waves-effect waves-light" type="submit" name="action">发射导弹攻击 &#9658;</button>
          </div>
        </form>
        <div class="row"></div>
        <div class="center-align">
          <button id="searchRvBtn" class="btn btn-ui waves-effect waves-light" name="search">显示所有导弹 &#9658;</button>
        </div>
      </div>
      <div id="ms-error" class="center-align menu-selectable start-hidden">
        <h6 class="center-align">错误</h6>
      </div>
    </div>
  </div>
  `;

  dragOptions: ClickDragOptions = {
    isDraggable: true,
  };
  /** Is a submarine selected */
  private isSub_: boolean = false;
  private i_: number;

  addHtml(): void {
    super.addHtml();
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, this.uiManagerFinal_.bind(this));
  }

  addJs(): void {
    super.addJs();

    // Missile orbits have to be updated every draw or they quickly become inaccurate
    keepTrackApi.on(KeepTrackApiEvents.updateLoop, this.updateLoop_.bind(this));
  }

  private searchForRvs_() {
    const uiManagerInstance = keepTrackApi.getUiManager();

    uiManagerInstance.doSearch('RV_');
  }

  private missileSubmit_(): void {
    // eslint-disable-next-line max-statements
    showLoading(() => {
      const timeManagerInstance = keepTrackApi.getTimeManager();
      const uiManagerInstance = keepTrackApi.getUiManager();

      getEl('ms-error')!.style.display = 'none';
      const type = parseFloat((<HTMLInputElement>getEl('ms-type')).value);
      const attacker = parseFloat((<HTMLInputElement>getEl('ms-attacker')).value);
      let lauLat = parseFloat((<HTMLInputElement>getEl('ms-lat-lau')).value);
      let lauLon = parseFloat((<HTMLInputElement>getEl('ms-lon-lau')).value);
      const target = parseFloat((<HTMLInputElement>getEl('ms-target')).value);
      let tgtLat = parseFloat((<HTMLInputElement>getEl('ms-lat')).value);
      let tgtLon = parseFloat((<HTMLInputElement>getEl('ms-lon')).value);
      const launchTime = timeManagerInstance.selectedDate.getTime();

      let sim = '';

      if (type === 1) {
        sim = 'simulation/Russia2USA.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 2) {
        sim = 'simulation/Russia2USAalt.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 3) {
        sim = 'simulation/China2USA.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 4) {
        sim = 'simulation/NorthKorea2USA.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 5) {
        sim = 'simulation/USA2Russia.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 6) {
        sim = 'simulation/USA2China.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type === 7) {
        sim = 'simulation/USA2NorthKorea.json';
        missileManager.massRaidPre(launchTime, sim);
      }
      if (type !== 0) {
        uiManagerInstance.toast(`${sim} 已加载`, ToastMsgType.standby, true);
      }
      if (type === 0) {
        if (target === -1) {
          // Custom Target
          if (isNaN(tgtLat)) {
            uiManagerInstance.toast('目标纬度无效！', ToastMsgType.critical);
            hideLoading();

            return;
          }
          if (isNaN(tgtLon)) {
            uiManagerInstance.toast('目标经度无效！', ToastMsgType.critical);
            hideLoading();

            return;
          }
        } else {
          // Premade Target
          tgtLat = <number>missileManager.globalBMTargets[target * 3];
          tgtLon = <number>missileManager.globalBMTargets[target * 3 + 1];
        }

        if (this.isSub_) {
          if (isNaN(lauLat)) {
            uiManagerInstance.toast('发射纬度无效！', ToastMsgType.critical);
            hideLoading();

            return;
          }
          if (isNaN(lauLon)) {
            uiManagerInstance.toast('发射经度无效！', ToastMsgType.critical);
            hideLoading();

            return;
          }
        }

        let a: number;
        let b: number;
        const catalogManagerInstance = keepTrackApi.getCatalogManager();

        if (attacker < 200) {
          // USA
          a = attacker - 100;
          b = 500 - missileManager.missilesInUse;
          let missileMinAlt = 1200;

          if (attacker !== 100) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.UsaICBM[a * 4];
            lauLon = <number>missileManager.UsaICBM[a * 4 + 1];
            missileMinAlt = 1100; // https://www.space.com/8689-air-force-launches-ballistic-missile-suborbital-test.html
          }
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.UsaICBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.UsaICBM[a * 4 + 3],
            'United States',
            missileMinAlt,
          );
        } else if (attacker < 300) {
          // Russian
          a = attacker - 200;
          b = 500 - missileManager.missilesInUse;
          const missileMinAlt = 1120;

          if (attacker !== 213 && attacker !== 214 && attacker !== 215) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.RussianICBM[a * 4];
            lauLon = <number>missileManager.RussianICBM[a * 4 + 1];
          }
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.RussianICBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.RussianICBM[a * 4 + 3],
            'Russia',
            missileMinAlt,
          );
        } else if (attacker < 400) {
          // Chinese
          a = attacker - 300;
          b = 500 - missileManager.missilesInUse;
          const missileMinAlt = 1120;

          if (attacker !== 321) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.ChinaICBM[a * 4];
            lauLon = <number>missileManager.ChinaICBM[a * 4 + 1];
          }
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.ChinaICBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.ChinaICBM[a * 4 + 3],
            'China',
            missileMinAlt,
          );
        } else if (attacker < 500) {
          // North Korean
          a = attacker - 400;
          b = 500 - missileManager.missilesInUse;
          const missileMinAlt = 1120;

          if (attacker !== 400) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.NorthKoreanBM[a * 4];
            lauLon = <number>missileManager.NorthKoreanBM[a * 4 + 1];
          }
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.NorthKoreanBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.NorthKoreanBM[a * 4 + 3],
            'North Korea',
            missileMinAlt,
          );
        } else if (attacker < 600) {
          // French SLBM
          a = attacker - 500;
          b = 500 - missileManager.missilesInUse;
          const missileMinAlt = 1000;

          if (attacker !== 500) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.FraSLBM[a * 4];
            lauLon = <number>missileManager.FraSLBM[a * 4 + 1];
          }
          // https://etikkradet.no/files/2017/02/EADS-Engelsk.pdf
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.FraSLBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.FraSLBM[a * 4 + 3],
            'France',
            missileMinAlt,
          );
        } else if (attacker < 700) {
          // United Kingdom SLBM
          a = attacker - 600;
          b = 500 - missileManager.missilesInUse;
          const missileMinAlt = 1200;

          if (attacker !== 600) {
            // Use Custom Launch Site
            lauLat = <number>missileManager.ukSLBM[a * 4];
            lauLon = <number>missileManager.ukSLBM[a * 4 + 1];
          }
          missileManager.createMissile(
            lauLat,
            lauLon,
            tgtLat,
            tgtLon,
            3,
            catalogManagerInstance.missileSats - b,
            launchTime,
            missileManager.ukSLBM[a * 4 + 2] as string,
            30,
            2.9,
            0.07,
            <number>missileManager.ukSLBM[a * 4 + 3],
            'United Kigndom',
            missileMinAlt,
          );
        }
        uiManagerInstance.toast(missileManager.lastMissileError, missileManager.lastMissileErrorType);
        uiManagerInstance.doSearch('RV_');
      }
      hideLoading();
    });
  }

  private uiManagerFinal_(): void {
    clickAndDragWidth(getEl(`${this.id}-menu`));
    getEl(`${this.id}-form`)!.addEventListener('submit', (e: Event): void => {
      e.preventDefault();
      this.missileSubmit_();
    });
    getEl('ms-attacker')!.addEventListener('change', this.msAttackerChange_);
    getEl('ms-target')!.addEventListener('change', this.msTargetChange_);
    getEl('ms-error')!.addEventListener('click', this.msErrorClick_);
    getEl(`${this.id}-form`)!.addEventListener('change', this.missileChange_);
    getEl('searchRvBtn')!.addEventListener('click', this.searchForRvs_);

    this.msAttackerChange_();
    this.msTargetChange_();
  }

  private updateLoop_(): void {
    if (typeof missileManager !== 'undefined' && missileManager.missileArray.length > 0) {
      const orbitManagerInstance = keepTrackApi.getOrbitManager();

      for (this.i_ = 0; this.i_ < missileManager.missileArray.length; this.i_++) {
        orbitManagerInstance.updateOrbitBuffer(missileManager.missileArray[this.i_].id);
      }
    }
  }

  private missileChange_(): void {
    if (parseFloat((<HTMLInputElement>getEl('ms-type')).value) !== 0) {
      getEl('ms-custom-opt')!.style.display = 'none';
    } else {
      getEl('ms-custom-opt')!.style.display = 'block';
    }
  }
  private msErrorClick_(): void {
    getEl('ms-error')!.style.display = 'none';
  }
  private msTargetChange_() {
    if (parseInt((<HTMLInputElement>getEl('ms-target')).value) !== -1) {
      getEl('ms-tgt-holder-lat')!.style.display = 'none';
      getEl('ms-tgt-holder-lon')!.style.display = 'none';
    } else {
      getEl('ms-tgt-holder-lat')!.style.display = 'block';
      getEl('ms-tgt-holder-lon')!.style.display = 'block';
    }
  }

  private msAttackerChange_() {
    this.isSub_ = false;
    const subList = [100, 600, 213, 214, 215, 321, 500, 400];

    for (const sub of subList) {
      if (sub === parseInt((<HTMLInputElement>getEl('ms-attacker')).value)) {
        this.isSub_ = true;
      }
    }
    if (!this.isSub_) {
      getEl('ms-lau-holder-lat')!.style.display = 'none';
      getEl('ms-lau-holder-lon')!.style.display = 'none';
    } else {
      getEl('ms-lau-holder-lat')!.style.display = 'block';
      getEl('ms-lau-holder-lon')!.style.display = 'block';
    }
  }
}
