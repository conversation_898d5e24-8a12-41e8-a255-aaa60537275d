/**
 * 测试GEO卫星经度历史API
 */

const fetch = require('node-fetch');

async function testGeoLongitudeAPI() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('开始测试GEO卫星经度历史API...\n');
  
  // 测试1: 查询所有GEO卫星
  console.log('测试1: 查询所有GEO卫星经度数据');
  try {
    const response1 = await fetch(`${baseUrl}/api/es-history?start=2025-01-01&end=2025-01-02&geo_only=true`);
    const data1 = await response1.json();
    
    if (data1.data && data1.data.length > 0) {
      console.log(`✓ 成功获取 ${data1.count} 条记录`);
      // 过滤GEO卫星数据
      const geoData = data1.data.filter(record => record.object_type === 'GEO' && record.subsat_long);
      console.log(`✓ 其中GEO卫星记录: ${geoData.length} 条`);
      if (geoData.length > 0) {
        console.log(`✓ 示例数据: NORAD ${geoData[0].norad_id}, 经度: ${geoData[0].subsat_long}°`);
      }
    } else {
      console.log(`✗ 查询失败: ${data1.error || '无数据'}`);
    }
  } catch (error) {
    console.log(`✗ 请求失败: ${error.message}`);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试2: 查询特定卫星
  console.log('测试2: 查询特定NORAD ID的GEO卫星');
  try {
    const response2 = await fetch(`${baseUrl}/api/es-history?norad_id=25544&start=2025-01-01&end=2025-01-02&geo_only=true`);
    const data2 = await response2.json();
    
    if (data2.data && data2.data.length > 0) {
      console.log(`✓ 成功获取 ${data2.count} 条记录`);
      // 检查是否有GEO卫星数据
      const geoData = data2.data.filter(record => record.object_type === 'GEO' && record.subsat_long);
      if (geoData.length > 0) {
        console.log(`✓ 卫星名称: ${geoData[0].object_name}`);
        console.log(`✓ 经度值: ${geoData[0].subsat_long}°`);
      } else {
        console.log('! 该卫星可能不是GEO卫星或在指定时间范围内无经度数据');
      }
    } else {
      console.log(`✗ 查询失败: ${data2.error || '无数据'}`);
    }
  } catch (error) {
    console.log(`✗ 请求失败: ${error.message}`);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试3: 测试错误处理
  console.log('测试3: 测试错误处理（缺少参数）');
  try {
    const response3 = await fetch(`${baseUrl}/api/es-history?start=2025-01-01`);
    const data3 = await response3.json();
    
    if (data3.error && data3.error.includes('缺少')) {
      console.log('✓ 错误处理正常');
    } else {
      console.log('✗ 错误处理异常');
    }
  } catch (error) {
    console.log(`✗ 请求失败: ${error.message}`);
  }
  
  console.log('\n测试完成！');
}

// 运行测试
if (require.main === module) {
  testGeoLongitudeAPI().catch(console.error);
}

module.exports = { testGeoLongitudeAPI };
